#!/bin/bash

echo "🚀 Iniciando atualização dos microfronts..."

for dir in iterpec-booking-*; do
  if [ -d "$dir" ]; then
    echo ""
    echo "📁 Entrando em: $dir"
    cd "$dir" || { echo "❌ Falha ao entrar em $dir"; continue; }

    echo "🔀 Trocando para a branch main..."
    git checkout main || { echo "❌ Falha ao trocar para a branch main em $dir"; cd ..; continue; }

    echo "⬇️  Dando git pull..."
    git pull || echo "⚠️  Falha ao dar git pull em $dir"

    cd ..
    echo "✅ Finalizado: $dir"
  fi
done

echo ""
echo "🎉 Atualização concluída!"