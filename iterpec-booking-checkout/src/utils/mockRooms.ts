//@ts-ignore
import { Types } from "@iterpec/booking-utility";

// Função para gerar um nome aleatório
const getRandomName = () => {
  const firstNames = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
  ];

  const lastNames = [
    "Silva",
    "Santos",
    "Oliveira",
    "Piva",
    "Amaral",
    "Lal<PERSON>",
    "Costa",
    "Souza",
    "Almeida",
    "Rod<PERSON><PERSON>",
    "<PERSON><PERSON>ira",
    "<PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "Teixeira",
  ];

  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
  const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

  return { firstName, lastName };
};

// Função para gerar uma data de nascimento aleatória como objeto Date
const getRandomBirthday = (minAge: number, maxAge: number): Date => {
  const today = new Date();
  const minDate = new Date(
    today.getFullYear() - maxAge,
    today.getMonth(),
    today.getDate()
  );
  const maxDate = new Date(
    today.getFullYear() - minAge,
    today.getMonth(),
    today.getDate()
  );

  return new Date(
    minDate.getTime() + Math.random() * (maxDate.getTime() - minDate.getTime())
  );
};

// Função para gerar dados mockados para rooms
export const generateMockRooms = (roomCount: number): Types["IGuest"][] => {
  return Array.from({ length: roomCount }, (_, roomIndex) => {
    const mainGuest = getRandomName();
    // Não gera guests aqui, guests devem ser criados conforme adultos/crianças no componente
    return {
      firstName: mainGuest.firstName,
      familyName: mainGuest.lastName,
      birthday: getRandomBirthday(18, 60), // Adultos
      phone: `55${Math.floor(1000000000 + Math.random() * 9000000000)}`,
      email: `${mainGuest.firstName.toLowerCase()}.${mainGuest.lastName.toLowerCase()}@example.com`,
      guest: [],
    };
  });
};

// Função para gerar um guest adulto
export const generateMockAdult = () => {
  const guest = getRandomName();
  return {
    firstName: guest.firstName,
    familyName: guest.lastName,
    birthday: getRandomBirthday(18, 60),
    main: false,
  };
};

// Função para gerar um guest criança
export const generateMockChild = () => {
  const guest = getRandomName();
  return {
    firstName: guest.firstName,
    familyName: guest.lastName,
    birthday: getRandomBirthday(0, 17),
    main: false,
  };
};

const getRandomCity = () => {
  const cities = [
    "São Paulo",
    "Rio de Janeiro",
    "Belo Horizonte",
    "Curitiba",
    "Porto Alegre",
    "Salvador",
    "Fortaleza",
    "Brasília",
  ];
  return cities[Math.floor(Math.random() * cities.length)];
};

const getRandomStreet = () => {
  const streets = [
    "Avenida Paulista",
    "Rua das Flores",
    "Praça da Sé",
    "Alameda Santos",
    "Rua Augusta",
    "Estrada do Sol",
    "Travessa das Palmeiras",
    "Rua XV de Novembro",
  ];
  return streets[Math.floor(Math.random() * streets.length)];
};

// Função para gerar dados mockados para o objeto billingContact
export const generateMockBillingContact =
  (): Types["ISearch"]["payments"][0] => {
    const { firstName, lastName } = getRandomName();

    return {
      given_name: firstName,
      family_name: lastName,
      document:
        Math.random() > 0.5
          ? `02291433008` // CPF aleatório
          : `26348366006`, // CNPJ aleatório
      address: {
        line_1: `${getRandomStreet()}, ${Math.floor(1 + Math.random() * 1000)}`,
        line_2: `Apto ${Math.floor(1 + Math.random() * 1000)}`,
        line_3: `Bairro ${lastName}`,
        city: getRandomCity(),
        state_province_code: "SP",
        postal_code: `${Math.floor(10000 + Math.random() * 90000)}-${Math.floor(
          100 + Math.random() * 900
        )}`,
        country_code: "BR",
      },
    };
  };
