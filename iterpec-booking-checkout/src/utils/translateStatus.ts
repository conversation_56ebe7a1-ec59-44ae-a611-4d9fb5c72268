// utils/translateStatus.ts
import { TFunction } from "i18next";

export function translateStatus(status: string, t: TFunction) {
  switch (status?.toUpperCase()) {
    case "CANCELED":
    case "CANCELLED":
      return t("cardTrip.status.cancelled");
    case "COMPLETED":
      return t("cardTrip.status.completed");
    case "ERROR":
      return t("cardTrip.status.error");
    case "HOLD":
      return t("cardTrip.status.hold");
    default:
      return status;
  }
}
