// Sistema de eventos customizado para gerenciar comunicação entre componentes
class EventEmitter {
  private events: { [key: string]: Function[] } = {};

  // Adiciona um listener para um evento específico
  on(event: string, callback: Function) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  // Remove um listener de um evento específico
  off(event: string, callback: Function) {
    if (!this.events[event]) return;

    this.events[event] = this.events[event].filter((cb) => cb !== callback);
  }

  // Emite um evento com dados
  emit(event: string, data?: any) {
    if (!this.events[event]) return;

    this.events[event].forEach((callback) => {
      callback(data);
    });
  }

  // Remove todos os listeners de um evento específico
  removeAllListeners(event: string) {
    if (this.events[event]) {
      delete this.events[event];
    }
  }
}

// Instância singleton do EventEmitter
export const eventEmitter = new EventEmitter();

// Tipos de eventos disponíveis
export const EVENT_TYPES = {
  WEBSOCKET_VALIDATION_ERROR: "VALIDATION_ERROR",
  STATIC_DATA_INTEGRATION_ERROR: "STATIC_DATA_INTEGRATION_ERROR",
  WEBSOCKET_BOOKING_SUCCESS: "websocket_booking_success",
  WEBSOCKET_BOOKING_ERROR: "websocket_booking_error",
} as const;

// Tipos TypeScript para os eventos
export interface ValidationError {
  index: number;
  error: Array<{
    descricao: string;
    inputName: string;
  }>;
}

export interface WebSocketValidationErrorData {
  message: string;
  errorStatus: string;
  statusCode: number;
  extra?: {
    validationErrors: ValidationError[];
  };
}

export interface WebSocketBookingSuccessData {
  status: "PAID" | "PIX_PENDING";
  emv?: string;
  location?: {
    url: string;
  };
}

export interface WebSocketBookingErrorData {
  message: string;
  errorStatus: string;
  statusCode: number;
  extra?: any;
}
