// Exporta o sistema de eventos para uso em outros componentes
export { eventEmitter, EVENT_TYPES } from "./eventEmitter";
export type {
  ValidationError,
  WebSocketBookingSuccessData,
  WebSocketBookingErrorData,
} from "./eventEmitter";

// Exporta hooks para facilitar o uso do sistema de eventos
export {
  useWebSocketEvents,
  useBookingSuccessListener,
  useBookingErrorListener,
} from "../hooks/useWebSocketEvents";

export const getStatus = (status: number | string) => {
  switch (status) {
    case 0:
      return "Procesando";
    case 1:
      return "Procesando";
    case 2:
      return "Reservado";
    case 3:
      return "Cancelada";

    default:
      return "";
  }
};

class Telefone {
  ddi: string;
  ddd: string;
  numero: string;

  constructor(ddi: string, ddd: string, numero: string) {
    this.ddi = ddi;
    this.ddd = ddd;
    this.numero = numero;
  }
}
export function separarTelefone(telefoneCompleto: string): Telefone {
  const ddi = telefoneCompleto.slice(
    0,
    telefoneCompleto.startsWith("+") ? 3 : 2
  );
  const ddd = telefoneCompleto.slice(ddi.length, ddi.length + 2);
  const numero = telefoneCompleto.slice(ddi.length + 2);

  return new Telefone(ddi.replace("+", ""), ddd, numero);
}

export type WebSocketValidationErrorData = {
  extra?: {
    validationErrors?: Array<any>;
  };
  error?: {
    extra?: {
      validationErrors?: Array<any>;
    };
  };
  // ...outros campos se necessário
};

import { useEffect } from "react";
import { EVENT_TYPES, eventEmitter } from "./eventEmitter";

export function useValidationErrorListener(callback) {
  useEffect(() => { 
    const handler = (data) => { 
      callback(data);
    };
    eventEmitter.on(EVENT_TYPES.WEBSOCKET_VALIDATION_ERROR, handler);
    return () => {
      eventEmitter.off(EVENT_TYPES.WEBSOCKET_VALIDATION_ERROR, handler);
    };
  }, [callback]);
}
