import { IconsValuesEnum } from "@iterpecdev/icons-system";

type IconMatch = {
  icon: IconsValuesEnum;
  keywords: string[];
};

const iconMatches: IconMatch[] = [
  {
    icon: "Wifi",
    keywords: ["wifi", "internet", "rede", "network", "red"],
  },
  {
    icon: "WeightLifter",
    keywords: ["fitness", "academia", "ginasio", "gym", "gimnasio"],
  },
  {
    icon: "Car",
    keywords: ["parking", "estacionamento", "parqueo"],
  },
  {
    icon: "Paw",
    keywords: ["pet", "pets", "animal", "mascota", "mascote"],
  },
  {
    icon: "Pool",
    keywords: ["water", "agua", "acuatico", "aquatico"],
  },
  {
    icon: "SnowflakeVariant",
    keywords: [
      "ac",
      "air conditioning",
      "condicionado",
      "ar-condicionado",
      "aire-acondicionado",
    ],
  },
  {
    icon: "Spa",
    keywords: ["spa"],
  },
  {
    icon: "Swim",
    keywords: ["pool", "piscina", "alberca"],
  },
  {
    icon: "WashingMachine",
    keywords: ["lavanderia", "laundry", "lavado"],
  },
  {
    icon: "Coffee",
    keywords: [
      "desayuno",
      "breakfast",
      "café",
      "coffee",
      "café da manhã",
      "cafe da manha",
      "incluído",
    ],
  },
  {
    icon: "Infinity",
    keywords: ["todo incluído", "all inclusive"],
  },
];

export const iconName = (inputName: string): IconsValuesEnum => {
  const normalized = inputName
    .toLowerCase()
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "");

  const scores = iconMatches.map(({ icon, keywords }) => {
    const score = keywords.reduce((acc, word) => {
      return acc + (normalized.includes(word) ? 1 : 0);
    }, 0);
    return { icon, score };
  });

  const bestMatch = scores
    .filter((s) => s.score > 0)
    .sort((a, b) => b.score - a.score)[0];

  return bestMatch?.icon ?? null;
};
