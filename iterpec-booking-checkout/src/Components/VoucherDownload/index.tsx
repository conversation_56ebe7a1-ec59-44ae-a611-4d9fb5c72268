import { Button } from "@iterpecdev/design-sistem";
import { usePDF } from "react-to-pdf";
import { useState, useEffect, useRef, useMemo } from "react";
import BookingConfirmation from "../Voucher";
//@ts-ignore
import { Utils } from "@iterpec/booking-utility";
import { Container } from "./styles";

export const BookingConfirmationPDF = (props) => {
  const contentRef = useRef(null);
  const imageRef = useRef(null);
  const [dimensions, setDimensions] = useState(null);
  const [readyToGenerate, setReadyToGenerate] = useState(false);
  const [imageBase64, setImageBase64] = useState(null);
  const pxToMm = (px) => px * 0.264583;
  const { cachedImage } = Utils;

  const { toPDF, targetRef } = usePDF({
    filename: `${props.booking.retrive.bookingId}.pdf`,
    page: dimensions
      ? {
          orientation: "portrait",
          format: [pxToMm(dimensions.width), pxToMm(dimensions.height)],
        }
      : undefined,
  });

  // Converte a imagem do DOM para base64 quando ela é carregada
  const convertImageToBase64 = () => {
    if (!imageRef.current) return null;

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    canvas.width = imageRef.current.width;
    canvas.height = imageRef.current.height;

    ctx.drawImage(imageRef.current, 0, 0);

    return canvas.toDataURL("image/png"); // Pode mudar para "image/jpeg"
  };

  // Aguarda a imagem carregar antes de iniciar a conversão
  const handleImageLoad = () => {
    const base64Img = convertImageToBase64();
    setImageBase64(base64Img);
  };

  const handleDownload = () => {
    if (contentRef.current) {
      const { offsetWidth, offsetHeight } = contentRef.current;
      setDimensions({ width: offsetWidth, height: offsetHeight });

      setReadyToGenerate(true);
    }
  };

  useEffect(() => {
    if (readyToGenerate && dimensions) {
      const timer = setTimeout(() => {
        toPDF();
        setReadyToGenerate(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [readyToGenerate, dimensions, toPDF]);
  const imageUrl = useMemo(() => {
    const imageUrl = props?.booking.static?.images?.find(
      (image) => image.heroImage
    )?.links["1000px"].href;
    return imageUrl;
  }, [props.booking.images]);

  return (
    <>
      <Button variant="link" onClick={handleDownload}>
        Baixar PDF
      </Button>

      <Container>
        {/* 🔹 Imagem é carregada na tela para ser convertida */}
        <img
          ref={imageRef}
          id="hotelImage"
          src={cachedImage(imageUrl)}
          alt="Imagem do Hotel"
          style={{ display: "none", width: "100%", height: "100%" }} // Esconde a imagem visualmente, mas mantém carregada
          onLoad={handleImageLoad} // Aguarda o carregamento completo antes da conversão
          crossOrigin="anonymous"
        />

        <div
          ref={(el) => {
            contentRef.current = el;
            targetRef.current = el;
          }}
        >
          <BookingConfirmation
            booking={{ ...props.booking, imageBase64 }}
            imagem={cachedImage(imageUrl)}
          />
        </div>
      </Container>
    </>
  );
};
