import Icon from "@iterpecdev/icons-system";
import * as Styles from "./styles";
import { useEffect, useState } from "react";

const Stars = (props) => {
  const [stars, setStars] = useState<{
    full: number[];
    empty: number[];
    half: boolean;
  }>();

  const HandleStars = () => {
    let full = [];
    let half = !Number.isInteger(Number(props.stars));
    let empty = [];
    for (let i = 1; i <= Math.floor(Number(props.stars)); i++) {
      full.push(i);
    }
    for (let i = full.length + (!half ? 1 : 2); i <= 5; i++) {
      empty.push(i);
    }
    setStars({
      full,
      empty,
      half,
    });
  };
  useEffect(() => {
    HandleStars();
  }, []);
  useEffect(() => {
    HandleStars();
  }, [props]);
  return (
    <Styles.WrapperStar {...props} className="WrapperStar">
      {stars?.full.map((star) => (
        <Icon name="Star" key={star} className="full" />
      ))}
      {stars?.half ? <Icon name="StarHalfFull" className="half" /> : null}
      {stars?.empty.map((star) => (
        <Icon name="Star" key={star} className="empty" />
      ))}
    </Styles.WrapperStar>
  );
};
export default Stars;
