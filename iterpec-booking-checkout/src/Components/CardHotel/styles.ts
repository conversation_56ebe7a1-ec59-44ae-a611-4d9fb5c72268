import styled, { css } from "styled-components";

export const WrapperCardHotel = styled.div`
  ${({ theme }) => css`
    & > div.card {
      position: relative;
      & > div.WrapperImage {
        width: calc(100% + 32px);
        height: calc(100% + 16px);
        margin-left: -16px;
        margin-top: -8px;
        position: relative;
        & > h3 {
          font-size: 0.9em;
          color: #fff;
          -webkit-tap-highlight-color: rgba(0, 135, 245, 0);
          position: absolute;
          bottom: 1rem;
          left: 1rem;
          z-index: 14;
          & > div > svg {
            width: 20px;
          }
        }
      }
      & > div > div.shadow {
        content: "\A";
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        -webkit-border-radius: 0.17647059em;
        -moz-border-radius: 0.17647059em;
        -o-border-radius: 0.17647059em;
        border-radius: 0.17647059em;
        z-index: 13;
      }
      display: flex;
      flex-direction: column;
      gap: 12px;
      width: 100%;
      background-color: #fff;

      & > div.carrouselWrapper {
        display: block;
        margin-left: -16px;
        width: 109.5%;
        height: auto;
        max-height: 35vh;
      }
      & > h4 {
        color: #000;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 137.5% */
      }
      & > img {
        margin-left: -16px;
        width: 109.5%;
        object-fit: cover;
      }
      & div > img {
        width: calc(100% + 32px);
        height: calc(100% + 16px);
        margin-left: -16px;
        margin-top: -8px;
        object-fit: cover;
        max-height: 280px;
      }

      & > div.wrapperScoreAmenities {
        display: flex;
        gap: 8px;
        & > button {
          border-radius: 8px;
          border: 1px solid #a4ce4a;
          background: #a4ce4a;
        }
        & > svg,
        & > div > svg {
          width: 20px;
          height: auto;
        }
      }
      & > div.wrapperTypeStars {
        display: flex;
        gap: 8px;
        & > button {
          border-radius: 8px;
          border: 1px solid #212843;
          background: #212843;
        }
        & > div.wrapperStars {
          & > div > svg {
            width: 24px;
          }
        }
      }
      div.wrapperButtons {
        display: flex;
        justify-content: space-between;
      }
      div.wrapperContent {
        display: flex;
        flex-direction: column;
        gap: 7px;
        & > p {
          font-size: 0.8rem;
          &.location {
            display: flex;
            align-items: center;
            justify-content: start;
            gap: 2px;
          }
          & > svg {
            height: 18px;
            fill: #878787;
          }
          & > b {
            width: max-content;
            height: min-content;
            white-space: nowrap;
          }
          & > span,
          > h4 {
            color: #878787;
            width: fit-content;
            font-size: 0.8rem;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            text-align: justify;
            &.refundable {
              color: #a4ce4a;
            }
            &.noRefundable {
              color: #d35454;
            }
          }
        }
        & > div.roomPrice {
          display: flex;
          gap: 7px;
          & > h2 {
            color: #000;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 110% */
          }
          & > h2.oldPrice {
            color: rgba(255, 0, 0, 0.3);
            text-decoration: line-through;
          }
        }
        & > div.roomName {
          display: flex;
          gap: 7px;
          flex-direction: column;
          & > h3 {
            font-size: 1.1rem;
          }
          & > span {
            color: #878787;
            font-family: Inter;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
          }
          > * {
            width: 100%;
          }
        }
      }
    }
  `}
`;

export const SkeletonWrapperCardHotel = styled.div`
  ${({ theme }) => css`
    margin-top: 10px;
    & > div.card {
      display: flex;
      flex-direction: column;
      gap: 12px;
      width: 100%;
      * {
        border-radius: 8px;
      }
      & > span {
        margin-left: -16px;
        width: 110%;
        height: 181px;
      }
      & > div.wrapperScoreAmenities {
        display: flex;
        gap: 8px;
        & > span {
          border-radius: 8px;
        }
      }
      & > div.wrapperTypeStars {
        display: flex;
        gap: 8px;
        & > span {
          border-radius: 8px;
          width: 59px;
          height: 24px;
        }
        & > div.wrapperStars {
          & > span {
            width: 120px;
            height: 24px;
          }
        }
      }
      div.wrapperButtons {
        display: flex;
        justify-content: space-between;
      }
      div.wrapperContent {
        display: flex;
        flex-direction: column;
        gap: 7px;
        & > span {
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          &.refundable {
          }
        }
        & > div.roomPrice {
          display: flex;
          gap: 7px;
          & > span {
            color: #000;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 110% */
          }
          & > h2.oldPrice {
            text-decoration: line-through;
          }
        }
        & > div.roomName {
          display: flex;
          gap: 7px;
          flex-direction: column;
          & > span {
            font-size: 1.1rem;
          }
          & > h3 {
            width: 108px;
          }
          > * {
            width: 100%;
          }
        }
      }
    }
  `}
`;
