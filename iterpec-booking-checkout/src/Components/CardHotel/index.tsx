import { <PERSON>, Chips, Divider } from "@iterpecdev/design-sistem";
import Icon from "@iterpecdev/icons-system";
import * as Styles from "./styles";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Hooks,
  getSearch,
  getHotel,
  getRegion,
  //@ts-ignore
} from "@iterpec/booking-utility";
import moment from "moment";
import ImageNotFoud from "../ImageNotFound";
import Carousel from "../Carrousel";
import { AmenitiesIconNoRepeat } from "../AmenitiesIcon/noRepeat";
import Stars from "../Stars";
const CardHotel = () => {
  const search = getSearch();
  const region = getRegion();
  const { rate, room, ...hotel } = getHotel();
  const { useCurrency } = Hooks;
  const { t } = useTranslation(); // Hook de tradução
  const cancelPenalties = rate.cancelPenalties[0];
  const data = hotel;

  const sumFor = (arr) => {
    var soma = 0;
    for (var x = 0, l = arr.length; x < l; x++) {
      soma += arr[x].value;
    }
    return soma;
  };
  return (
    <Styles.WrapperCardHotel>
      <Card className="card">
        <div className="WrapperImage">
          <h3>
            {hotel.name} <Stars stars={data.ratings.property.rating} />
          </h3>
          <div className="shadow" />
          {room?.images?.length ? (
            <Carousel showButtons={false} showDots={false}>
              {room?.images.map((image, index) => (
                <img
                  src={image.links["1000px"].href}
                  key={index}
                  loading="lazy"
                  alt={image.caption}
                />
              ))}
            </Carousel>
          ) : (
            <ImageNotFoud />
          )}
        </div>
        <div className="wrapperScoreAmenities">
          <Chips active variant="tag" color="success">
            {Number(data?.ratings.guest?.overall) * 2}
          </Chips>
          {!!data?.amenities?.length && (
            <AmenitiesIconNoRepeat
              amenities={[...data?.amenities, ...rate.amenities]}
            />
          )}
        </div>

        <Divider />
        <div className="wrapperContent">
          <p className="location">
            <span>
              {`${data?.address?.lineOne}, ${data?.address.city}, ${data?.address?.postalCode}`}
              {data?.address.countryCode
                ? `, ${data?.address.countryCode}`
                : ``}
            </span>
          </p>
        </div>
        <Divider />
        <div className="wrapperContent">
          <p>
            <b>{` ${t("cardHotel.rooms")}: `}</b>

            <span>{room?.name}</span>
          </p>

          <p>
            <b>{t("cardHotel.beds")}: </b>{" "}
            <span>{room?.bedGroups?.[0].description}</span>
          </p>

          <Divider />
          <p>
            <b>{t("cardHotel.checkIn")}: </b>
            <span>
              {`${moment(search.checkIn).format("DD/MM/YYYY")} ${
                hotel?.checkin.beginTime
              }`}{" "}
              {t("cardHotel.until")} {hotel?.checkin.endTime}
            </span>
          </p>

          <p>
            <b>{t("cardHotel.checkOut")}: </b>

            <span>
              {`${moment(search.checkOut).format("DD/MM/YYYY")} ${t(
                "cardHotel.until"
              )} ${hotel?.checkout.time}`}
            </span>
          </p>
          <p>
            <b>{t("cardHotel.minAgeCheckin")}: </b>
            <span>
              {hotel?.checkin?.minAge
                ? hotel.checkin.minAge
                : t("cardHotel.notInformed")}
            </span>
          </p>
          <p>
            <b>{t("cardHotel.cancellation")}: </b>
            {rate?.cancelPenalties?.map((penalty, index) =>
              penalty &&
              rate.refundable &&
              moment(penalty.startDate).isAfter(new Date()) &&
              moment(penalty?.endDate).isAfter(new Date()) ? (
                <span className={"refundable"}>
                  <b>{t("cardHotel.freeCancellation")}</b>{" "}
                  {t("cardHotel.until")}{" "}
                  <b>
                    {moment(
                      moment(penalty?.startDate).subtract(1, "days")
                    ).format("DD/MM/YYYY")}
                  </b>
                </span>
              ) : (
                <span className={"noRefundable"}>
                  <b>{t("cardHotel.nonRefundable")}</b>
                </span>
              )
            )}
          </p>
        </div>
        <Divider />

        <div className="wrapperContent">
          <p>
            <b>{t("cardHotel.guests")}</b>
          </p>
          {search?.rooms?.map((searchRoom, index) => (
            <p key={index}>
              <b>
                {t("cardHotel.room")} {index + 1}:{" "}
              </b>
              <span>
                {searchRoom.adults}{" "}
                {searchRoom.adults > 1
                  ? t("cardHotel.adults")
                  : t("cardHotel.adult")}
                {searchRoom.children > 0
                  ? ` ${t("cardHotel.and")} ${searchRoom.children} ${
                      searchRoom.children > 1
                        ? t("cardHotel.children")
                        : t("cardHotel.child")
                    }`
                  : null}
              </span>
            </p>
          ))}
        </div>
      </Card>
    </Styles.WrapperCardHotel>
  );
};

export default CardHotel;
