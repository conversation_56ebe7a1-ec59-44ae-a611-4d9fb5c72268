import { BlipChat } from "blip-chat-widget";
import React, { Fragment, useEffect } from "react";
import {
  getUser,
  // @ts-ignore
} from "@iterpec/booking-utility";
import { <PERSON><PERSON>, Drawer } from "@iterpecdev/design-sistem";
import { WrapperChat } from "./styles";
export const Chatbot: React.FC<{ hadleChat: () => void; open: boolean }> = ({
  open = true,
  hadleChat,
}) => {
  if (!open) return <Fragment />;
  const user = getUser();
  const blipClient = new BlipChat()
    .withAppKey(
      "YjJjcm90ZWFkb3IxOmJjYjdiNTYyLTA1ZTMtNDQwNS1hN2FjLWNlMzBkMWU1N2RmNw=="
    )
    .withCustomCommonUrl("https://iterpec.chat.blip.ai/")
    .withAccount({
      fullName: user?.name + " " + user?.surname,
      email: user?.email,
      phoneNumber: user?.phoneNumber,
      city: user?.countryCodeISO,
      extras: {
        channelId: "196fd9f7-1766-4b97-8ca8-811c4e62639a",
        clientId: "c443b2c1-2daf-4e1e-8786-c4aa99340089",
        // br / en / es
        idioma: "br",
      },
    })
    .withEventHandler(BlipChat.ENTER_EVENT, function () {
      console.log("enter");
    })
    .withEventHandler(BlipChat.LEAVE_EVENT, function () {
      console.log("leave");
      closeChat();
    })
    .withEventHandler(BlipChat.LOAD_EVENT, function () {
      console.log("chat loaded");
      /*    blipClient.sendCommand({
        id: "ag0asd0as-daasdasd0a",
        to: "<EMAIL>",
        method: Lime.CommandMethod.GET,
        uri: "/entities",
      }); */
    })
    .withEventHandler(BlipChat.CREATE_ACCOUNT_EVENT, function () {
      console.log("account created");
      blipClient.sendMessage({
        type: "text/plain",
        content: "Start",
      });
    });
  const closeChat = () => {
    hadleChat();
    blipClient.destroy();
  };

  useEffect(() => {
    blipClient?.build();

    blipClient.toogleChat();

    const blipChatButton = document.getElementById("blip-chat-open-iframe");

    blipChatButton.classList.remove("opened");
  }, []);
  return <Fragment />;
};
