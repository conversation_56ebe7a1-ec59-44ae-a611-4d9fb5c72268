import Icon from "@iterpecdev/icons-system";
import * as Styles from "./styles";
import { Card, Divider } from "@iterpecdev/design-sistem";
import React, {
  useEffect,
  useState,
  useMemo,
  useCallback,
  useRef,
} from "react";
import moment from "moment";
import { useTranslation } from "react-i18next";

const TIMER_VALUE = 840; // 840s = 14min

const formatTime = (seconds: number) =>
  moment.utc(seconds * 1000).format("mm:ss");

const Timer = React.memo(() => {
  const { t } = useTranslation();
  const [timer, setTimer] = useState(TIMER_VALUE);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const updateTimer = useCallback(() => {
    setTimer((prevTimer) => {
      if (prevTimer === 0 && intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
        return 0;
      }
      return prevTimer - 1;
    });
  }, []);

  useEffect(() => {
    intervalRef.current = setInterval(updateTimer, 1000);
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [updateTimer]);

  const percent = useMemo(() => (timer / TIMER_VALUE) * 100, [timer]);

  return (
    <Styles.WrapperTimer $percent={percent}>
      <Card>
        <Icon name="TimerOutline" />
        <h2
          dangerouslySetInnerHTML={{
            __html: timer
              ? t("timer.expiresIn", { time: formatTime(timer) })
              : t("timer.expired"),
          }}
        />
        <Divider />
      </Card>
    </Styles.WrapperTimer>
  );
});

export default Timer;
