import styled from "styled-components";

export const WrapperTimer = styled.div<{ $percent: number }>`
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  & > div {
    width: 100%;
    background-color: #fff;
    display: flex;
    align-items: center;
    gap: 15px;
    border: #f00;
    position: relative;
    & > hr {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 2px;
      width: ${(props) => props.$percent}%;
      transition: all 0.5s;
      background-color: #f00;
    }
    & > h2 {
      color: #000;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      & > b {
        color: #f00;
        font-weight: 700;
      }
    }
    & > svg {
      width: 25px;
    }
  }
`;
