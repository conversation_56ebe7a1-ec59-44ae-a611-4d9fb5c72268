import React, {
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
} from "react";
import { useNavigate } from "react-router-dom";
import Timer from "../Timer";
import * as Styles from "./styles";
import { Toaster, toast } from "sonner";

import {
  getSearch,
  setHeader,
  getHotel,
  getUser,
  Hooks,
  // @ts-ignore
} from "@iterpec/booking-utility";
import CardHotel from "../CardHotel";
import GuestForm from "../GuestForm";
import PaymentForm from "../PaymentContact";
import { Accordion, But<PERSON>, Card, Drawer } from "@iterpecdev/design-sistem";
import Payment from "../Payment";
import { useTranslation } from "react-i18next";
import useWebSocket, { ReadyState } from "react-use-websocket";
import { separarTelefone, eventEmitter, EVENT_TYPES } from "../../utils";
import type {
  WebSocketValidationErrorData,
  WebSocketBookingSuccessData,
  WebSocketBookingErrorData,
} from "../../utils";
import Footer from "../Footer";
import Api from "../../Services";
import moment from "moment";

const Checkout = () => {
  const api = useMemo(() => new Api(), []);
  const [pixData, setPixData] = useState(null);
  const [drawer, setDrawer] = useState(false);
  const [isBookingInProgress, setIsBookingInProgress] = useState(false);
  const isMobile = Hooks.useMobileDetect().isMobile();
  const navigate = useNavigate();
  const [hotelData, setHotelData] = useState(getHotel());
  const { room = null, rate = null, ...hotel } = hotelData;

  const user = getUser();
  const search = getSearch();
  const { t } = useTranslation();

  const [wsKey, setWsKey] = useState(0);
  const { sendMessage, lastMessage, readyState } = useWebSocket(
    `wss://api-expedia.iterpec.com/booking/ws?k=${wsKey}`
    /* "ws://localhost:8082/booking/ws" */
  );
  const Accordions: JSX.Element = useMemo(() => {
    return (
      <>
        {rate?.cancelPenalties && rate.cancelPenalties.length > 0 && (
          <Accordion
            className="card accordion"
            title={t("checkout.cancelPolicies")}
            defaultOpen
          >
            <p>
              {rate.cancelPenalties.map((penalty, index) =>
                penalty &&
                rate.refundable &&
                moment(penalty.startDate).isAfter(new Date()) &&
                moment(penalty?.endDate).isAfter(new Date()) ? (
                  <span className={"refundable"} key={index}>
                    <b>{t("cardHotel.freeCancellation")}</b>{" "}
                    {t("cardHotel.until")}{" "}
                    <b>
                      {moment(
                        moment(penalty?.startDate).subtract(1, "days")
                      ).format("DD/MM/YYYY")}
                    </b>
                  </span>
                ) : (
                  <span className={"noRefundable"} key={index}>
                    <b>{t("cardHotel.nonRefundable")}</b>
                  </span>
                )
              )}
            </p>
            {rate.cancelPenalties.map((policie, idx) => (
              <p key={idx}>{policie.text}</p>
            ))}
          </Accordion>
        )}
        {
          //@ts-ignore
          hotel?.mandatoryFee && hotel.mandatoryFee.trim() !== "" && (
            <Accordion
              className="card accordion"
              title={t("checkout.mandatoryFee")}
              defaultOpen
            >
              <div
                //@ts-ignore
                dangerouslySetInnerHTML={{ __html: hotel.mandatoryFee }}
              />
            </Accordion>
          )
        }
        {hotel?.fees.optional && hotel?.fees.optional.trim() !== "" && (
          <Accordion
            className="card accordion"
            title={t("checkout.optionalFee")}
            defaultOpen
          >
            <div dangerouslySetInnerHTML={{ __html: hotel?.fees.optional }} />
          </Accordion>
        )}
        {(hotel?.checkin.instructions?.trim() ||
          hotel?.checkin.specialInstructions.trim() ||
          hotel?.policies?.knowBeforeYouGo.trim()) && (
          <Accordion
            className="card accordion"
            title={t("checkout.knowBeforeYouGo")}
            defaultOpen
          >
            {hotel?.checkin.instructions?.trim() && (
              <div
                dangerouslySetInnerHTML={{
                  __html: hotel?.checkin.instructions,
                }}
              />
            )}
            {hotel?.checkin.specialInstructions?.trim() && (
              <div
                dangerouslySetInnerHTML={{
                  __html: hotel?.checkin.specialInstructions,
                }}
              />
            )}
            {hotel?.policies?.knowBeforeYouGo.trim() && (
              <div
                dangerouslySetInnerHTML={{
                  __html: hotel?.policies?.knowBeforeYouGo,
                }}
              />
            )}
          </Accordion>
        )}
      </>
    );
  }, [hotel, t, rate]);

  // Memoize Container para evitar recriação
  const Container = useMemo(
    () =>
      ({ children }) =>
        isMobile ? (
          <>{children}</>
        ) : (
          <Styles.WrapperContainer>{children}</Styles.WrapperContainer>
        ),
    [isMobile]
  );

  // Função para copiar texto para a área de transferência
  const copyTextToClipboard = useCallback(async (text) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (error) {
      console.error("Failed to copy text:", error);
    }
  }, []);

  // Header
  const handleHeader = useCallback(() => {
    setHeader({
      $showMenu: isMobile,
      $showSearch: false,
      $showFallback: true,
      $show: isMobile,
      $fallback: () => navigate(-1),
    });
  }, [navigate, isMobile]);

  // Inicialização do WebSocket
  const initializeWebSocket = useCallback(() => {
    if (readyState) {
      sendMessage(
        JSON.stringify({
          messageType: "BOOKING_CLIENT_ID",
          clientId: user.clientId,
        })
      );
    }
  }, [readyState, sendMessage, user.idUser]);

  // Manipulação de mensagens do WebSocket
  const handleWebSocketMessage = useCallback(
    (message) => {
      if (message === "ping") return;
      try {
        const parsedMessage = JSON.parse(message);
        // Verifica se é um erro de validação
        if (parsedMessage?.error?.errorStatus === "VALIDATION_ERROR") {
          const errorData: WebSocketValidationErrorData = parsedMessage;
          eventEmitter.emit(EVENT_TYPES.WEBSOCKET_VALIDATION_ERROR, errorData);
          toast.error(t("websocket.validationError"));
          setIsBookingInProgress(false);
          isBookingInProgressRef.current = false;
          return;
        }

        // Verifica se é um erro geral do WebSocket
        if (parsedMessage?.errorStatus && parsedMessage?.statusCode) {
          const errorData: WebSocketBookingErrorData = parsedMessage;
          eventEmitter.emit(EVENT_TYPES.WEBSOCKET_BOOKING_ERROR, errorData);
          setIsBookingInProgress(false);
          isBookingInProgressRef.current = false;
          toast.error(t("websocket.generalError"));
          return;
        }

        // Trata erro inesperado (ex: No Room from Redis found...)
        if (parsedMessage?.error?.errorStatus === "UNEXPECTED_ERROR") {
          const errorData: WebSocketBookingErrorData = parsedMessage;
          eventEmitter.emit(EVENT_TYPES.WEBSOCKET_BOOKING_ERROR, errorData);
          setIsBookingInProgress(false);
          isBookingInProgressRef.current = false;
          toast.error(
            t("websocket.unexpectedError", {
              message: parsedMessage?.error?.message,
            })
          );
          return;
        }
        // Trata erro inesperado (ex: No Room from Redis found...)
        if (
          parsedMessage?.error?.errorStatus === "STATIC_DATA_INTEGRATION_ERROR"
        ) {
          const errorData: WebSocketBookingErrorData = parsedMessage;
          eventEmitter.emit(
            EVENT_TYPES.STATIC_DATA_INTEGRATION_ERROR,
            errorData
          );
          setIsBookingInProgress(false);
          isBookingInProgressRef.current = false;
          return;
        }

        // Verifica se contém dados do PIX
        if (parsedMessage?.emv) {
          const successData: WebSocketBookingSuccessData = parsedMessage;
          setPixData(parsedMessage);
          eventEmitter.emit(EVENT_TYPES.WEBSOCKET_BOOKING_SUCCESS, successData);
          setIsBookingInProgress(false);
          isBookingInProgressRef.current = false;
        }
        // Verifica se o pagamento foi concluído
        else if (
          parsedMessage?.status === "PAID" ||
          parsedMessage?.status === "PIX_PENDING"
        ) {
          const successData: WebSocketBookingSuccessData = parsedMessage;
          eventEmitter.emit(EVENT_TYPES.WEBSOCKET_BOOKING_SUCCESS, successData);
          setIsBookingInProgress(false);
          isBookingInProgressRef.current = false;
          navigate("/my-bookings");
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
        const errorData: WebSocketBookingErrorData = {
          message: "Erro ao processar mensagem do WebSocket",
          errorStatus: "PARSE_ERROR",
          statusCode: 500,
          extra: { originalError: error },
        };
        eventEmitter.emit(EVENT_TYPES.WEBSOCKET_BOOKING_ERROR, errorData);
        setIsBookingInProgress(false);
        isBookingInProgressRef.current = false;
      }
    },
    [navigate]
  );

  // Criação do payload para hold booking
  const createHoldBookingPayload = useCallback(() => {
    // Função utilitária para determinar o tipo de hóspede
    const getGuestType = (birthday: Date): "CHILD" | "ADULT" => {
      if (!birthday) return "ADULT";
      const birthDate = moment(birthday, "YYYY-MM-DD");
      const age = moment().diff(birthDate, "years");
      return age < 18 ? "CHILD" : "ADULT";
    };
    const phone = separarTelefone(user.phoneNumber);
    const rooms = search?.guestData?.map((room) => ({
      paxs: [
        {
          given_name: room.firstName,
          family_name: room.familyName,
          birthday: moment(room.birthday).format("YYYY-MM-DD"),
          type: getGuestType(room.birthday),
          main: true,
        },
        ...room.guest.map((guest) => ({
          given_name: guest.firstName,
          family_name: guest.familyName,
          main: false,
          birthday: moment(guest.birthday).format("YYYY-MM-DD"),
          type: getGuestType(guest.birthday),
        })),
      ],
      smoking: false,
      special_request: "none",
    }));

    return {
      priceCheck: {
        propertyId: hotel?.propertyIdTBI,
        rateId: rate?.rateId,
        roomId: room?.id,
        token: rate?.bedConfiguration[0].token,
      },
      holdBooking: {
        partnerId: user.idUser,
        createItinerary: {
          email: user.email,
          phone: {
            country_code: phone.ddi,
            area_code: phone.ddd,
            number: phone.numero,
          },
          rooms,
          payments: search?.payments.map((item) => ({
            billing_contact: {
              ...item,
            },
          })),
        },
      },
    };
  }, [user, search, hotel, rate, room]);

  // Função para criar hold booking
  const isBookingInProgressRef = useRef(false);
  const holdCreate = useCallback(async () => {
    // Verifica se já está em processo de reserva
    if (isBookingInProgressRef.current) return;
    isBookingInProgressRef.current = true;
    setIsBookingInProgress(true);

    if (pixData?.pixKey) {
      setDrawer(true);
      setIsBookingInProgress(false);
      isBookingInProgressRef.current = false;
      return;
    }

    // Se o WebSocket estiver encerrado, força reconexão e retorna
    if (readyState === ReadyState.CLOSED) {
      setWsKey((k) => k + 1);
      setIsBookingInProgress(false);
      isBookingInProgressRef.current = false;
      return;
    }

    // Aguarda o WebSocket conectar, se necessário
    const waitForWebSocket = async (maxTries = 10, interval = 300) => {
      let tries = 0;
      while (readyState !== ReadyState.OPEN && tries < maxTries) {
        await new Promise((res) => setTimeout(res, interval));
        tries++;
      }
      return readyState === ReadyState.OPEN;
    };

    // Aguarda o WebSocket conectar, se necessário
    const isConnected = await waitForWebSocket();
    if (!isConnected) {
      console.error(
        "Não foi possível conectar ao WebSocket para finalizar a reserva."
      );
      setIsBookingInProgress(false);
      isBookingInProgressRef.current = false;
      return;
    }

    try {
      const payload = createHoldBookingPayload();

      sendMessage(
        JSON.stringify({
          messageType: "BOOKING_DO_BOOKING",
          clientId: user.clientId,
          bookingWebSocketRequest: {
            searchId: search?.searchId,
            taxRegistrationNumber:
              search.payments[0].document ?? user.document.documentNumber,
            userCountryCode: user.document.documentCountryId,
            isBookingRequest: false,
            ...payload,
          },
        })
      );
    } catch (error) {
      console.error("Error creating hold:", error);
      setIsBookingInProgress(false);
      isBookingInProgressRef.current = false;
    }
  }, [
    pixData,
    createHoldBookingPayload,
    sendMessage,
    user,
    search,
    readyState,
  ]);

  // Efeitos
  useEffect(() => {
    handleHeader();
    initializeWebSocket();
    setHotelData(getHotel());
  }, [handleHeader, initializeWebSocket]);
  useEffect(() => {
    handleHeader();
  }, [isMobile, handleHeader]);
  useEffect(() => {
    if (lastMessage?.data) {
      handleWebSocketMessage(lastMessage?.data);
    }
  }, [lastMessage, handleWebSocketMessage]);

  return (
    <Container>
      <Styles.WrapperCheckout>
        <Toaster richColors />
        {!!isMobile && <h1>{t("checkout.reservationSecure")}</h1>}
        <Styles.WrapperCardGroup className="HotelTimerPayment">
          <Styles.WrapperTimer>
            <Timer />
            <CardHotel />
          </Styles.WrapperTimer>
          {!isMobile && <Payment submit={holdCreate} />}
        </Styles.WrapperCardGroup>
        <Styles.WrapperCardGroup className="UserForm">
          <Card className="card">
            <GuestForm />
          </Card>
          <Card className="card">
            <PaymentForm />
          </Card>
          {!isMobile && Accordions}
        </Styles.WrapperCardGroup>
        {isMobile && (
          <>
            {Accordions}
            <Payment submit={holdCreate} />
          </>
        )}
      </Styles.WrapperCheckout>
      <Drawer
        open={drawer}
        position={isMobile ? "bottom" : "left"}
        onDismiss={() => setDrawer(false)}
      >
        <Styles.WrapperPix title="Copie ou scaneie o QR CODE">
          <h4>Copie ou scaneie o QR CODE</h4>
          <span>
            Ao copiar o código, abra seu aplicativo cadastrado no PIX e realize
            seu pagamento de forma rápida.
          </span>
          <img src={pixData?.location?.url} alt="QR CODE" />
          <Button onClick={() => copyTextToClipboard(pixData?.emv)}>
            COPIAR CÓDIGO
          </Button>
          <Button onClick={() => setDrawer(false)} variant="secondary">
            FECHAR
          </Button>
        </Styles.WrapperPix>
      </Drawer>
      <Footer />
    </Container>
  );
};

export default Checkout;
