import styled from "styled-components";

export const WrapperCardGroup = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
  ${({ theme }) =>
    theme.breakpoints.desktop(` 
        gap: 40px;
        &.HotelTimerPayment{
          width: 31%;
          order: 2;
          & > div { 
            width: 100%;
          }
        }
        &.UserForm{
          order: 1;
          width: 65%;
        }
    `)}
  ${({ theme }) =>
    theme.breakpoints.mobile(` 
        gap: 20px;
        justify-content: center;
        align-items: center;
        width: 100%;
        
    `)}
`;

export const WrapperContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #f1f1f1;
  & > div {
    max-width: 1200px;
    padding-bottom: 2rem;
    width: 100%;
  }
`;

export const WrapperTimer = styled.div`
  width: 95%;
`;
export const WrapperCheckout = styled.div`
  padding: 20px 16px;

  display: flex;
  flex-wrap: wrap;
  ${(props) =>
    props.theme.breakpoints.mobile(`
        flex-direction: column;
        padding: 20px 0px;
      `)}

  gap: 20px;
  * {
    font-family: Inter;
  }
  & > h1 {
    font-size: 1.5rem;
    line-height: 200%;
    ${(props) =>
      props.theme.breakpoints.mobile(`
        padding: 0 16px;
      `)}
  }
  background-color: #f1f1f1;
  & > div {
    &.card,
    > div.card {
      background-color: ${(props) => props.theme.palette.neutral.white};
      width: 100%;
      &.accordion {
        ${(props) =>
          props.theme.breakpoints.desktop(`
          order: 3;
        `)}
      }
    }

    ${(props) =>
      props.theme.breakpoints.desktop(`
          height: fit-content;
          &.{
          }
      `)}
  }
`;

export const Wrapper = styled.div`
  background-color: #f1f1f1;
  width: 100%;
  & > div {
    background-color: #fff;
  }
  * {
    font-family: Inter;
  }
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  gap: 20px;
`;
export const WrapperPix = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: center;
  && > h4 {
    width: 100%;
    margin: 10px 0;
  }
  & > button {
    margin: 8px 0;
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  & > img {
    max-width: 80%;
  }
`;
