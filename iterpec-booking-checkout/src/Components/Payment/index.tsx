import {
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Divider,
  Input,
  Tooltip,
  Drawer,
} from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
// @ts-ignore
import { getSearch, Hooks, getHotel } from "@iterpec/booking-utility";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import Icon from "@iterpecdev/icons-system";
import { useTranslation } from "react-i18next";
import moment from "moment";

const Payment = ({
  submit,
  disabled,
}: {
  submit: () => void;
  disabled?: boolean;
}) => {
  const { t } = useTranslation();
  const { useCurrency, useCurrencyName } = Hooks;
  const [terms, setTerms] = useState(false);
  const [openInfo, setInfo] = useState(false);
  const [error, setErrorTerms] = useState(false);
  const search = getSearch();
  const hotel = getHotel();
  const holdCreateRef = useRef<() => void>();

  const isMobile = Hooks.useMobileDetect().isMobile();

  const validateForm = useCallback(() => {
    window.dispatchEvent(new CustomEvent("validateForm"));
  }, []);

  const validateCheckbox = useCallback(() => {
    if (!terms) {
      setErrorTerms(true);
      return;
    }
    setErrorTerms(false);

    return new Promise((res) => {
      validateForm();
      setTimeout(() => res(null), 7000);
    });
  }, [terms, validateForm]);

  useEffect(() => {
    const listener = () => {
      if (holdCreateRef.current) {
        holdCreateRef.current();
      }
    };
    window.addEventListener("validForm", listener);
    return () => window.removeEventListener("validForm", listener);
  }, []);

  useEffect(() => {
    holdCreateRef.current = submit;
  }, [submit]);

  // 🎯 Memo para calcular totais e evitar re-renderizações
  const {
    nights,
    totalPrice,
    averagePerNight,
    taxesAndFees,
    mandatoryFee,
    resortFee,
    totalExtraFees,
    grandTotal,
    resortFeesRequestPricePerNight,
  } = useMemo(() => {
    const nights = moment(search.checkOut).diff(search.checkIn, "days") || 1;

    const totalPrice = hotel?.rate?.totalPriceInclusive?.value || 0;
    const averagePerNight = hotel?.rate?.averagePerNight?.value || 0;

    const exclusive = hotel?.rate?.totalPriceExclusive?.value || 0;
    const taxesAndFees = totalPrice - exclusive;

    const resortFeesRequestPricePerNight =
      hotel?.rate?.resortFeesRequest?.pricePerNight?.value ?? null;

    const mandatoryFee =
      hotel?.rate?.mandatoryTaxRequest?.priceTotal?.value ?? null;

    const resortFee = hotel?.rate?.resortFeesRequest?.priceTotal?.value ?? null;

    const totalExtraFees = [mandatoryFee, resortFee]
      .filter((v): v is number => v !== null)
      .reduce((sum, v) => sum + v, 0);

    const grandTotal = Math.round((totalPrice + totalExtraFees) * 100) / 100;

    return {
      nights,
      totalPrice,
      averagePerNight,
      taxesAndFees,
      mandatoryFee,
      resortFee,
      totalExtraFees,
      grandTotal,
      resortFeesRequestPricePerNight,
    };
  }, [hotel, search]);

  return (
    <Styles.WrapperPayment>
      <Card>
        <h2>{t("payment.title")}</h2>
        <div>
          <p>
            <span>
              {`${search.rooms.length} ${t("payment.rooms")} ${nights} ${t(
                "payment.nights"
              )}`}
            </span>
            <span>{useCurrency(hotel?.rate?.totalPriceExclusive?.value)}</span>
          </p>

          <p>
            <small>
              {useCurrency(averagePerNight)}{" "}
              {search.rooms.length > 1
                ? t("payment.averagePerNightMult")
                : t("payment.averagePerNight")}
            </small>
          </p>
        </div>

      {/*   <p>
          <span>{t("payment.taxesAndFees")}</span>
          <span>{useCurrency(taxesAndFees)}</span>
        </p>
 */}
        <p>
          <span>
            {t("payment.taxesAndFees")}
          <Styles.WrapperInfo>
              <Icon name="InformationSlabCircleOutline" />
              <Tooltip className="tooltip">{t("payment.taxesAndFeesDescrption")}</Tooltip>
            </Styles.WrapperInfo>
          </span>
          <span>{useCurrency(taxesAndFees)}</span>
        </p>

        {mandatoryFee !== null && (
          <div>
            <p>
              <span>{t("payment.localTaxesAndFees")}</span>
              <span>{useCurrency(mandatoryFee)}</span>
            </p>
            <p>
              <small>{t("payment.localTaxes")}</small>
            </p>
          </div>
        )}

        {resortFee !== null && (
          <div>
            <p>
              <span>{t("payment.risortTaxesAndFees")}</span>
              <span>{useCurrency(resortFee)}</span>
            </p>
            <p>
              <small>
                {useCurrency(resortFeesRequestPricePerNight)}{" "}
                {t("payment.localTaxesAndFeesText")}
              </small>
            </p>
          </div>
        )}

        <Divider />

        <p>
          <span>{t("payment.total")}</span>
          <span>{useCurrency(grandTotal)}</span>
        </p>

        <p>
          <span>
            <b>{t("payment.payNow")}</b>
          </span>
          <span>
            <b>{useCurrency(totalPrice)}</b>
          </span>
        </p>

        {!!hotel?.rate?.totalFeesRequest?.value && (
          <>
            <Divider />
            {isMobile ? (
              <p onClick={() => setInfo(!openInfo)}>
                <span>
                  {t("payment.localTaxes")}
                  <Styles.WrapperInfo>
                    <Icon name="InformationSlabCircleOutline" />
                  </Styles.WrapperInfo>
                </span>
                <span>{useCurrency(hotel?.rate?.totalFeesRequest.value)}</span>
              </p>
            ) : (
              <p>
                <span>
                  {t("payment.localTaxes")}
                  <Styles.WrapperInfo>
                    <Icon name="InformationSlabCircleOutline" />
                    <Tooltip className="tooltip">
                      {t("payment.drawerDescription", {
                        current_currency: useCurrencyName(
                          hotel?.rate?.totalFeesRequest?.currency
                        ),
                        original_currency: useCurrencyName(
                          hotel?.rate?.totalFeesBilling?.currency
                        ),
                        billable_value: useCurrency(
                          hotel?.rate?.totalFeesBilling?.value,
                          hotel?.rate?.totalFeesBilling?.currency
                        ),
                      })}
                    </Tooltip>
                  </Styles.WrapperInfo>
                </span>
                <span>{useCurrency(hotel?.rate?.totalFeesRequest.value)}</span>
              </p>
            )}
          </>
        )}

        <Divider />

        <Input label={t("payment.discountCoupon")} />

        <div>
          <Checkbox checked={terms} onChangeValue={setTerms}>
            <span
              dangerouslySetInnerHTML={{
                __html: t("payment.termsAndConditions"),
              }}
            />
          </Checkbox>

          {error && (
            <span className="error">{t("payment.errorAcceptTerms")}</span>
          )}
        </div>
        <Drawer
          open={openInfo}
          position="bottom"
          onDismiss={() => setInfo(!openInfo)}
          title={t("payment.drawerTitle")}
        >
          <p>
            {t("payment.drawerDescription", {
              current_currency: useCurrencyName(
                hotel?.rate?.totalFeesRequest?.currency
              ),
              original_currency: useCurrencyName(
                hotel?.rate?.totalFeesBilling?.currency
              ),
              billable_value: useCurrency(
                hotel?.rate?.totalFeesBilling?.value,
                hotel?.rate?.totalFeesBilling?.currency
              ),
            })}
          </p>
        </Drawer>

        <Button onClick={validateCheckbox} disabled={disabled}>
          {t("payment.payWithClaroPay")}
        </Button>
      </Card>
    </Styles.WrapperPayment>
  );
};

export default Payment;
