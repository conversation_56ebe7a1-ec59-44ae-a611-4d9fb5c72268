import styled from "styled-components";

export const WrapperInfo = styled.div`
  & > div {
    font-size: 12px;
    line-height: 110%;
  }
`;
export const WrapperPayment = styled.div`
  width: 100%;
  background-color: ${({ theme }) => theme.palette.neutral.white};
  & > div {
    padding: 15px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 15px;
    & > button {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    & > h2 {
      color: #000;
      font-size: 18px;
      font-style: normal;
      line-height: 24px; /* 133.333% */
    }
    & > div > span.error {
      font-weight: 400;
      font-size: 14px;
      line-height: 150%;
      transition: color 300ms ease 0s;
      color: rgb(234, 78, 44);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    & > p,
    > div > p {
      display: flex;
      justify-content: space-between;
      & > span.discount {
        color: ${(props) => props.theme.palette.status.success.successDefault};
      }
      position: relative;

      & > span {
        display: inherit;
        gap: 6px;
        color: #000;
        font-size: 16px;
        font-style: normal;
        line-height: 25px; /* 137.5% */

        &:focus-within,
        &:focus,
        &:active,
        &:target,
        &:hover,
        &:focus-visible,
        & *:focus:focus-within:active:target:hover:focus-visible,
        & > use:hover use:active use:focus {
          & > div > div.tooltip {
            & ${WrapperInfo}:focus {
              display: block;
            }
            &
              ${WrapperInfo}
              *:focus:focus-within:active:target:hover:focus-visible {
              display: block;
            }
            display: block;
            position: absolute;
            left: 0;
            top: 20px;
            z-index: 5;
            width: 100%;
          }
        }

        & > div {
          width: min-content;
          display: inherit;
          & > svg {
            width: 15px;
          }

          & > div.tooltip {
            display: none;
          }
        }
      }
    }
  }
`;
