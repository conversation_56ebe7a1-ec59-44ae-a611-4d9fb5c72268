import { <PERSON><PERSON>, Card, Skeleton } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import Icon from "@iterpecdev/icons-system";
import Carousel from "../Carrousel";
import { Fragment, useEffect, useState, useCallback } from "react";
//@ts-ignore
import { getUser, setHeader } from "@iterpec/booking-utility";
import { useNavigate } from "react-router-dom";
import Api from "../../Services";
import { BookingItem, IRetrive, IStaticData } from "../../Services/types";
import { CardTrip } from "../CardTrip";
import Booking from "../Booking";
import { WrapperButon } from "./styles";
import { useTranslation } from "react-i18next";
import Footer from "../Footer";

const MyBookings = () => {
  const { t } = useTranslation();
  const api = new Api();
  const user = getUser();
  const navigate = useNavigate();

  const [bookings, setBookings] = useState<BookingItem[]>([]);
  const [expanded, setExpanded] = useState(false);
  const [selected, setSelected] = useState<BookingItem | null>(null);
  const [searching, setSearching] = useState(false);

  const getStaticData = useCallback(
    async (propertyId: string) => {
      try {
        const { data } = await api.getStaticData(propertyId);
        return data.property;
      } catch (error) {
        console.log(error);
        return null;
      }
    },
    [api]
  );

  const getRetrive = useCallback(async () => {
    setSearching(true);
    try {
      const { data } = await api.getRetrive(user.idUser);
      const itineraries = data?.itineraries || [];
      const bookingsData: BookingItem[] = await Promise.all(
        itineraries
          .filter(
            (itinerary) =>
              Array.isArray(itinerary.stays) && itinerary.stays.length > 0
          )
          .map(async (itinerary) => {
            const stay = itinerary.stays[0];
            const staticData = await getStaticData(stay.propertyId);
            return { static: staticData, retrive: itinerary };
          })
      );
      setBookings(bookingsData);
      setSearching(false);
    } catch (error) {
      console.error("Error fetching bookings:", error);
    } finally {
      setSearching(false);
    }
  }, [api, user.idUser, getStaticData]);

  console.log(searching);
  const handleExpanded = useCallback((hotel: BookingItem) => {
    setSelected(hotel);
    setExpanded(true);
  }, []);

  const handleHeader = useCallback((func: () => void) => {
    setHeader({
      $showSearch: false,
      $showMenu: true,
      $showFallback: true,
      $fallback: func,
    });
  }, []);

  useEffect(() => {
    handleHeader(expanded ? () => setExpanded(false) : () => navigate("/"));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [expanded, handleHeader, navigate]);

  useEffect(() => {
    getRetrive();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Styles.WrapperFooter>
      <Styles.WrapperMyBookings>
        {!expanded && (
          <div className="WrapperTitle">
            <h1>{t("myBookings.title")}</h1>
            <Icon name="BagSuitcaseOutline" />
          </div>
        )}
        {searching && (
          <div className="WrapperSkeleton">
            <Skeleton width="100%" height="90px" />
          </div>
        )}
        {!expanded ? (
          bookings.length ? (
            bookings.map((hotel, index) => (
              <CardTrip
                key={index}
                hotel={hotel}
                expanded={expanded}
                setExpanded={() => handleExpanded(hotel)}
              />
            ))
          ) : !searching ? (
            <h4
              dangerouslySetInnerHTML={{ __html: t("myBookings.noBookings") }}
            />
          ) : null
        ) : (
          <Booking booking={selected} close={() => setExpanded(false)} />
        )}
      </Styles.WrapperMyBookings>
      <Footer />
    </Styles.WrapperFooter>
  );
};

export default MyBookings;
