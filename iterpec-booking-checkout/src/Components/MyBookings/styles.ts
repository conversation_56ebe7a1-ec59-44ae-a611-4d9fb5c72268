import styled from "styled-components";
import { Card } from "@iterpecdev/design-sistem";

export const WrapperMyBookings = styled(Card)`
  * {
    font-family: Inter;
  }
  display: flex;
  padding: 15px;
  flex-direction: column;
  ${({ theme }) =>
    theme.breakpoints.desktop(`
      margin-top: 2rem;
      max-width: 1200px;
      background-color: ${theme.palette.neutral.white};
      padding: ${theme.spacings.squished.lg};
  `)}
  gap: 20px;
  width: 100%;

  & > div.WrapperSkeleton {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  & > div.WrapperTitle {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    & > h1 {
      color: #000;
      font-size: 24px;
      font-style: normal;
      font-weight: 900;
    }
    & > svg {
      width: 30px;
      height: auto;
    }
  }
`;
export const WrapperFooter = styled.div`
  display: flex;
  min-height: calc(100vh - 77px);
  flex-direction: column;
  justify-content: space-between;

  ${({ theme }) =>
    theme.breakpoints.desktop(`
      align-items: center;
      background-color: ${theme.palette.neutral.lighter};
      height: 100%;

  `)}
`;

export const WrapperButon = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  & > button {
  }
`;
