import styled from "styled-components";

export const WrapperGuestForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  & > form {
    display: flex;
    flex-direction: column;
    gap: 10px;
    & > div {
      display: flex;
      flex-direction: column;
      gap: 20px;
      ${(props) =>
        props.theme.breakpoints.desktop(`
          width: 100%;
          flex-direction: row;
          flex-wrap: wrap;
          & > h3 {
            width: 100%
          }
          & > div:not(.guest) {
            width: 48%;
          }
          & > div.guest { 
            width: 100%;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            & > div { 
              width: 48%;
            }

          }
          
        `)}
    }
  }

  * {
    font-family: Inter;
  }
  p {
    width: 100%;
    color: rgb(64, 64, 64);
  }
  h2 {
    color: #000;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    width: 100%;
    line-height: 24px;
  }
  h3 {
    color: #000;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    width: 100%;
  }

  padding: 15px;
`;
