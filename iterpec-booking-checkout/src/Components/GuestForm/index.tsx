import { Fragment, useEffect, useRef, useState, useCallback } from "react";
import { Card, Input, Select } from "@iterpecdev/design-sistem";
import { ErrorItem } from "../../Services/types";
import { useNavigate } from "react-router-dom";
import * as Styles from "./styles";
import * as Yup from "yup";
import { IContryCode } from "../../Services/types";

import {
  Types,
  setSearch,
  getSearch,
  getHotel,
  // @ts-ignore
} from "@iterpec/booking-utility";
import { useTranslation } from "react-i18next"; // Importando o hook de tradução
import { useValidationErrorListener } from "../../utils";
import type { WebSocketValidationErrorData } from "../../utils";
import moment from "moment";
import {
  generateMockAdult,
  generateMockChild,
  generateMockRooms,
} from "../../utils/mockRooms";

const GuestForm = () => {
  const { t } = useTranslation(); // Usando o hook de tradução
  const search = getSearch();
  const refs = useRef<Array<React.RefObject<HTMLDivElement>>>([]);

  const devMode =
    new URLSearchParams(window.location.search).get("devMode") === "true"; // Ativa o mock se devMode=true estiver na URL

  const [rooms, setRooms] = useState<Types["IGuest"][]>(
    devMode
      ? search.rooms.map((room) => {
          const mainGuest = generateMockRooms(1)[0]; // Gera um hóspede principal
          const guests = [
            // Adultos extras (além do principal)
            ...Array.from(
              { length: room.adults > 1 ? room.adults - 1 : 0 },
              () => generateMockAdult()
            ),
            // Crianças
            ...Array.from({ length: room.children || 0 }, () =>
              generateMockChild()
            ),
          ];
          return {
            ...mainGuest,
            guest: guests,
          };
        })
      : getSearch().rooms.map<Types["IGuest"]>((room) => ({
          ...room,
          guest: Array.from(
            {
              length:
                (room.adults > 1 ? room.adults - 1 : 0) + (room.children || 0),
            },
            () => ({
              firstName: "",
              familyName: "",
              main: false,
              birthday: null,
            })
          ),
        }))
  );

  const [errors, setErrors] = useState<
    { index: number; errors: ErrorItem[] }[]
  >([]);
  // Estado para erros do WebSocket
  const [webSocketErrors, setWebSocketErrors] = useState<
    Record<number, Record<string, string>>
  >({});

  // Recebe erros do WebSocket e mapeia para os campos
  const handleWebSocketValidationError = useCallback(
    (data: WebSocketValidationErrorData) => {
      // Tenta pegar dos dois caminhos possíveis
      const validationErrors =
        data.extra?.validationErrors ||
        // @ts-ignore : pode vir do backend assim
        data.error?.extra?.validationErrors ||
        [];

      // Novo: só seta erro no quarto correto
      const wsErrorsByRoom: Record<number, Record<string, string>> = {};
      validationErrors.forEach((validation: any) => {
        const roomIndex = validation.index;
        if (!wsErrorsByRoom[roomIndex]) wsErrorsByRoom[roomIndex] = {};
        validation.error.forEach((error: any) => {
          wsErrorsByRoom[roomIndex][error.inputName] = error.descricao;
        });
      });
      setWebSocketErrors(wsErrorsByRoom);
      // Removido o scroll/focus daqui
    },
    []
  );
  useValidationErrorListener(handleWebSocketValidationError);

  // Limpa erro do WebSocket ao digitar
  const clearWebSocketError = useCallback(
    (fieldName: string, roomIndex: number) => {
      setWebSocketErrors((prev) => {
        const newErrors = { ...prev };
        if (newErrors[roomIndex]) {
          delete newErrors[roomIndex][fieldName];
          if (Object.keys(newErrors[roomIndex]).length === 0) {
            delete newErrors[roomIndex];
          }
        }
        return newErrors;
      });
    },
    []
  );

  // Limpa erro do WebSocket e do Yup ao digitar
  const clearAllFieldErrors = useCallback(
    (fieldName: string, roomIndex: number, guestIndex?: number) => {
      // Limpa erro do WebSocket
      setWebSocketErrors((prev) => {
        const newErrors = { ...prev };
        if (newErrors[roomIndex]) {
          delete newErrors[roomIndex][fieldName];
          if (Object.keys(newErrors[roomIndex]).length === 0) {
            delete newErrors[roomIndex];
          }
        }
        return newErrors;
      });
      // Limpa erro do Yup
      setErrors((prev) => {
        return prev.map((roomErr) => {
          if (roomErr.index !== roomIndex) return roomErr;
          return {
            ...roomErr,
            errors: roomErr.errors.filter(
              (err) => err.inputName !== fieldName
            ),
          };
        });
      });
    },
    []
  );

  // Helpers para erro local + WebSocket
  const hasFieldError = useCallback(
    (fieldName: string, roomIndex: number) => {
      const localError = errors[roomIndex]?.errors?.some(
        (error) => error.inputName === fieldName
      );
      const webSocketError = webSocketErrors[roomIndex]?.[fieldName];
      return localError || webSocketError;
    },
    [errors, webSocketErrors]
  );
  const getFieldErrorMessage = useCallback(
    (fieldName: string, roomIndex: number) => {
      const localErrors = errors[roomIndex]?.errors
        ?.filter((error) => error.inputName === fieldName)
        .map((error) => error.description)
        .join(", ");
      const webSocketError = webSocketErrors[roomIndex]?.[fieldName];
      return webSocketError || localErrors || undefined;
    },
    [errors, webSocketErrors]
  );

  // Função genérica para atualização de formulário
  const updateFormArray = <T,>(
    arr: T[],
    index: number,
    updatedField: Partial<T>
  ): T[] => {
    return arr.map((item, i) =>
      i === index ? { ...item, ...updatedField } : item
    );
  };

  const formatDate = (value: Date | string) => {
    moment(value).format("YYYY-MM-DD");
    return value;
  };

  const setMainGuestData = (propName: string, value: any, index: number) => {
    const newValue = propName === "birthday" ? formatDate(value) : value;
    const updatedRooms = updateFormArray(rooms, index, {
      [propName]: newValue,
    });
    setRooms(updatedRooms);
    clearAllFieldErrors(propName, index);
  };
  const setGuestData = (
    propName: string,
    value: any,
    roomIndex: number,
    guestIndex: number
  ) => {
    const newValue = propName === "birthday" ? formatDate(value) : value;
    const updatedRooms = rooms.map((room, index) => {
      if (index === roomIndex) {
        const updatedGuests =
          room.guest?.map((guest, gIndex) =>
            gIndex === guestIndex ? { ...guest, [propName]: newValue } : guest
          ) || [];
        return { ...room, guest: updatedGuests };
      }
      return room;
    });
    setRooms(updatedRooms);
    clearAllFieldErrors(`guest[${guestIndex}].${propName}`, roomIndex, guestIndex);
  };

  const validateForm = async (rooms: Types["IGuest"][]) => {
    try {
      // Schema de validação para um hóspede
      const guestSchema = Yup.object().shape({
        firstName: Yup.string()
          .required(t("form.requiredField"))
          .min(
            2,
            t("form.minLength", { field: t("form.firstName"), length: 2 })
          )
          .max(
            50,
            t("form.maxLength", { field: t("form.firstName"), length: 50 })
          )
          .matches(/^[A-Za-zÀ-ÿ\s]+$/, t("form.onlyLetters")),
        familyName: Yup.string()
          .required(t("form.requiredField"))
          .min(
            2,
            t("form.minLength", { field: t("form.familyName"), length: 2 })
          )
          .max(
            50,
            t("form.maxLength", { field: t("form.familyName"), length: 50 })
          )
          .matches(/^[A-Za-zÀ-ÿ\s]+$/, t("form.onlyLetters")),
        birthday: Yup.date()
          .nullable()
          .typeError(t("form.dateTypeError"))
          .required(t("form.requiredField"))
          .max(new Date(), t("form.birthdayFutureError")),
      });

      // Schema de validação para um quarto
      const roomSchema = Yup.object().shape({
        birthday: Yup.date()
          .required(t("form.requiredField"))
          .typeError(t("form.dateTypeError"))
          .max(new Date(), t("form.birthdayFutureError"))
          .test({
            name: "adult",
            message: t("form.adultOnly"),
            test: function (value) {
              if (!value) return false;
              const today = new Date();
              const birth = new Date(value);
              let age = today.getFullYear() - birth.getFullYear();
              const m = today.getMonth() - birth.getMonth();
              if (m < 0 || (m === 0 && today.getDate() < birth.getDate())) {
                age--;
              }
              return age >= 18;
            },
          }),
        document: Yup.string(),
        email: Yup.string()
          .email(t("form.invalidEmail"))
          .required(t("form.requiredField")),
        guest: Yup.array().of(guestSchema), // Validação do array de hóspedes
        firstName: Yup.string()
          .required(t("form.requiredField"))
          .min(
            2,
            t("form.minLength", { field: t("form.firstName"), length: 2 })
          )
          .max(
            50,
            t("form.maxLength", { field: t("form.firstName"), length: 50 })
          )
          .matches(/^[A-Za-zÀ-ÿ\s]+$/, t("form.onlyLetters")),
        familyName: Yup.string()
          .required(t("form.requiredField"))
          .min(
            2,
            t("form.minLength", { field: t("form.familyName"), length: 2 })
          )
          .max(
            50,
            t("form.maxLength", { field: t("form.familyName"), length: 50 })
          )
          .matches(/^[A-Za-zÀ-ÿ\s]+$/, t("form.onlyLetters")),
        phone: Yup.string()
          .required(t("form.requiredField"))
          .matches(/^\d{10,20}$/, t("form.invalidPhone")),
      });

      let roomsError: { index: number; errors: ErrorItem[] }[] = [];

      // Itera sobre os quartos para validação
      for (let index = 0; index < rooms?.length; index++) {
        const room = rooms[index];

        try {
          // Valida cada quarto individualmente
          await roomSchema.validate(room, { abortEarly: false });
          roomsError.push({ index, errors: [] }); // Sem erros para o quarto
        } catch (error) {
          if (error instanceof Yup.ValidationError) {
            // Coleta erros
            let tmp: ErrorItem[] = error.inner.map((erro) => ({
              description: erro.message,
              inputName: erro.path ? erro.path : "",
            }));
            roomsError.push({ index, errors: tmp }); // Adiciona erros ao quarto
          }
        }
      }

      // Corrige: se algum quarto tem erros, isValid deve ser false
      const hasError = roomsError.some((room) => room.errors.length > 0);

      setErrors(roomsError); // Atualiza o estado com os erros

      console.log("hasError: " + JSON.stringify(hasError));
      return !hasError; // Retorna true se não houver erros
    } catch (error: any) {
      console.error("Erro na validação:", error);
      return false;
    }
  };

  const sendEvent = (event: string) => {
    const customEvent = new CustomEvent(event);
    window.dispatchEvent(customEvent);
  };

  const goToForm = () => {
    const filtered = refs.current.filter(
      (ref) => ref?.current?.className === "form-error"
    );
    if (filtered.length)
      window.scrollTo({
        top: filtered[0].current.offsetTop - 200,
        behavior: "smooth",
      });
  };

  const handleSubmitRef = useRef<() => void>();
  useEffect(() => {
    handleSubmitRef.current = async () => {
      const isValid = await validateForm(rooms);
      if (isValid) {
        sendEvent("validForm");
        console.log("Formulário valido");
      } else {
        sendEvent("invalidForm");
        console.log("Formulário inválido");
      }
    };
  }, [rooms, errors]);
  useEffect(() => {
    const handleValidateForm = () => {
      if (handleSubmitRef.current) {
        handleSubmitRef.current();
      }
    };
    window.addEventListener("validateForm", handleValidateForm);
    return () => {
      window.removeEventListener("validateForm", handleValidateForm);
    };
  });

  useEffect(() => {
    const currentSearch = getSearch();
    setSearch({
      ...currentSearch,
      guestData: rooms,
    });
  }, [rooms]);

  return (
    <Styles.WrapperGuestForm>
      {!!rooms.length && <h2>{t("form.guestData")}</h2>}
      <form>
        {rooms.map((room, index) => (
          <div
            key={index}
            className={errors[index]?.errors.length ? "form-error" : null}
            ref={refs[index]}
          >
            <h3>
              {t("form.mainGuest")} {rooms.length > 1 ? index + 1 : ""}
            </h3>

            <Input
              label={t("form.firstName")}
              name={`room[${index}].firstName`}
              value={room.firstName}
              onChange={(e) =>
                setMainGuestData("firstName", e.target.value, index)
              }
              helper={hasFieldError("firstName", index) ? "error" : undefined}
              helperText={getFieldErrorMessage("firstName", index)}
            />
            <Input
              label={t("form.familyName")}
              name={`room[${index}].familyName`}
              value={room.familyName}
              onChange={(e) =>
                setMainGuestData("familyName", e.target.value, index)
              }
              helper={hasFieldError("familyName", index) ? "error" : undefined}
              helperText={getFieldErrorMessage("familyName", index)}
            />
            <Input
              label={t("form.birthday")}
              name={`room[${index}].birthday`}
              type="date"
              value={
                room.birthday
                  ? typeof room.birthday === "string"
                    ? room.birthday
                    : room.birthday instanceof Date
                    ? room.birthday.toISOString().split("T")[0]
                    : ""
                  : ""
              }
              onChange={(e) =>
                setMainGuestData("birthday", e.target.value, index)
              }
              helper={hasFieldError("birthday", index) ? "error" : undefined}
              helperText={getFieldErrorMessage("birthday", index)}
            />
            <div className="WrapperNumber">
              <Input
                type="number"
                label={t("form.cellPhoneNumber")}
                name={`room[${index}].phone`}
                value={room.phone}
                onChange={(e) =>
                  setMainGuestData("phone", e.target.value, index)
                }
                helper={hasFieldError("phone", index) ? "error" : undefined}
                helperText={getFieldErrorMessage("phone", index)}
              />
            </div>
            <Input
              label={t("form.email")}
              name={`room[${index}].email`}
              value={room.email}
              onChange={(e) => setMainGuestData("email", e.target.value, index)}
              helper={hasFieldError("email", index) ? "error" : undefined}
              helperText={getFieldErrorMessage("email", index)}
            />
            <h2>
              {t("form.guestData")} {index + 1}
            </h2>
            <p>
              {search.rooms[index]?.children > 0
                ? t("form.roomDescriptionWithChildren", {
                    adults: search.rooms[index]?.adults || 0,
                    adultLabel:
                      search.rooms[index]?.adults === 1
                        ? t("form.adultLabelSingular")
                        : t("form.adultLabelPlural"),
                    children: search.rooms[index]?.children || 0,
                    childLabel:
                      search.rooms[index]?.children === 1
                        ? t("form.childLabelSingular")
                        : t("form.childLabelPlural"),
                    childrenAges:
                      search.rooms[index]?.childrenAge
                        ?.map((chd) => chd.age)
                        ?.join(", ") || t("form.noChildren"),
                  })
                : t("form.roomDescriptionWithoutChildren", {
                    adults: search.rooms[index]?.adults || 0,
                    adultLabel:
                      search.rooms[index]?.adults === 1
                        ? t("form.adultLabelSingular")
                        : t("form.adultLabelPlural"),
                  })}
            </p>
            {room.guest?.map((guest, guestIndex) => (
              <div key={guestIndex} className="guest">
                <Input
                  label={t("form.firstName")}
                  name={`room[${index}].guest[${guestIndex}].firstName`}
                  value={guest.firstName}
                  onChange={(e) =>
                    setGuestData("firstName", e.target.value, index, guestIndex)
                  }
                  helper={
                    hasFieldError(`guest[${guestIndex}].firstName`, index)
                      ? "error"
                      : undefined
                  }
                  helperText={getFieldErrorMessage(
                    `guest[${guestIndex}].firstName`,
                    index
                  )}
                />
                <Input
                  label={t("form.familyName")}
                  name={`room[${index}].guest[${guestIndex}].familyName`}
                  value={guest.familyName}
                  onChange={(e) =>
                    setGuestData(
                      "familyName",
                      e.target.value,
                      index,
                      guestIndex
                    )
                  }
                  helper={
                    hasFieldError(`guest[${guestIndex}].familyName`, index)
                      ? "error"
                      : undefined
                  }
                  helperText={getFieldErrorMessage(
                    `guest[${guestIndex}].familyName`,
                    index
                  )}
                />
                <Input
                  label={t("form.birthday")}
                  name={`room[${index}].guest[${guestIndex}].birthday`}
                  type="date"
                  value={
                    guest.birthday
                      ? typeof guest.birthday === "string"
                        ? guest.birthday
                        : guest.birthday instanceof Date
                        ? guest.birthday.toISOString().split("T")[0]
                        : ""
                      : ""
                  }
                  onChange={(e) =>
                    setGuestData("birthday", e.target.value, index, guestIndex)
                  }
                  helper={
                    hasFieldError(`guest[${guestIndex}].birthday`, index)
                      ? "error"
                      : undefined
                  }
                  helperText={getFieldErrorMessage(
                    `guest[${guestIndex}].birthday`,
                    index
                  )}
                />
              </div>
            ))}
          </div>
        ))}
      </form>
    </Styles.WrapperGuestForm>
  );
};

export default GuestForm;
