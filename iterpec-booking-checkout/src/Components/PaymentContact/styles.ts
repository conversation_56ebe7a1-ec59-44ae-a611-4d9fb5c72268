import styled from "styled-components";

export const WrapperGuestForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  & > form {
    display: flex;
    flex-direction: column;
    gap: 10px;
    & > div {
      display: flex;
      flex-direction: column;
      gap: 20px;
      ${(props) =>
        props.theme.breakpoints.desktop(`
          width: 100%;
          flex-direction: row;
          flex-wrap: wrap;
          & > h3 {
            width: 100%
          }
          & > div {
            width: 48%;
          }
        `)}
      ${(props) =>
        props.theme.breakpoints.mobile(`
          & > div.half-width {
            width: 48%;
          }
        `)}
    }
  }

  * {
    font-family: Inter;
  }
  & > h2 {
    color: #000;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
  }
  & > h3 {
    color: #000;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 22px;
  }

  padding: 15px;
`;
