import { Fragment, useEffect, useRef, useState } from "react";
import { Card, Input, Select, Button } from "@iterpecdev/design-sistem";
import { ErrorItem } from "../../Services/types";
import { useNavigate } from "react-router-dom";
import * as Styles from "./styles";
import * as Yup from "yup";
import { IContryCode } from "../../Services/types";
import { generateMockBillingContact } from "../../utils/mockRooms";

import {
  Types,
  setSearch,
  getSearch,
  getHotel,
  // @ts-ignore
} from "@iterpec/booking-utility";
import { useTranslation } from "react-i18next"; // Importando o hook de tradução

// Tipo para o objeto billingContact

const PaymentForm = () => {
  type BillingContact = Types["ISearch"]["payments"][0];
  const search = getSearch();

  const devMode =
    new URLSearchParams(window.location.search).get("devMode") === "true"; // Ativa o mock se devMode=true estiver na URL

  const [billingContact, setBillingContact] = useState<BillingContact>(
    devMode
      ? generateMockBillingContact() // Gera dados mockados
      : {
          given_name: "",
          family_name: "",
          document: "",
          address: {
            line_1: "",
            line_2: "",
            line_3: "",
            city: "",
            state_province_code: "",
            postal_code: "",
            country_code: "BR",
          },
        }
  );

  const handleBillingContactChange = (field: string, value: string) => {
    if (field.startsWith("address.")) {
      const addressField = field.split(".")[1];
      setBillingContact((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]: value,
        },
      }));
    } else {
      setBillingContact((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const [errors, setErrors] = useState<
    { inputName: string; description: string }[]
  >([]); // Ajustado para refletir a estrutura correta
  const refs: React.MutableRefObject<HTMLDivElement>[] = Array.from(
    getSearch().rooms.map(() => useRef(null))
  );

  const { t } = useTranslation(); // Hook de tradução

  const validateForm = async (billingContact: BillingContact) => {
    try {
      const billingContactSchema = Yup.object().shape({
        given_name: Yup.string()
          .required(t("form.requiredField"))
          .min(
            2,
            t("form.minLength", { field: t("form.firstName"), length: 2 })
          )
          .max(
            50,
            t("form.maxLength", { field: t("form.firstName"), length: 50 })
          )
          .matches(/^[A-Za-zÀ-ÿ\s]+$/, t("form.onlyLetters")),
        family_name: Yup.string()
          .required(t("form.requiredField"))
          .min(
            2,
            t("form.minLength", { field: t("form.familyName"), length: 2 })
          )
          .max(
            50,
            t("form.maxLength", { field: t("form.familyName"), length: 50 })
          )
          .matches(/^[A-Za-zÀ-ÿ\s]+$/, t("form.onlyLetters")),
        document: Yup.string()
          .required(t("form.requiredField"))
          .matches(/^(\d{11}|\d{14})$/, t("form.invalidDocument")), // CPF ou CNPJ simples
        address: Yup.object().shape({
          line_1: Yup.string()
            .required(t("form.requiredField"))
            .min(
              3,
              t("form.minLength", { field: t("form.street"), length: 3 })
            ),
          line_2: Yup.string()
            .required(t("form.requiredField"))
            .min(
              1,
              t("form.minLength", { field: t("form.complement"), length: 1 })
            ),
          line_3: Yup.string()
            .required(t("form.requiredField"))
            .min(
              2,
              t("form.minLength", { field: t("form.neighborhood"), length: 2 })
            ),
          city: Yup.string()
            .required(t("form.requiredField"))
            .min(2, t("form.minLength", { field: t("form.city"), length: 2 })),
          state_province_code: Yup.string()
            .required(t("form.requiredField"))
            .length(2, t("form.invalidState"))
            .matches(/^[A-Za-z]{2}$/i, t("form.invalidState")),
          postal_code: Yup.string()
            .required(t("form.requiredField"))
            .matches(/^\d{5}-?\d{3}$/, t("form.invalidPostalCode")),
          country_code: Yup.string()
            .required(t("form.requiredField"))
            .length(2, t("form.invalidCountryCode"))
            .matches(/^[A-Za-z]{2}$/i, t("form.invalidCountryCode")),
        }),
      });

      await billingContactSchema.validate(billingContact, {
        abortEarly: false,
      });
      setErrors([]); // Limpa os erros se a validação passar
      return true; // Retorna true se não houver erros
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        const newErrors = error.inner.map((err) => ({
          inputName: err.path || "",
          description: err.message,
        }));
        setErrors(newErrors); // Atualiza os erros no estado
      }
      return false; // Retorna falso se houver erros no billingContact
    }
  };

  const sendEvent = (event: string) => {
    const customEvent = new CustomEvent(event);
    window.dispatchEvent(customEvent);
  };

  const handleSubmitRef = useRef<() => void>();
  useEffect(() => {
    handleSubmitRef.current = async () => {
      const isValid = await validateForm(billingContact);
      if (isValid) {
        sendEvent("validForm");
      } else {
        sendEvent("invalidForm");
        console.log("Formulário inválido");
      }
    };
  }, [billingContact]);
  useEffect(() => {
    const handleValidateForm = () => {
      if (handleSubmitRef.current) {
        handleSubmitRef.current();
      }
    };
    window.addEventListener("validateForm", handleValidateForm);
    return () => {
      window.removeEventListener("validateForm", handleValidateForm);
    };
  });

  useEffect(() => {
    // Sempre pega o search mais atualizado para não sobrescrever guestData/quartos
    const currentSearch = getSearch();
    setSearch({
      ...currentSearch,
      payments: [billingContact],
    });
  }, [billingContact]);

  return (
    <Styles.WrapperGuestForm>
      <h2>{t("form.billingContact")}</h2>
      <form>
        <div>
          <Input
            label={t("form.firstName")}
            value={billingContact.given_name}
            onChange={(e) =>
              handleBillingContactChange("given_name", e.target.value)
            }
            helper={
              errors.some((error) => error.inputName === "given_name")
                ? "error"
                : undefined
            }
            helperText={
              errors
                .filter((error) => error.inputName === "given_name")
                .map((error) => error.description)
                .join(", ") || undefined
            }
          />
          <Input
            label={t("form.familyName")}
            value={billingContact.family_name}
            onChange={(e) =>
              handleBillingContactChange("family_name", e.target.value)
            }
            helper={
              errors.some((error) => error.inputName === "family_name")
                ? "error"
                : undefined
            }
            helperText={
              errors
                .filter((error) => error.inputName === "family_name")
                .map((error) => error.description)
                .join(", ") || undefined
            }
          />
          <Input
            label={t("form.document")}
            value={billingContact.document}
            onChange={(e) =>
              handleBillingContactChange("document", e.target.value)
            }
            helper={
              errors.some((error) => error.inputName === "document")
                ? "error"
                : undefined
            }
            helperText={
              errors
                .filter((error) => error.inputName === "document")
                .map((error) => error.description)
                .join(", ") || undefined
            }
          />
          <Input
            label={t("form.street")} // ou "form.streetAddress"
            value={billingContact.address.line_1}
            onChange={(e) =>
              handleBillingContactChange("address.line_1", e.target.value)
            }
            helper={
              errors.some((error) => error.inputName === "address.line_1")
                ? "error"
                : undefined
            }
            helperText={
              errors
                .filter((error) => error.inputName === "address.line_1")
                .map((error) => error.description)
                .join(", ") || undefined
            }
          />
          <Input
            className="half-width"
            label={t("form.complement")} // ou "form.addressComplement"
            value={billingContact.address.line_2}
            onChange={(e) =>
              handleBillingContactChange("address.line_2", e.target.value)
            }
            helper={
              errors.some((error) => error.inputName === "address.line_2")
                ? "error"
                : undefined
            }
            helperText={
              errors
                .filter((error) => error.inputName === "address.line_2")
                .map((error) => error.description)
                .join(", ") || undefined
            }
          />
          <Input
            label={t("form.neighborhood")} // ou "form.district"
            className="half-width"
            value={billingContact.address.line_3}
            onChange={(e) =>
              handleBillingContactChange("address.line_3", e.target.value)
            }
            helper={
              errors.some((error) => error.inputName === "address.line_3")
                ? "error"
                : undefined
            }
            helperText={
              errors
                .filter((error) => error.inputName === "address.line_3")
                .map((error) => error.description)
                .join(", ") || undefined
            }
          />
          <Input
            label={t("form.city")}
            value={billingContact.address.city}
            onChange={(e) =>
              handleBillingContactChange("address.city", e.target.value)
            }
            helper={
              errors.some((error) => error.inputName === "address.city")
                ? "error"
                : undefined
            }
            helperText={
              errors
                .filter((error) => error.inputName === "address.city")
                .map((error) => error.description)
                .join(", ") || undefined
            }
          />
          <Input
            label={t("form.stateProvinceCode")}
            value={billingContact.address.state_province_code}
            onChange={(e) =>
              handleBillingContactChange(
                "address.state_province_code",
                e.target.value
              )
            }
            helper={
              errors.some(
                (error) => error.inputName === "address.state_province_code"
              )
                ? "error"
                : undefined
            }
            helperText={
              errors
                .filter(
                  (error) => error.inputName === "address.state_province_code"
                )
                .map((error) => error.description)
                .join(", ") || undefined
            }
          />
          <Input
            label={t("form.postalCode")}
            value={billingContact.address.postal_code}
            onChange={(e) =>
              handleBillingContactChange("address.postal_code", e.target.value)
            }
            helper={
              errors.some((error) => error.inputName === "address.postal_code")
                ? "error"
                : undefined
            }
            helperText={
              errors
                .filter((error) => error.inputName === "address.postal_code")
                .map((error) => error.description)
                .join(", ") || undefined
            }
          />
          <Input label={t("form.countryCode")} value="BR" disabled />
        </div>
      </form>
    </Styles.WrapperGuestForm>
  );
};

export default PaymentForm;
