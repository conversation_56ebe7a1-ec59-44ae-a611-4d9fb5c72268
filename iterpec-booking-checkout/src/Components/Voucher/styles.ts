import styled from "styled-components";

export const Container = styled.div`
  width: 100%;
  font-family: Inter, sans-serif;
  padding: 20px;
  color: #333;
  line-height: 190%;
`;

export const Top = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  & > svg {
    width: 150px;
  }
`;

export const Wrapper = styled.div`
  margin: auto;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
`;
export const WrapperFooter = styled.div`
  & > p {
    font-size: 14px;
    line-height: 150%;
  }
  svg {
    margin: 15px 0;
  }
`;

export const HeaderImage = styled.img`
  width: 100%;
  object-fit: cover;
  overflow: hidden;
  height: auto;
  max-height: 400px;
`;

export const Content = styled.div`
  padding: 20px;
`;

export const Section = styled.div`
  margin-top: 30px;

  h2 {
    color: #00796b;
    margin-bottom: 10px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
  }
`;

export const StyledTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  & > tbody > div {
    padding: 0 20px;
  }
`;

export const TableRow = styled.tr`
  ${({ theme }) =>
    theme.breakpoints.mobile(`
      justify-content: space-between;
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      td {
        width: auto;
        font-size: 14px;
      }
  `)}
  td {
    padding: 6px 0;
    vertical-align: top;
  }
`;

export const Label = styled.td`
  font-weight: bold;
  color: #555;
  width: 240px;
  ${({ theme }) =>
    theme.breakpoints.mobile(`
    width: 50%;
  `)}
`;

export const Footer = styled.div`
  background: #fafafa;
  padding: 20px;
  font-size: 12px;
  color: #777;
  text-align: center;
  border-top: 1px solid #eee;
`;

export const Highlight = styled.span`
  font-weight: bold;
  color: #000;
`;

export const Canceled = styled.span`
  color: #c62828;
  font-weight: bold;
`;

export const Refundable = styled.span`
  color: #2e7d32;
  font-weight: bold;
`;
