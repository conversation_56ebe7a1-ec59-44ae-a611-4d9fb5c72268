import React, { Fragment, useCallback, useMemo } from "react";
import {
  Container,
  Wrapper,
  HeaderImage,
  Content,
  Section,
  StyledTable,
  TableRow,
  Label,
  Footer,
  Highlight,
  Canceled,
  Refundable,
  Top,
  WrapperFooter,
} from "./styles";
import { IItinerary, IRetrive, IStaticData } from "../../Services/types";
import moment from "moment";
import { Logo } from "@iterpecdev/design-sistem";
//@ts-ignore
import { getHotel, getSearch, getUser, Hooks } from "@iterpec/booking-utility";
import { useTranslation } from "react-i18next";
import { Static } from "../../Services/types/getStaticData";
import { translateStatus } from "../../utils/translateStatus";

const BookingConfirmation = ({
  booking,
  imagem,
}: {
  booking: {
    static: Static["property"];
    retrive: IItinerary;
  };
  imagem: string;
}) => {
  const user = getUser();
  const { t } = useTranslation();

  const { useCurrency } = Hooks;
  const {
    nights,
    totalPrice,
    averagePerNight,
    taxesAndFees,
    mandatoryFee,
    resortFee,
    exclusive,
    totalExtraFees,
    grandTotal,
    resortFeesRequestPricePerNight,
  } = useMemo(() => {
    const stays = booking?.retrive?.stays || [];

    const nights =
      stays.length > 0
        ? moment(stays[0].checkOut).diff(stays[0].checkIn, "days") || 1
        : 1;

    const totalPrice = stays.reduce(
      (sum, s) => sum + s.rate?.totalPriceInclusive?.value,
      0
    );
    const averagePerNight = stays.reduce(
      (sum, s) => sum + s.rate?.averagePerNight?.value,
      0
    );
    const exclusive = stays.reduce(
      (sum, s) => sum + s.rate?.totalPriceExclusive?.value,
      0
    );
    const taxesAndFees = totalPrice - exclusive;

    const resortFeesRequestPricePerNight = stays.reduce(
      (sum, s) => sum + s.rate?.totalPriceExclusive?.value,
      0
    );
    // hotel?.rate?.resortFeesRequest?.pricePerNight.value ?? null;
    // Cálculo correto dos campos, igual ao Payment
    const mandatoryFee =
      stays.reduce((sum, s) => {
        // Considera mandatoryFeesRequest OU mandatoryTaxRequest
        const feeValue =
          s.rate?.mandatoryTaxRequest?.priceTotal?.value ??
          s.rate?.mandatoryTaxRequest?.value;
        return sum + (typeof feeValue === "number" ? feeValue : 0);
      }, 0) || null;
    const resortFee =
      stays.reduce((sum, s) => {
        const v = s?.rate?.resortFeesRequest?.value;
        return sum + (typeof v === "number" ? v : 0);
      }, 0) || null;

    console.log(resortFee);

    const totalExtraFees = [mandatoryFee, resortFee]
      .filter((v): v is number => v !== null)
      .reduce((sum, v) => sum + v, 0);

    const grandTotal = Math.round((totalPrice + totalExtraFees) * 100) / 100;

    return {
      nights,
      totalPrice,
      exclusive,
      averagePerNight,
      taxesAndFees,
      mandatoryFee,
      resortFee,
      totalExtraFees,
      grandTotal,
      resortFeesRequestPricePerNight,
    };
  }, [booking]);
  // Calcular a soma das taxas locais (mandatoryFeesRequest/mandatoryTaxRequest + resortFeesRequest) de todos os stays, ignorando valores negativos
  const totalLocalTaxes = useMemo(() => {
    return booking?.retrive?.stays?.reduce((sum, s) => {
      let value = 0;
      // Considera mandatoryFeesRequest OU mandatoryTaxRequest
      if (
        s.rate?.mandatoryFeesRequest &&
        typeof s.rate.mandatoryFeesRequest.value === "number"
      ) {
        value += s.rate.mandatoryFeesRequest.value;
      } else if (
        s.rate?.mandatoryTaxRequest &&
        typeof s.rate.mandatoryTaxRequest.priceTotal?.value === "number"
      ) {
        value += s.rate.mandatoryTaxRequest.priceTotal.value;
      }
      if (
        s.rate?.resortFeesRequest &&
        typeof s.rate.resortFeesRequest.priceTotal?.value === "number"
      ) {
        value += s.rate.resortFeesRequest.priceTotal.value;
      }
      return sum + (value > 0 ? value : 0);
    }, 0);
  }, [booking]);
  return (
    <Container>
      <Wrapper>
        <Top>
          <Logo />
        </Top>
        <HeaderImage src={imagem} alt="Imagem do Hotel" />

        <Content>
          <h2>{t("voucher.confirmation", { hotel: booking.static.name })}</h2>
          <p>
            {t("voucher.greeting", {
              firstName: user.name,
              lastName: user.surname,
            })}
          </p>
          <p>{t("voucher.summary", { hotel: booking.static.name })}</p>
          <Section>
            <h2>{t("voucher.details")}</h2>
            <StyledTable>
              <tbody>
                <TableRow>
                  <Label>{t("voucher.reservationCode")}</Label>
                  <td>{booking.retrive.itineraryNumber}</td>
                </TableRow>
                <TableRow>
                  <Label>{t("cardTrip.reservationStatus")}</Label>
                  <td>{translateStatus(booking.retrive.status, t)}</td>
                </TableRow>
                <TableRow>
                  <Label>{t("voucher.reservationDate")}</Label>
                  <td>
                    {moment(booking.retrive.creationDate).format("DD/MM/YYYY")}
                  </td>
                </TableRow>
              </tbody>
            </StyledTable>
          </Section>
          <Section>
            <h2>{t("checkout.knowBeforeYouGo")}</h2>
            <StyledTable>
              <tbody>
                <div
                  dangerouslySetInnerHTML={{
                    __html: booking.static?.checkin?.instructions,
                  }}
                />
                <div
                  dangerouslySetInnerHTML={{
                    __html: booking.static?.checkin?.specialInstructions,
                  }}
                />{" "}
                <div
                  dangerouslySetInnerHTML={{
                    __html: booking.static?.policies?.knowBeforeYouGo,
                  }}
                />
              </tbody>
            </StyledTable>
          </Section>

          {booking?.retrive?.stays?.map((room, idx) => {
            const staticRoom = booking.static.rooms.find(
              (asRoom) => (asRoom.id = room.providerInfo.roomExpediaId)
            );
            return (
              <React.Fragment key={idx}>
                <Section>
                  <h2>{t("voucher.room", { number: idx + 1 })}</h2>
                  <StyledTable>
                    <tbody>
                      <TableRow>
                        <Label>{t("voucher.name")}</Label>
                        <td>{staticRoom.name}</td>
                      </TableRow>
                      <TableRow>
                        <Label>{t("voucher.beds")}</Label>
                        {<td>{staticRoom?.bedGroups[0]?.description}</td>}
                      </TableRow>
                      <TableRow>
                        <Label>{t("voucher.checkIn")}</Label>
                        <td>
                          {`${moment(room.checkIn).format("DD/MM/YYYY")} ${
                            booking.static?.checkin.beginTime
                          }`}{" "}
                          {t("cardHotel.until")}{" "}
                          {booking.static?.checkin.endTime}
                        </td>
                      </TableRow>
                      <TableRow>
                        <Label>{t("cardHotel.checkOut")}:</Label>
                        <td>
                          {`${moment(room.checkOut).format("DD/MM/YYYY")} 
                           ${t("cardHotel.until")}  ${
                            booking.static?.checkout.time
                          }`}
                        </td>
                      </TableRow>
                      <TableRow>
                        <Label>{t("cardHotel.minAgeCheckin")}:</Label>
                        <td>{booking.static?.checkin.minAge}</td>
                      </TableRow>

                      <TableRow>
                        <Label>Número de Hóspedes:</Label>
                        <td>{room.paxs.length}</td>
                      </TableRow>
                    </tbody>
                  </StyledTable>
                </Section>

                <Section>
                  <h3>Hóspedes do Quarto {idx + 1}</h3>
                  <StyledTable>
                    <tbody>
                      {room.paxs.map((pax, paxIdx) => (
                        <TableRow key={paxIdx}>
                          <Label>Nome:</Label>
                          <td>
                            {pax.firstName} {pax.lastName}
                          </td>
                        </TableRow>
                      ))}
                      <TableRow>
                        <Label>Solicitação Especial:</Label>
                        <td>{"Nenhuma"}</td>
                      </TableRow>
                    </tbody>
                  </StyledTable>
                </Section>

                <Section>
                  <h2>{t("checkout.cancelPolicies")}</h2>
                  <StyledTable>
                    <tbody>
                      <p>
                        {room?.cancelPolicies?.map((penalty, index) =>
                          penalty &&
                          moment(penalty.startDate).isAfter(new Date()) &&
                          moment(penalty?.endDate).isAfter(new Date()) ? (
                            <span className={"refundable"}>
                              <b>{t("cardHotel.freeCancellation")}</b>{" "}
                              {t("cardHotel.until")}{" "}
                              <b>
                                {moment(
                                  moment(penalty?.startDate).subtract(1, "days")
                                ).format("DD/MM/YYYY")}
                              </b>
                            </span>
                          ) : (
                            <span className={"noRefundable"}>
                              <b>{t("cardHotel.nonRefundable")}</b>
                            </span>
                          )
                        )}
                      </p>
                      {room?.cancelPolicies.map((policie) => (
                        <p>{policie.text}</p>
                      ))}
                    </tbody>
                  </StyledTable>
                </Section>
              </React.Fragment>
            );
          })}
          {/* Resumo do valor total da reserva */}
          <Section>
            <h2>{t("voucher.paymentSummary")}</h2>
            <StyledTable>
              <tbody>
                <TableRow>
                  <Label>
                    {`${booking.retrive.stays.length} ${t(
                      "payment.rooms"
                    )} ${nights} ${t("payment.nights")}`}
                  </Label>
                  <td>{useCurrency(exclusive)}</td>
                </TableRow>
                <TableRow>
                  <Label>{t("payment.taxesAndFees")}</Label>
                  <td>{useCurrency(taxesAndFees)}</td>
                </TableRow>
                <TableRow>
                  <Label>{t("payment.paid")}</Label>
                  <td>
                    <b>
                      {useCurrency(
                        booking.retrive.fullPrice.value,
                        booking.retrive.fullPrice.currency
                      )}
                    </b>
                  </td>
                </TableRow>
                {mandatoryFee !== null && (
                  <TableRow>
                    <Label>{t("payment.localTaxesAndFees")}</Label>
                    <td>
                      {useCurrency(mandatoryFee)}{" "}
                      <small>({t("payment.localTaxes")})</small>
                    </td>
                  </TableRow>
                )}
                {resortFee !== null && (
                  <TableRow>
                    <Label>{t("payment.risortTaxesAndFees")}</Label>
                    <td>
                      {useCurrency(resortFee)}{" "}
                      <small>({t("payment.localTaxes")})</small>
                    </td>
                  </TableRow>
                )}
                <TableRow>
                  <Label>{t("payment.total")}</Label>
                  <td>
                    <b>
                      {useCurrency(
                        grandTotal,
                        booking.retrive.fullPrice.currency
                      )}
                    </b>
                  </td>
                </TableRow>
              </tbody>
            </StyledTable>
          </Section>

          <Section>
            <h2>{t("voucher.hotelContact")}</h2>
            <StyledTable>
              <tbody>
                <TableRow>
                  <Label>{t("voucher.hotelName")}</Label>
                  <td>{booking.static.name}</td>
                </TableRow>
                <TableRow>
                  <Label>{t("voucher.hotelPhone")}</Label>
                  {booking.static.phone}
                </TableRow>
                <TableRow>
                  <Label>{t("voucher.hotelAddress")}</Label>
                  <td>
                    {booking?.static?.address && booking?.static
                      ? [
                          booking?.static?.address?.lineOne,
                          booking?.static?.address?.city,
                          booking?.static?.address?.postalCode,
                          booking?.static?.address?.countryCode,
                        ]
                          .filter(Boolean)
                          .join(", ") + "."
                      : null}
                  </td>
                </TableRow>
              </tbody>
            </StyledTable>
          </Section>

          <WrapperFooter>
            <p>{t("voucher.footerMessage")}</p>
            <p>
              {t("voucher.sincerely")}, <br />
              <Logo width={140} />
            </p>
          </WrapperFooter>
        </Content>

        <Footer>
          {/*         Esta é uma mensagem automática. Por favor, não responda este e-mail. */}
        </Footer>
      </Wrapper>
    </Container>
  );
};

export default BookingConfirmation;
