import { <PERSON><PERSON>, <PERSON>er, Skeleton } from "@iterpecdev/design-sistem";
import React, { useEffect, useState } from "react";
import Api from "../../Services";
import {
  IItinerary,
  IRetrive,
  IStaticData,
  roomStatus,
} from "../../Services/types";
import Carousel from "../Carrousel";
import { WrapperDrawer, WrapperTrip } from "./styles";
//@ts-ignore
import { getRegion, Hooks } from "@iterpec/booking-utility";
import moment from "moment";
import { getStatus } from "../../utils";
import { Chatbot } from "../Chatbot";
import { useTranslation } from "react-i18next";
import BookingConfirmation from "../Voucher";
import { BookingConfirmationPDF } from "../VoucherDownload";
import { Static } from "../../Services/types/getStaticData";

const Booking: React.FC<{
  booking: {
    static: Static["property"];
    retrive: IItinerary;
  };
  close?: () => void;
}> = ({ booking, close }) => {
  const api = new Api();
  const { t } = useTranslation();
  const region = getRegion();
  const { useCurrency, useMobileDetect } = Hooks;
  const isMobile = useMobileDetect().isMobile();
  const [mangeBooking, setManageBooking] = useState<boolean>(false);
  const [chat, setHandleChat] = useState<boolean>(false);
  const [staticData, setData] = useState<IStaticData>(null);
  const [open, setOpen] = useState(false);

  const deleteBooking = async () => {
    try {
      const { data } = await api.deleteBooking(booking.retrive.id);
      setOpen(false);
      close();
      console.log(data);
    } catch (error) {
      console.log(error);
    }
  };

  const handleOpen = () => {
    setOpen((old) => !old);
  };

  const handleChat = () => {
    setHandleChat((old) => !old);
  };

  moment.locale(region.language);

  return (
    <WrapperTrip>
      <Button variant="link" onClick={close}>
        {t("booking.back")}
      </Button>
      <Chatbot open={chat} hadleChat={handleChat} />
      <BookingConfirmationPDF booking={booking} />
      <div className="Wrapper">
        <div className="WrapperButtons">
          {mangeBooking ? (
            <>
              <Button onClick={() => setHandleChat((chat) => !chat)}>
                {t("booking.chatSupport")}
              </Button>
              {/* <Button variant="secondary">{t("booking.changeBooking")}</Button> */}
              <Button className="cancel" onClick={handleOpen}>
                {t("booking.cancelBooking")}
              </Button>
              {isMobile && (
                <Button variant="link" onClick={() => setManageBooking(false)}>
                  {t("booking.back")}
                </Button>
              )}
            </>
          ) : (
            <>
              <Button
                variant="secondary"
                onClick={() => setManageBooking(true)}
              >
                {t("booking.manageBooking")}
              </Button>
            </>
          )}
        </div>
      </div>
      <Drawer
        position={"bottom"}
        open={open}
        onDismiss={handleOpen}
        title={t("booking.deleteConfirmationTitle")}
      >
        <WrapperDrawer>
          <Button variant="primary" onClick={deleteBooking}>
            {t("booking.yes")}
          </Button>
          <Button onClick={handleOpen} variant="secondary">
            {t("booking.no")}
          </Button>
        </WrapperDrawer>
      </Drawer>
    </WrapperTrip>
  );
};

export default Booking;
