import styled from "styled-components";

export const WrapperTrip = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  ${({ theme }) =>
    theme.breakpoints.desktop(`
          flex-wrap: wrap;
      `)}
  & > button:first-child {
    width: 50%;
  }
  & > div.Wrapper {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 15px;
    & hr {
      background-color: rgb(204, 204, 204);
    }

    & > div.WrapperRow {
      display: flex;
      gap: 15px;
      & > div.WrapperImage {
        & > img {
          max-width: 130px;
          max-height: 90px;
          border-radius: 6px;
          object-fit: cover;
        }
      }
    }
    & > h2 {
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px; /* 150% */
    }
    & > div.WrapperButtons {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 15px;
      ${(props) =>
        props.theme.breakpoints.desktop(`
        flex-direction: row;
      `)}
      & > button {
        width: 100%;
        ${(props) =>
          props.theme.breakpoints.desktop(`
          width: max-content;
        `)}
        display: flex;
        justify-content: center;
        align-items: center;
        &.cancel {
          background-color: #d35454;
          color: #fff;
        }
      }
    }
    & > div.WrapperLocation {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 5px;
      & > p {
        width: 100%;
        display: flex;
        justify-content: space-between;
        & > b {
          font-size: 13px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 169.231% */
          text-align: right;
          &.norefundable {
            color: #d35454;
          }
          &.refundable {
            color: #a4ce4a;
            font-weight: 600;
          }
        }
        & > span {
          color: #878787;
          text-align: right;
          font-size: 13px;
          font-style: normal;
          font-weight: 500;
          line-height: 22px; /* 169.231% */
        }
      }
    }
    & > div.WrapperDescription {
      width: 100%;
      & > h3 {
        font-size: 13px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 169.231% */
      }
      & > p {
        width: 100%;
        display: flex;
        justify-content: space-between;
        & > b {
          font-size: 13px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 169.231% */
        }
        & > span {
          color: #878787;
          text-align: right;
          font-size: 13px;
          font-style: normal;
          font-weight: 500;
          line-height: 22px; /* 169.231% */
        }
      }
    }
    & > div.WrapperRowExpanded {
      width: 100%;
      max-height: 300px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      & > div > div > div > div > img {
        width: 100%;
        max-height: 300px;

        object-fit: cover;
      }
    }
  }
`;
export const WrapperDrawer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  min-height: 25vh;
  padding-top: 2rem;
  & > button {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }
`;
