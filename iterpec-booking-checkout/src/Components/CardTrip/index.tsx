import { <PERSON><PERSON>, <PERSON>, Divider, Skeleton } from "@iterpecdev/design-sistem";
import React, { useEffect, useMemo, useState } from "react";
import Api from "../../Services";
import {
  IStay,
  IStaticData,
  roomStatus,
  IItinerary,
} from "../../Services/types";
import { Static } from "../../Services/types/getStaticData";
import Carousel from "../Carrousel";
import { WrapperTrip } from "./styles";
//@ts-ignore
import { getRegion, Hooks, Utils } from "@iterpec/booking-utility";
import moment from "moment";
import { getStatus } from "../../utils";
import { translateStatus } from "../../utils/translateStatus";
import { useTranslation } from "react-i18next"; // Importando o hook de tradução
import Stars from "../Stars";
import { AmenitiesIconNoRepeat } from "../AmenitiesIcon/noRepeat";

export const CardTrip: React.FC<{
  hotel: {
    static: Static["property"];
    retrive: IItinerary;
  };
  setExpanded: React.Dispatch<React.SetStateAction<boolean>>;
  expanded: boolean;
}> = ({ hotel, expanded, setExpanded }) => {
  const api = new Api();
  const { useCurrency, useMobileDetect } = Hooks;
  const isMobile = useMobileDetect().isMobile();
  const { cachedImage } = Utils;
  const [mangeBooking, setManageBooking] = useState<boolean>(false);
  const { t } = useTranslation(); // Hook de tradução
  const region = getRegion();

  const handleExpanded = () => setExpanded((state) => !state);

  moment.locale(region.language);
  const heroImage = useMemo(() => {
    const imageUrl = hotel.static?.images?.find((image) => image.heroImage)
      ?.links["1000px"].href;
    return imageUrl;
  }, [hotel.static?.images]);

  return (
    <WrapperTrip>
      <Card className="Wrapper" onClick={handleExpanded}>
        <div className="WrapperRow">
          <div className="WrapperImage">
            <img id="hotelImage" src={cachedImage(heroImage)} />
          </div>
          <div className="WrapperLocation">
            <p>
              <b>
                {t("cardTrip.tripTo", {
                  city: hotel.static?.address.city,
                  hotel: hotel.static?.name,
                })}
              </b>
            </p>
            <p>
              <Stars stars={Number(hotel.static?.ratings.property.rating)} />
            </p>
            <p>
              <span>
                {hotel.retrive?.stays
                  ? moment(hotel.retrive?.stays[0]?.checkIn).format(
                      "DD/MM/YYYY"
                    )
                  : null}{" "}
                {t("cardTrip.until")}{" "}
                {hotel.retrive?.stays
                  ? moment(hotel.retrive?.stays[0]?.checkOut).format(
                      "DD/MM/YYYY"
                    )
                  : null}
              </span>
            </p>
            <p>
              <span>
                {t("cardTrip.reservationNumber")}:{" "}
                {hotel.retrive.stays[0].providerInfo.itineraryExpediaId ??
                  hotel.retrive.itineraryNumber}
              </span>
            </p>
            <p>
              <span>
                {t("cardTrip.reservationStatus")}:{" "}
                {translateStatus(hotel.retrive.status, t)}
              </span>
            </p>
            <p>
              <span>
                {/* Total de quartos e pessoas, internacionalizado */}
                {t("cardTrip.roomsAndPeople", {
                  rooms: hotel.retrive?.stays?.length || 0,
                  people: hotel.retrive?.stays?.reduce(
                    (acc, stay) => acc + stay.adults + stay.children,
                    0
                  ),
                })}
              </span>
            </p>
            {!isMobile && (
              <div className="WrapperButton">
                <Button variant="link">{t("cardTrip.seeMore")}</Button>
              </div>
            )}{" "}
          </div>
          {isMobile && (
            <div className="WrapperButton">
              <Button variant="link">{t("cardTrip.seeMore")}</Button>
            </div>
          )}
        </div>
      </Card>
    </WrapperTrip>
  );
};
