import styled from "styled-components";

export const WrapperTrip = styled.div`
  & > div.Wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 0px;
    overflow: hidden;
    box-shadow: ${(props) => props.theme.shadows.full};
    & hr {
      background-color: rgb(204, 204, 204);
    }
    & > div.WrapperRow {
      display: flex;
      gap: 15px;
      ${({ theme }) =>
        theme.breakpoints.mobile(`
          flex-wrap: wrap;
  
      `)}
      & > div.WrapperButton {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        ${({ theme }) =>
          theme.breakpoints.mobile(`
      
        `)}
      }
      & > div.WrapperLocation {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 5px;
        padding: 14px 16px;
        ${({ theme }) =>
          theme.breakpoints.mobile(`
            gap: 0;
        `)}
        & > p > div.WrapperStar {
          > svg {
            width: 20px;
          }
        }
        & > p {
          width: 100%;
          display: flex;
          justify-content: space-between;

          font-size: 15px;
          & > b {
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 169.231% */
            ${({ theme }) =>
              theme.breakpoints.desktop(`
                font-size: 1.3rem;
            `)}
            &.norefundable {
              color: #d35454;
            }
            &.refundable {
              color: #a4ce4a;
            }
          }
          & > span {
            color: #878787;
            text-align: right;
            font-size: 13px;
            ${({ theme }) =>
              theme.breakpoints.desktop(`
                font-size: 1rem;
            `)}
            font-style: normal;
            font-weight: 500;
            line-height: 22px; /* 169.231% */
          }
        }
      }
      & > div.WrapperImage {
        & > img {
          max-width: 110%;
          margin-top: -5px;
          border-radius: 6px;
          object-fit: cover;
          ${({ theme }) =>
            theme.breakpoints.desktop(`
            max-height: 100%;
            max-width: 420px;
        `)}
        }
      }
    }
    & > h2 {
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px; /* 150% */
    }
    & > div.WrapperButtons {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 15px;
      & > button {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        &.cancel {
          background-color: #d35454;
          color: #fff;
        }
      }
    }

    & > div.WrapperDescription {
      width: 100%;
      & > h3 {
        font-size: 13px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 169.231% */
      }
      & > p {
        width: 100%;
        display: flex;
        justify-content: space-between;
        & > b {
          font-size: 13px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 169.231% */
        }
        & > span {
          color: #878787;
          text-align: right;
          font-size: 13px;
          font-style: normal;
          font-weight: 500;
          line-height: 22px; /* 169.231% */
        }
      }
    }
    & > div.WrapperRowExpanded {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 10px;
      & > div > div > div > div > img {
        width: 100%;
        max-height: 40vh;
        object-fit: cover;
      }
    }
  }
`;
