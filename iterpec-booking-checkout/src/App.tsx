import { Provider } from "@iterpecdev/theme-system";
import { Routes } from "./Routes";
import { BrowserRouter } from "react-router-dom";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Hooks,
  // @ts-ignore
} from "@iterpec/booking-utility";

const App: React.FC = () => {
  const { i18n, t } = useTranslation();
  const useTheme = Hooks.useTheme();

  useEffect(() => {
    window.addEventListener(
      "languageChange",
      (customEvent: CustomEvent<{ language: string }>) => {
        i18n.changeLanguage(customEvent?.detail?.language);
      }
    );
  }, []);

  return (
    <Provider theme={useTheme}>
      <BrowserRouter>
        <Routes />
      </BrowserRouter>
    </Provider>
  );
};

export default App;
