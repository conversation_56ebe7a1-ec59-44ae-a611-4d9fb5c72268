import { useEffect, useCallback } from 'react';
import { eventEmitter, EVENT_TYPES } from '../utils/eventEmitter';
import type { 
  WebSocketValidationErrorData, 
  WebSocketBookingSuccessData, 
  WebSocketBookingErrorData 
} from '../utils/eventEmitter';

// Hook personalizado para escutar eventos do WebSocket
export const useWebSocketEvents = () => {
  // Listener para erros de validação
  const onValidationError = useCallback((callback: (data: WebSocketValidationErrorData) => void) => {
    eventEmitter.on(EVENT_TYPES.WEBSOCKET_VALIDATION_ERROR, callback);
    
    // Retorna função para remover o listener
    return () => {
      eventEmitter.off(EVENT_TYPES.WEBSOCKET_VALIDATION_ERROR, callback);
    };
  }, []);

  // Listener para sucesso de booking
  const onBookingSuccess = useCallback((callback: (data: WebSocketBookingSuccessData) => void) => {
    eventEmitter.on(EVENT_TYPES.WEBSOCKET_BOOKING_SUCCESS, callback);
    
    // Retorna função para remover o listener
    return () => {
      eventEmitter.off(EVENT_TYPES.WEBSOCKET_BOOKING_SUCCESS, callback);
    };
  }, []);

  // Listener para erros de booking
  const onBookingError = useCallback((callback: (data: WebSocketBookingErrorData) => void) => {
    eventEmitter.on(EVENT_TYPES.WEBSOCKET_BOOKING_ERROR, callback);
    
    // Retorna função para remover o listener
    return () => {
      eventEmitter.off(EVENT_TYPES.WEBSOCKET_BOOKING_ERROR, callback);
    };
  }, []);

  return {
    onValidationError,
    onBookingSuccess,
    onBookingError
  };
};

// Hook para componentes que precisam escutar apenas erros de validação
export const useValidationErrorListener = (callback: (data: WebSocketValidationErrorData) => void) => {
  useEffect(() => {
    const unsubscribe = eventEmitter.on(EVENT_TYPES.WEBSOCKET_VALIDATION_ERROR, callback);
    
    return () => {
      eventEmitter.off(EVENT_TYPES.WEBSOCKET_VALIDATION_ERROR, callback);
    };
  }, [callback]);
};

// Hook para componentes que precisam escutar apenas sucesso de booking
export const useBookingSuccessListener = (callback: (data: WebSocketBookingSuccessData) => void) => {
  useEffect(() => {
    eventEmitter.on(EVENT_TYPES.WEBSOCKET_BOOKING_SUCCESS, callback);
    
    return () => {
      eventEmitter.off(EVENT_TYPES.WEBSOCKET_BOOKING_SUCCESS, callback);
    };
  }, [callback]);
};

// Hook para componentes que precisam escutar apenas erros de booking
export const useBookingErrorListener = (callback: (data: WebSocketBookingErrorData) => void) => {
  useEffect(() => {
    eventEmitter.on(EVENT_TYPES.WEBSOCKET_BOOKING_ERROR, callback);
    
    return () => {
      eventEmitter.off(EVENT_TYPES.WEBSOCKET_BOOKING_ERROR, callback);
    };
  }, [callback]);
};
