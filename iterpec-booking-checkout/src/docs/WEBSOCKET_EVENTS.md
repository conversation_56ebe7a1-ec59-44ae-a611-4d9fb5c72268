# Sistema de Eventos WebSocket

Este sistema permite que diferentes componentes da aplicação escutem e reajam a eventos do WebSocket de forma desacoplada.

## Tipos de Eventos Disponíveis

### 1. `WEBSOCKET_VALIDATION_ERROR`
Emitido quando há erros de validação de dados dos hóspedes.

**Estrutura de dados:**
```typescript
{
  message: string;
  errorStatus: "VALIDATION_ERROR";
  statusCode: 400;
  extra: {
    validationErrors: Array<{
      index: number; // Índice do quarto com erro
      error: Array<{
        descricao: string; // Descrição do erro
        inputName: string; // Nome do campo com erro
      }>;
    }>;
  };
}
```

### 2. `WEBSOCKET_BOOKING_SUCCESS`
Emitido quando a reserva é bem-sucedida ou contém dados do PIX.

**Estrutura de dados:**
```typescript
{
  status?: "PAID" | "PIX_PENDING";
  emv?: string; // Código PIX
  location?: {
    url: string; // URL do QR Code
  };
}
```

### 3. `WEBSOCKET_BOOKING_ERROR`
Emitido quando há erros gerais na reserva.

**Estrutura de dados:**
```typescript
{
  message: string;
  errorStatus: string;
  statusCode: number;
  extra?: any;
}
```

## Como Usar

### 1. Usando Hooks Individuais

```typescript
import { useValidationErrorListener } from '../hooks/useWebSocketEvents';

const MyComponent = () => {
  const handleValidationError = useCallback((data) => {
    console.log('Erro de validação:', data);
    // Tratar erro de validação aqui
  }, []);

  useValidationErrorListener(handleValidationError);

  return <div>...</div>;
};
```

### 2. Usando o Hook Principal

```typescript
import { useWebSocketEvents } from '../hooks/useWebSocketEvents';

const MyComponent = () => {
  const { onValidationError, onBookingSuccess, onBookingError } = useWebSocketEvents();

  useEffect(() => {
    // Escutar erros de validação
    const unsubscribeValidation = onValidationError((data) => {
      console.log('Erro de validação:', data);
    });

    // Escutar sucesso de reserva
    const unsubscribeSuccess = onBookingSuccess((data) => {
      console.log('Reserva bem-sucedida:', data);
    });

    // Escutar erros de reserva
    const unsubscribeError = onBookingError((data) => {
      console.log('Erro na reserva:', data);
    });

    // Limpar listeners ao desmontar
    return () => {
      unsubscribeValidation();
      unsubscribeSuccess();
      unsubscribeError();
    };
  }, [onValidationError, onBookingSuccess, onBookingError]);

  return <div>...</div>;
};
```

### 3. Usando o EventEmitter Diretamente

```typescript
import { eventEmitter, EVENT_TYPES } from '../utils';

// Escutar evento
eventEmitter.on(EVENT_TYPES.WEBSOCKET_VALIDATION_ERROR, (data) => {
  console.log('Erro de validação:', data);
});

// Emitir evento (normalmente feito no componente WebSocket)
eventEmitter.emit(EVENT_TYPES.WEBSOCKET_VALIDATION_ERROR, errorData);

// Remover listener
eventEmitter.off(EVENT_TYPES.WEBSOCKET_VALIDATION_ERROR, callback);
```

## Exemplo Prático: Componente de Notificação

O componente `ValidationErrorNotification` demonstra como usar o sistema para mostrar erros de validação:

```typescript
import ValidationErrorNotification from '../Components/ValidationErrorNotification';

const App = () => {
  return (
    <div>
      {/* Outros componentes */}
      <ValidationErrorNotification />
    </div>
  );
};
```

## Exemplo de Uso em um Formulário

```typescript
import { useValidationErrorListener } from '../hooks/useWebSocketEvents';

const GuestForm = () => {
  const [fieldErrors, setFieldErrors] = useState({});

  const handleValidationError = useCallback((data) => {
    // Mapear erros para campos específicos
    const errors = {};
    data.extra.validationErrors.forEach((validation) => {
      validation.error.forEach((error) => {
        errors[error.inputName] = error.descricao;
      });
    });
    setFieldErrors(errors);
  }, []);

  useValidationErrorListener(handleValidationError);

  return (
    <form>
      {/* Campos do formulário com erros */}
      <input name="guest[0].birthday" />
      {fieldErrors['guest[0].birthday'] && (
        <span className="error">{fieldErrors['guest[0].birthday']}</span>
      )}
    </form>
  );
};
```

## Benefícios

1. **Desacoplamento**: Componentes não precisam conhecer uns aos outros
2. **Flexibilidade**: Múltiplos componentes podem escutar o mesmo evento
3. **Manutenibilidade**: Facilita a adição de novos listeners
4. **Tipagem**: TypeScript garante tipos seguros para os dados dos eventos
5. **Performance**: Eventos são emitidos apenas quando necessário
