{"payment": {"title": "Payment", "total": "Total", "discount": "discount", "rooms": "rooms for", "averagePerNight": "average per night", "averagePerNightMult": "average per night, per room", "nights": "nights", "taxesAndFees": "Taxes and fees", "taxesAndFeesDescrption": "The taxes are tax recovery charges paid to vendors (e.g. hotels); for details, please see our  Terms of Use. Service fees are retained as compensation in servicing your booking and may include fees charged by vendors.", "localTaxesAndFees": "Local taxes", "localTaxesAndFeesText": "per night, paid at property", "risortTaxesAndFees": "Resort fee", "paid": "Already paid", "payNow": "Pay now", "localTaxes": "Pay at property", "discountCoupon": "Discount coupon", "termsAndConditions": "I have read and accept the cancellation policy, <a target='__blank' href='/terms'>terms</a> of use and privacy policy.", "errorAcceptTerms": "You must accept the terms to continue.", "payWithClaroPay": "Pay with PIX", "drawerTitle": "Description", "drawerDescription": "Rates are shown in {{current_currency}}, with an original value of {{billable_value}}. The property will charge taxes and fees in {{original_currency}}. The amount of taxes and/or charges to be paid at the property will be calculated according to the exchange rate at the time of the transaction, which may vary until the travel date."}, "booking": {"status": "Status", "cancelledOn": "Cancelled on {{date}}", "boughtOn": "Purchased on", "checkIn": "Check-in", "checkOut": "Check-out", "nights": "Nights", "cancellation": "Cancellation", "nonRefundable": "Non-refundable", "refundable": "Refundable until", "bookingNumber": "Booking number", "phoneNumber": "Phone number", "chatSupport": "Chat support", "changeBooking": "Change booking", "cancelBooking": "Cancel booking", "manageBooking": "Manage booking", "back": "Back", "deleteConfirmationTitle": "Are you sure you want to delete?", "yes": "Yes", "no": "No", "guests": "Guests"}, "cardHotel": {"rooms": "Rooms", "beds": "Beds", "checkIn": "Check-in", "checkOut": "Check-out", "cancellation": "Cancellation", "freeCancellation": "Free cancellation", "nonRefundable": "Non-refundable", "until": "until", "option": "Option", "guests": "Guests", "room": "Room", "adult": "adult", "adults": "adults", "child": "child", "children": "children", "and": "and", "nights": "{{count}}x nights", "minAgeCheckin": "Minimum age for check-in"}, "cardTrip": {"tripTo": "Your trip to {{city}} at hotel {{hotel}}", "roomsAndPeople": "{{rooms}} room(s) for {{people}} person(s)", "seeMore": "See more", "until": "until", "imageAlt": "Hotel image", "reservationNumber": "Reservation Number", "reservationStatus": "Reservation status", "status": {"cancelled": "Cancelled", "completed": "Completed", "error": "Error", "hold": "Processing"}}, "checkout": {"reservationSecure": "Secure reservation", "mandatoryFee": "Mandatory fees", "knowBeforeYouGo": "Know before you go", "cancelPolicies": "Cancellation policies", "optionalFee": "Optional fee"}, "form": {"document": "CPF", "addressLine1": "Address", "city": "City", "stateProvinceCode": "State", "billingContact": "Billing contact", "postalCode": "Postal code", "countryCode": "Country", "guestData": "Guest data for room", "mainGuest": "Main guest", "firstName": "First name", "familyName": "Last name", "birthday": "Date of birth", "cellPhoneNumber": "Mobile phone number", "email": "Email", "guest": "Guest", "children": "Children", "fullName": "Full name", "requiredField": "This field is required", "invalidEmail": "Invalid email", "dateTypeError": "Expected a date, but received a different type", "minLength": "{{field}} must be at least {{length}} characters", "maxLength": "{{field}} must be at most {{length}} characters", "onlyLetters": "Use letters only", "invalidPhone": "Invalid phone. Use only numbers, between 10 and 20 digits", "birthdayFutureError": "Date cannot be in the future", "adultOnly": "Main guest must be over 18 years old", "invalidDocument": "Invalid document. Use CPF (11 digits) or CNPJ (14 digits)", "invalidPostalCode": "Invalid postal code. Use the format 00000-000", "invalidState": "Invalid state. Use only 2 letters", "invalidCountryCode": "Invalid country code. Use only 2 letters", "notInformed": "Not informed", "roomDescription": "Room with {{adults}} adults and {{children}} children (ages: {{childrenAges}})", "roomDescriptionWithChildren": "This room accommodates {{adults}} {{adultLabel}} and {{children}} {{childLabel}}.", "roomDescriptionWithoutChildren": "Room with {{adults}} {{adult<PERSON><PERSON><PERSON>}}", "adultLabelSingular": "adult", "adultLabelPlural": "adults", "childLabelSingular": "child", "childLabelPlural": "children"}, "myBookings": {"title": "Bookings", "noBookings": "Nothing here yet, make your <a href=\"/\">booking now</a>", "loading": "Loading bookings..."}, "timer": {"expiresIn": "This offer expires in <b>{{time}}</b>", "expired": "<b>The offer has expired</b>"}, "voucher": {"confirmation": "Booking Confirmation at {{hotel}}", "greeting": "Hello {{firstName}} {{lastName}},", "summary": "Below is the summary of your booking at the hotel {{hotel}}.", "details": "Booking Details", "reservationCode": "Reservation Code:", "reservationDate": "Reservation Date:", "room": "Room {{number}}", "name": "Name:", "beds": "Beds:", "checkIn": "Check-in:", "guests": "Number of Guests:", "specialRequest": "Special Request:", "none": "None", "hotelContact": "Hotel Contact", "hotelName": "Name:", "hotelPhone": "Phone:", "hotelAddress": "Address:", "footerMessage": "We are available for any questions or needs.", "sincerely": "Since<PERSON>y", "paymentSummary": "Payment Summary"}, "websocket": {"validationError": "There was an error filling in the data, please check the fields.", "generalError": "A general error occurred while processing the booking.", "unexpectedError": "{{message, defaultValue: 'Unexpected error'}}"}}