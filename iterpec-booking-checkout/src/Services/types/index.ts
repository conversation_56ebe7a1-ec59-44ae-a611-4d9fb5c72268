import { Static } from "./getStaticData";

export interface IContryCode {
  name: Name;
  tld: string[];
  cca2: string;
  ccn3: string;
  cca3: string;
  cioc: string;
  independent: boolean;
  status: string;
  unMember: boolean;
  currencies: Currencies;
  idd: Idd;
  capital: string[];
  altSpellings: string[];
  region: string;
  subregion: string;
  languages: Languages;
  translations: Translations;
  latlng: number[];
  landlocked: boolean;
  borders: string[];
  area: number;
  demonyms: Demonyms;
  flag: string;
  maps: Maps;
  population: number;
  gini: Gini;
  fifa: string;
  car: Car;
  timezones: string[];
  continents: string[];
  flags: Flags;
  coatOfArms: CoatOfArms;
  startOfWeek: string;
  capitalInfo: CapitalInfo;
  postalCode: PostalCode;
}

// Tipos utilitários compartilhados
export interface IPax {
  firstName: string;
  lastName: string;
  type: string;
  main: boolean;
}
export interface ICancelPolicy {
  text: string | null;
  startDate: string | null;
  endDate: string | null;
}
export interface IPerNight {
  date: string;
  value: number;
  currency: string;
  type: string;
}
export interface CurrencyValue {
  type: string | null;
  value: number | null;
  currency: string | null;
}

/**
 * Represents details for a mandatory tax or resort fee, including display text
 * and price breakdowns.
 * This replaces MandatoryTaxRequest, ResortFeesRequest,
 * MandatoryTaxBilling, ResortFeesBilling.
 */
export interface TaxOrFeeDetail {
  displayText: string;
  priceTotal: CurrencyValue;
  pricePerNight: CurrencyValue;
}

export interface CancelPenalty {
  text: string;
  startDate: string; // Consider using Date type if parsing dates
  endDate: string; // Consider using Date type if parsing dates
}

export interface Nightly {
  date: string;
  charges: Charge[];
}

export interface Charge {
  type: string;
  currency: string;
  value: number;
}

export interface Address {
  line_1: string;
  line_2: string;
  city: string;
  state_province_code: any;
  state_province_name: any;
  postal_code: string;
  country_code: string;
  obfuscation_required: boolean;
}
export interface Name {
  common: string;
  official: string;
  nativeName: NativeName;
}

export interface NativeName {
  spa: Spa;
}

export interface Spa {
  official: string;
  common: string;
}

export interface Currencies {
  GTQ: Gtq;
}

export interface Gtq {
  name: string;
  symbol: string;
}

export interface Idd {
  root: string;
  suffixes: string[];
}

export interface Languages {
  spa: string;
}

export interface Translations {
  ara: deafultTranslate;
  bre: deafultTranslate;
  ces: deafultTranslate;
  cym: deafultTranslate;
  deu: deafultTranslate;
  est: deafultTranslate;
  fin: deafultTranslate;
  fra: deafultTranslate;
  hrv: deafultTranslate;
  hun: deafultTranslate;
  ita: deafultTranslate;
  jpn: deafultTranslate;
  kor: deafultTranslate;
  nld: deafultTranslate;
  per: deafultTranslate;
  pol: deafultTranslate;
  por: deafultTranslate;
  rus: deafultTranslate;
  slk: deafultTranslate;
  spa: deafultTranslate;
  srp: deafultTranslate;
  swe: deafultTranslate;
  tur: deafultTranslate;
  urd: deafultTranslate;
  zho: deafultTranslate;
}
export interface deafultTranslate {
  official: string;
  common: string;
}
export interface Demonyms {
  eng: Eng;
  fra: Fra2;
}

export interface Eng {
  f: string;
  m: string;
}

export interface Fra2 {
  f: string;
  m: string;
}

export interface Maps {
  googleMaps: string;
  openStreetMaps: string;
}

export interface Gini {
  "2014": number;
}

export interface Car {
  signs: string[];
  side: string;
}

export interface Flags {
  png: string;
  svg: string;
  alt: string;
}

export interface CoatOfArms {
  png: string;
  svg: string;
}

export interface CapitalInfo {
  latlng: number[];
}

export interface PostalCode {
  format: string;
  regex: string;
}

export interface IHoldCreateRequest {
  priceCheckId: string;
  partnerId: string;
  createItinerary: {
    affiliate_reference_id?: string;
    hold?: true;
    email: string;
    phone: {
      country_code: string;
      area_code: string;
      number: string;
    };
    rooms: {
      given_name: string;
      family_name: string;
      smoking?: string;
      special_request?: string;
      loyalty_id?: string;
    }[];
    payments: {
      type: string;
      number?: string;
      security_code?: string;
      expiration_month?: string;
      expiration_year?: string;
      billing_contact: {
        given_name: string;
        family_name: string;
        address: {
          line_1?: string;
          line_2?: string;
          line_3?: string;
          city?: string;
          state_province_code?: string;
          postal_code?: string;
          country_code?: string;
        };
      };
      third_party_authentication?: {
        cavv?: string;
        eci?: string;
        three_ds_version?: string;
        ds_transaction_id?: string;
        pa_res_status?: string;
        ve_res_status?: string;
        xid?: string;
        cavv_algorithm?: string;
        ucaf_indicator?: string;
      };
      enrollment_date?: string;
    }[];
    affiliate_metadata?: string;
    tax_registration_number?: string;
    traveler_handling_instructions?: string;
  };
}

export interface ErrorItem {
  description: string;
  inputName: string;
}
export interface IRate {
  refundable: boolean;
  allInclusive: boolean;
  expediaRateCode: string | null;
  totalRound: CurrencyValue;
  perNight: IPerNight[];
  averagePerNight: CurrencyValue;
  totalPriceInclusive: CurrencyValue;
  totalPriceExclusive: CurrencyValue;
  totalPriceInclusiveStrikethrough: CurrencyValue;
  totalPriceExclusiveStrikethrough: CurrencyValue;
  mandatoryFeesRequest: any;
  mandatoryTaxRequest: any;
  resortFeesRequest: any;
  mandatoryFeesBilling: any;
  mandatoryTaxBilling: any;
  resortFeesBilling: any;
  taxes: any;
}
export interface IItinerary {
  id: string;
  status: string;
  itineraryNumber: number;
  userId: string;
  userEmail: string;
  countryCode: string;
  phone: string;
  fullPrice: CurrencyValue;
  creationDate: string;
  stays: IStay[];
}
export interface IStay {
  totalRound: CurrencyValue;
  type: string;
  checkIn: string;
  checkOut: string;
  specialRequest: string;
  adults: number;
  children: number;
  smoking: boolean;
  propertyId: string;
  providerInfo: {
    prefix: string | null;
    roomExpediaId: string | null;
    itineraryExpediaId: string | null;
    roomCangoorooId: string | null;
    serviceCangoorooId: string | null;
    hotelCangoooroId: string | null;
    reservationCangoorooId: string | null;
    providerType: string | null;
    confirmationExpediaId: string | null;
    confirmationPropertyId: string | null;
  };
  rate: IRate;
  paxs: IPax[];
  cancelPolicies: ICancelPolicy[];
}
export interface BookingItem {
  static: Static["property"];
  retrive: IItinerary;
}
export interface TopLevel<t> {
  payload: t;
  errorCode: number;
  errorMessage: string;
  responseTime: number;
}
export enum roomStatus {
  PRICE_CHECKED,
  HELD_BOOKING,
  BOOKED,
  CANCELED,
}
export interface IStaticData {
  id: string;
  rooms: Room[]; // Assuming rooms is an array of any type, adjust as necessary
  name: string;
  phone: string;
  score: number;
  twentyFourHour: string;
  checkinBeginTime: string;
  checkinEndTime: string;
  instructions: string;
  specialInstructions: string;
  checkOutTime: string;
  optionalFee: string;
  knowBeforeYouGo: string;
  idExpedia: string;
  ancestors: Ancestor[];
  city: ICity;
  state: Category;
  country: ICity;
  stars: number;
  guestRatings: GuestRatings;
  address: IAdress;
  category: Category;
  images: Image[];
  location: Location;
  taxId: string;
}
export interface Image {
  heroImage: boolean;
  caption: string;
  links: {
    "350px": string;
    "70px": string;
    "1000px": string;
  };
}

export interface IAdress {
  lineOne: string;
  lineTwo: string;
  lineTree: string;
  city: string;
  state_province_code: string;
  postalCode: string;
  country_code: string;
}

export interface GuestRatings {
  count: number;
  overall: number;
  cleanliness: number;
  service: number;
  comfort: number;
  condition: number;
  location: number;
  neighborhood: number;
  quality: number;
  value: number;
  amenities: number;
  recommendation_percent: number;
}
export interface Category {
  id: string;
  modificationDate: string;
  creationDate: string;
  idExpedia: string;
}

export interface ICity {
  id: string;
  name: string;
}
export interface Ancestor {
  id: string;
  name: string;
  idExpedia: string;
}
export interface IDeleteBooking {
  bookingId: string;
  status: string;
  rooms: {
    serviceId: string;
    expediaConfirmationId: string;
    status: string;
  }[];
}
export interface IRetrive {
  bookingId: string;
  propertyId: string;
  creationDate: Date;
  name: string;
  checkinBeginTime: string;
  checkInDate: string;
  checkOutDate: string;
  checkinEndTime: string;
  instructions: string;
  specialInstructions: string;
  checkOutTime: string;
  optionalFee: string;
  knowBeforeYouGo: string;
  rooms: Room[];
  address: Address;
}
export interface Room {
  serviceId: string;
  roomName: string;
  checkIn: string;
  checkOut: string;
  firstName: string;
  paxs: Pax[];
  lastName: string;
  status: roomStatus | string;
  cancelationDate: Date;
  rates: RateDto[];
}
export interface Pax {
  given_name: string;
  family_name: string;
  birth: Date;
}

export interface RateDto {
  rateId: string;
  bedConfiguration: BedConfiguration[];
  amenities: Amenity[];
  rateDescription: any; // Consider a more specific type if possible
  availbleQuantity: number;
  refundable: boolean;
  allInclusive: boolean;
  totalFeesRequest: CurrencyValue;
  totalFeesBilling: CurrencyValue;
  perNight: CurrencyValue[];
  averagePerNight: CurrencyValue;
  totalRound: CurrencyValue;
  totalPriceInclusive: CurrencyValue;
  totalPriceExclusive: CurrencyValue;
  totalPriceInclusiveStrikethrough: CurrencyValue;
  totalPriceExclusiveStrikethrough: CurrencyValue;
  mandatoryFeesRequest: any; // Consider a more specific type if possible
  mandatoryTaxRequest: TaxOrFeeDetail;
  resortFeesRequest: TaxOrFeeDetail;
  mandatoryFeesBilling: any; // Consider a more specific type if possible
  mandatoryTaxBilling: TaxOrFeeDetail;
  resortFeesBilling: TaxOrFeeDetail;
  cancelPenalties: CancelPenalty[];
  nonRefundableDataRage: any[]; // Consider a more specific type if possible
}

export interface BedConfiguration {
  supplierId: any; // Consider a more specific type if possible (e.g., string)
  description: string;
  token: string;
}

export interface Amenity {
  name: string;
}

/**
 * Represents a value with a currency and an optional type.
 * This replaces TotalFeesRequest, TotalFeesBilling, PerNight, AveragePerNight,
 * TotalRound, TotalPriceInclusive, TotalPriceExclusive,
 * TotalPriceInclusiveStrikethrough, TotalPriceExclusiveStrikethrough,
 * PriceTotal, PricePerNight, PriceTotal2, PricePerNight2, PriceTotal3,
 * PricePerNight3, PriceTotal4, PricePerNight4.
 */
export interface CurrencyValue {
  type: string | null;
  value: number | null;
  currency: string | null;
}

/**
 * Represents details for a mandatory tax or resort fee, including display text
 * and price breakdowns.
 * This replaces MandatoryTaxRequest, ResortFeesRequest,
 * MandatoryTaxBilling, ResortFeesBilling.
 */
export interface TaxOrFeeDetail {
  displayText: string;
  priceTotal: CurrencyValue;
  pricePerNight: CurrencyValue;
}

export interface CancelPenalty {
  text: string;
  startDate: string; // Consider using Date type if parsing dates
  endDate: string; // Consider using Date type if parsing dates
}

export interface Nightly {
  date: string;
  charges: Charge[];
}

export interface Charge {
  type: string;
  currency: string;
  value: number;
}

export interface Address {
  line_1: string;
  line_2: string;
  city: string;
  state_province_code: any;
  state_province_name: any;
  postal_code: string;
  country_code: string;
  obfuscation_required: boolean;
}
export interface Name {
  common: string;
  official: string;
  nativeName: NativeName;
}

export interface NativeName {
  spa: Spa;
}

export interface Spa {
  official: string;
  common: string;
}

export interface Currencies {
  GTQ: Gtq;
}

export interface Gtq {
  name: string;
  symbol: string;
}

export interface Idd {
  root: string;
  suffixes: string[];
}

export interface Languages {
  spa: string;
}

export interface Translations {
  ara: deafultTranslate;
  bre: deafultTranslate;
  ces: deafultTranslate;
  cym: deafultTranslate;
  deu: deafultTranslate;
  est: deafultTranslate;
  fin: deafultTranslate;
  fra: deafultTranslate;
  hrv: deafultTranslate;
  hun: deafultTranslate;
  ita: deafultTranslate;
  jpn: deafultTranslate;
  kor: deafultTranslate;
  nld: deafultTranslate;
  per: deafultTranslate;
  pol: deafultTranslate;
  por: deafultTranslate;
  rus: deafultTranslate;
  slk: deafultTranslate;
  spa: deafultTranslate;
  srp: deafultTranslate;
  swe: deafultTranslate;
  tur: deafultTranslate;
  urd: deafultTranslate;
  zho: deafultTranslate;
}
export interface deafultTranslate {
  official: string;
  common: string;
}
export interface Demonyms {
  eng: Eng;
  fra: Fra2;
}

export interface Eng {
  f: string;
  m: string;
}

export interface Fra2 {
  f: string;
  m: string;
}

export interface Maps {
  googleMaps: string;
  openStreetMaps: string;
}

export interface Gini {
  "2014": number;
}

export interface Car {
  signs: string[];
  side: string;
}

export interface Flags {
  png: string;
  svg: string;
  alt: string;
}

export interface CoatOfArms {
  png: string;
  svg: string;
}

export interface CapitalInfo {
  latlng: number[];
}

export interface PostalCode {
  format: string;
  regex: string;
}

export interface IHoldCreateRequest {
  priceCheckId: string;
  partnerId: string;
  createItinerary: {
    affiliate_reference_id?: string;
    hold?: true;
    email: string;
    phone: {
      country_code: string;
      area_code: string;
      number: string;
    };
    rooms: {
      given_name: string;
      family_name: string;
      smoking?: string;
      special_request?: string;
      loyalty_id?: string;
    }[];
    payments: {
      type: string;
      number?: string;
      security_code?: string;
      expiration_month?: string;
      expiration_year?: string;
      billing_contact: {
        given_name: string;
        family_name: string;
        address: {
          line_1?: string;
          line_2?: string;
          line_3?: string;
          city?: string;
          state_province_code?: string;
          postal_code?: string;
          country_code?: string;
        };
      };
      third_party_authentication?: {
        cavv?: string;
        eci?: string;
        three_ds_version?: string;
        ds_transaction_id?: string;
        pa_res_status?: string;
        ve_res_status?: string;
        xid?: string;
        cavv_algorithm?: string;
        ucaf_indicator?: string;
      };
      enrollment_date?: string;
    }[];
    affiliate_metadata?: string;
    tax_registration_number?: string;
    traveler_handling_instructions?: string;
  };
}

export interface ErrorItem {
  description: string;
  inputName: string;
}
