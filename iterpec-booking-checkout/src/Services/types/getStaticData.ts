export interface Static {
  modificationDate: string;
  id: string;
  idExpedia: string;
  property: Property;
}

export interface Property {
  id: string;
  name: string;
  address: Address;
  ratings: Ratings;
  location: Location;
  phone: string;
  fax: string;
  category: Category;
  rank: number;
  businessModel: BusinessModel;
  checkin: Checkin;
  checkout: Checkout;
  fees: Fees;
  policies: Policies;
  attributes: Attributes;
  amenities: Amenity[];
  images: Image[];
  onsitePayments: OnsitePayments;
  rooms: Room[];
  rates: Rate[];
  descriptions: Descriptions2;
  statistics: Statistic[];
  airports: Airports;
  themes: Theme[];
  brand: Brand;
  spokenLanguages: SpokenLanguage[];
  multiUnit: boolean;
  paymentRegistrationRecommended: boolean;
  supplySource: string;
}

export interface Address {
  lineOne: string;
  city: string;
  stateProvincCode: string;
  stateProvinceName: string;
  postalCode: string;
  countryCode: string;
  obfuscationRequired: boolean;
}

export interface Ratings {
  property: Property2;
  guest: Guest;
}

export interface Property2 {
  rating: string;
  type: string;
}

export interface Guest {
  count: string;
  overall: string;
  cleanliness: string;
  service: string;
  comfort: string;
  condition: string;
  location: string;
  neighborhood: string;
  amenities: string;
}

export interface Location {
  coordinates: Coordinates;
  obfuscationRequired: boolean;
}

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface Category {
  id: string;
  name: string;
}

export interface BusinessModel {
  expediaCollect: boolean;
  propertyCollect: boolean;
}

export interface Checkin {
  beginTime: string;
  endTime: string;
  instructions: string;
  specialInstructions: string;
  minAge: number;
}

export interface Checkout {
  time: string;
}

export interface Fees {
  mandatory: string;
  optional: string;
}

export interface Policies {
  knowBeforeYouGo: string;
}

export interface Attributes {
  general: General[];
  pets: Pet[];
}

export interface General {
  id: string;
  name: string;
  value?: string;
}

export interface Pet {
  id: string;
  name: string;
  value?: string;
}

export interface Amenity {
  id: string;
  name: string;
  categories?: string[];
  value?: string;
}

export interface Image {
  heroImage: boolean;
  category: string;
  links: Links;
  caption: string;
}

export interface Links {
  "350px": N350px;
  "70px": N70px;
  "1000px": N1000px;
}

export interface N350px {
  href: string;
}

export interface N70px {
  href: string;
}

export interface N1000px {
  href: string;
}

export interface OnsitePayments {
  currency: string;
  types: Type[];
}

export interface Type {
  id: string;
  name: string;
}
export interface Room {
  id: string;
  name: string;
  descriptions: Descriptions;
  amenities: Amenity[];
  images: Image[];
  bedGroups: BedGroup[];
  area: Area;
  occupancy: Occupancy;
}

export interface Descriptions {
  overview: string;
}

export interface Amenity {
  id: string;
  name: string;
  categories?: string[];
}

export interface Image {
  heroImage: boolean;
  category: string;
  links: Links;
  caption: string;
}

export interface Links {
  "350px": N350px;
  "70px": N70px;
  "200px": N200px;
  "1000px": N1000px;
}

export interface N350px {
  href: string;
}

export interface N70px {
  href: string;
}

export interface N200px {
  href: string;
}

export interface N1000px {
  href: string;
}

export interface BedGroup {
  id: string;
  description: string;
  configuration: Configuration[];
}

export interface Configuration {
  type: string;
  size: string;
  quantity: string;
}

export interface Area {
  squareMeters: string;
  squareFeet: string;
}

export interface Occupancy {
  maxAllowed: MaxAllowed;
}

export interface MaxAllowed {
  total: number;
  children: number;
  adults: number;
}

export interface Descriptions {
  overview: string;
}

export interface Amenity2 {
  id: string;
  name: string;
  categories?: string[];
  value?: string;
}

export interface Image2 {
  heroImage: boolean;
  category: string;
  links: Links2;
  caption: string;
}

export interface Links2 {
  "350px": N350px2;
  "70px": N70px2;
  "200px": N200px;
  "1000px": N1000px2;
}

export interface N350px2 {
  href: string;
}

export interface N70px2 {
  href: string;
}

export interface N200px {
  href: string;
}

export interface N1000px2 {
  href: string;
}

export interface BedGroup {
  id: string;
  description: string;
  configuration: Configuration[];
}

export interface Configuration {
  type: string;
  size: string;
  quantity: string;
}

export interface Area {
  squareMeters: string;
  squareFeet: string;
}

export interface View {
  id: string;
  name: string;
}

export interface Occupancy {
  maxAllowed: MaxAllowed;
}

export interface MaxAllowed {
  total: number;
  children: number;
  adults: number;
}

export interface Rate {
  id: string;
  amenities: Amenity3[];
  specialOfferDescription?: string;
}

export interface Amenity3 {
  id: string;
  name: string;
  categories?: string[];
}

export interface Descriptions2 {
  amenities: string;
  dining: string;
  businessAmenities: string;
  rooms: string;
  attractions: string;
  location: string;
  headline: string;
}

export interface Statistic {
  id: string;
  name: string;
  value: string;
}

export interface Airports {
  preferred: Preferred;
}

export interface Preferred {
  iataAirportCode: string;
}

export interface Theme {
  id: string;
  name: string;
}

export interface Brand {
  id: string;
  name: string;
}

export interface SpokenLanguage {
  id: string;
  name: string;
}
