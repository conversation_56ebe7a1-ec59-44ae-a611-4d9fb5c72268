import axios, { AxiosRequestHeaders, AxiosInstance, AxiosHeaders } from "axios";
import { setupCache } from "axios-cache-adapter";

import {
  IContryCode,
  IDeleteBooking,
  IHoldCreateRequest,
  IItinerary,
  IRetrive,
  IStaticData,
  TopLevel,
} from "./types";
import {
  getRegion,
  getSearch,
  getUser,
  //@ts-ignore
} from "@iterpec/booking-utility";
import { Static } from "./types/getStaticData";

class Api {
  private expediaApi: AxiosInstance;

  private headers: AxiosRequestHeaders;
  private language: string;
  private searchId: string;
  private token: string;
  private client: string;
  private user: string;

  constructor() {
    /*    const cache = setupCache({
      maxAge: 15 * 60 * 1000, // Cache de 15 minutos
      debug: true, // Para evitar logs extensivos no ambiente de dev
    }); */

    this.language = getRegion()?.language;
    this.searchId = getSearch()?.searchId;
    this.token = getUser()?.accessToken ?? null;
    this.user = getUser()?.idUser ?? null;
    this.client = getUser()?.clientId ?? null;

    // Criando uma instância de AxiosHeaders
    this.headers = new AxiosHeaders();
    this.headers.set("Access-Control-Allow-Origin", "*");
    this.headers.set("Content-Type", "application/json;charset=utf-8");
    this.headers.set("Cache-Control", "public, max-age=900");
    this.headers.set("language", this.language);
    this.headers.set("X-UserId", this.user);
    this.headers.set(
      "x-clientId",
      this.client ? this.client : "127f50b0-9789-451b-b9a0-057a619420b5"
    );
    this.headers.set("crossDomain", "true");
    this.headers.set("Authorization", `Bearer ${this.token}`);

    this.expediaApi = axios.create({
      baseURL: "https://api.travelbi.solutions/",
      timeout: 40000,
      headers: this.headers,
      /*      adapter: cache.adapter, */
    });
  }
  async getCountryCode() {
    return await axios.get<Array<IContryCode>>(
      "https://restcountries.com/v3.1/all"
    );
  }
  async priceCheck(data: {
    propertyId: string;
    rateId: string;
    token: string;
    roomId: string;
  }) {
    return await this.expediaApi.post<{
      priceCheckId: string;
      status: string;
    }>(`/booking/priceCheck`, { ...data, searchId: this.searchId });
  }
  async holdCreate(data: IHoldCreateRequest) {
    return await this.expediaApi.post<{
      toBookingId: string;
      itineraryId: string;
    }>(`/booking/hold`, { ...data, searchId: this.searchId });
  }
  async bookingCreate(data: { toBookingId: string; itineraryId: string }) {
    return await this.expediaApi.post(`/booking/create`, {
      ...data,
      searchId: this.searchId,
    });
  }
  async getRetrive(id: string) {
    return await this.expediaApi.get<{ itineraries: IItinerary[] }>(
      `/booking/retrieve/${id}/${this.language}`
    );
  }
  async getRetriveLog(id: string) {
    return await this.expediaApi.get<{ itineraries: IItinerary[] }>(
      `/booking/retrieve/${id}/${this.language}`
    );
  }
  async deleteBooking(bookingId: string) {
    return await this.expediaApi.delete<IDeleteBooking>(
      `booking/cancel/itineraryId/${bookingId}`
    );
  }
  async getStaticData(propertyId: string) {
    return await this.expediaApi.get<Static>(
      `/static-data/property/` + propertyId
    );
  }
}

export default Api;
