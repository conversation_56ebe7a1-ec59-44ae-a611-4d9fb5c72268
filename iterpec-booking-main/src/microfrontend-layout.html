<single-spa-router>
  <!--

    This is the single-spa Layout Definition for your microfrontends.
    See https://single-spa.js.org/docs/layout-definition/ for more information.

  -->

  <!-- Example layouts you might find helpful:

  <nav>
    <application name="@org/navbar"></application>
  </nav>
  <route path="settings">
    <application name="@org/settings"></application>
  </route>

  -->
  <main>
    <nav>
      <application name="@iterpec/booking-nav-bar"></application>
    </nav>
    <route default>
      <application name="@iterpec/booking-dashboard"></application>
    </route>
    <route path="/search">
      <application name="@iterpec/booking-search"></application>
    </route>
    <route path="/my-settings">
      <application name="@iterpec/booking-dashboard"></application>
    </route>
    <route path="/checkout">
      <application name="@iterpec/booking-checkout"></application>
    </route>
    <route path="/my-bookings">
      <application name="@iterpec/booking-checkout"></application>
    </route>
    <route path="/chat">
      <application name="@iterpec/booking-checkout"></application>
    </route>
  </main>
</single-spa-router>
