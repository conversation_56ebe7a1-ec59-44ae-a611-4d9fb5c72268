  <!DOCTYPE html>
  <html lang="en">
    <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta http-equiv="X-UA-Compatible" content="ie=edge" />
      <title>Iterpec</title>
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
      <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap"
        rel="stylesheet"
      />
      <link
        rel="icon"
        type="image/png"
        sizes="192x192"
        href="/favIcon192x192.png"
      />
      <link rel="icon" type="image/png" sizes="32x32" href="/favIcon32x32.png" />
      <link rel="icon" type="image/png" sizes="96x96" href="/favIcon96x96.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favIcon16x16.png" />

      <script src="https://cdn.jsdelivr.net/npm/react@17.0.1/umd/react.development.min.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/react-dom@17.0.1/umd/react-dom.development.min.js"></script>
      <!--
      Remove this if you only support browsers that support async/await.
      This is needed by babel to share largeish helper code for compiling async/await in older
      browsers. More information at https://github.com/single-spa/create-single-spa/issues/112
    -->
      <script src="https://cdn.jsdelivr.net/npm/regenerator-runtime@0.13.7/runtime.min.js"></script>

      <!--
      This CSP allows any SSL-enabled host and for arbitrary eval(), but you should limit these directives further to increase your app's security.
      Learn more about CSP policies at https://content-security-policy.com/#directive
    -->
      <!--  <meta
        meta
        http-equiv="Content-Security-Policy"
        content="default-src 'self' https: localhost:*; script-src 'unsafe-inline' 'unsafe-eval' https: localhost:*; connect-src https: localhost:* ws://localhost:*; style-src 'unsafe-inline' https:; object-src 'none';"
      /> -->
      <meta name="importmap-type" content="systemjs-importmap" />
      <!-- If you wish to turn off import-map-overrides for specific environments (prod), uncomment the line below -->
      <!-- More info at https://github.com/joeldenning/import-map-overrides/blob/master/docs/configuration.md#domain-list -->
      <!-- <meta name="import-map-overrides-domains" content="denylist:prod.example.com" /> -->

      <!-- Shared dependencies go into this import map. Your shared dependencies must be of one of the following formats:

      1. System.register (preferred when possible) - https://github.com/systemjs/systemjs/blob/master/docs/system-register.md
      2. UMD - https://github.com/umdjs/umd
      3. Global variable

      More information about shared dependencies can be found at https://single-spa.js.org/docs/recommended-setup#sharing-with-import-maps.
    -->
      <script type="systemjs-importmap">
        {
          "imports": {
            "single-spa": "https://cdn.jsdelivr.net/npm/single-spa@5.9.0/lib/system/single-spa.min.js"
          }
        }
      </script>
      <link
        rel="preload"
        href="https://cdn.jsdelivr.net/npm/single-spa@5.9.0/lib/system/single-spa.min.js"
        as="script"
      />
      <link
        rel="preload"
        href="https://d2kv8mz6z68z3e.cloudfront.net/dist/icons-system/bundle-icons-system-1.9.0.js"
        as="script"
      />
      <link
        href="https://cdn.jsdelivr.net/npm/react-date-range@2.0.1/dist/theme/default.min.css"
        rel="stylesheet"
      />

      <!-- Add your organization's prod import map URL to this script's src  -->
      <!-- <script type="systemjs-importmap" src="/importmap.json"></script> -->
      <script type="systemjs-importmap">
        {
          "imports": {
            "single-spa": "https://cdn.jsdelivr.net/npm/single-spa@5.9.0/lib/system/single-spa.min.js",
            "moment": "https://cdn.jsdelivr.net/npm/moment@2.30.1/moment.min.js",
            "toposort": "https://d2kv8mz6z68z3e.cloudfront.net/assets/toposort.umd.js",
            "tiny-case": "https://d2kv8mz6z68z3e.cloudfront.net/assets/tiny-case.umd.js",
            "property-expr": "https://d2kv8mz6z68z3e.cloudfront.net/assets/property-expr-umd.js",
            "yup": "https://d2kv8mz6z68z3e.cloudfront.net/assets/yup.umd.js",
            "locale": "https://cdn.jsdelivr.net/npm/locale@0.1.0/lib/index.min.js",
            "date-fns/locale": "https://cdnjs.cloudflare.com/ajax/libs/date-fns/4.1.0/locale/cdn.min.js",
            "date-fns": "https://cdn.jsdelivr.net/npm/date-fns@4.1.0/cdn.min.js",
            "axios": "https://cdn.jsdelivr.net/npm/axios@1.7.9/dist/axios.min.js",
            "react": "https://cdn.jsdelivr.net/npm/react@17.0.1/umd/react.development.min.js",
            "react-dom": "https://cdn.jsdelivr.net/npm/react-dom@17.0.1/umd/react-dom.development.min.js",
            "styled-components": "https://cdnjs.cloudflare.com/ajax/libs/styled-components/6.1.8/styled-components.min.js",
            "react-is": "https://cdnjs.cloudflare.com/ajax/libs/react-is/17.0.0/umd/react-is.development.min.js",
            "react-draggable": "https://unpkg.com/react-draggable@4.4.5/build/web/react-draggable.min.js",
            "@remix-run/router": "https://unpkg.com/@remix-run/router@1.15.2/dist/router.umd.min.js",
            "react-router": "https://unpkg.com/react-router@6.21.2/dist/umd/react-router.production.min.js",
            "react-router-dom": "https://unpkg.com/react-router-dom@6.21.2/dist/umd/react-router-dom.production.min.js",
            "react-date-range": "https://d2iv02jhykxi93.cloudfront.net/react-date-range.umd.js",
            "@iterpecdev/icons-system": "https://d2kv8mz6z68z3e.cloudfront.net/dist/icons-system/bundle-icons-system-1.11.3.js",
            "@iterpecdev/design-sistem": "https://d2kv8mz6z68z3e.cloudfront.net/dist/design-system/bundle-core-1.6.1.js",
            "@iterpecdev/theme-system": "https://d2kv8mz6z68z3e.cloudfront.net/dist/theme-system/bundle-theme-system-1.5.4.js"
          }
        }
      </script>
      <script type="systemjs-importmap">
        {
          "imports": {
            "@iterpec/root-config": "#{Url.root-config}#",
            "@iterpec/booking-nav-bar": "#{Url.nav-bar}#",
            "@iterpec/booking-dashboard": "#{Url.dashboard}#",
            "@iterpec/booking-search": "#{Url.search}#",
            "@iterpec/booking-checkout": "#{Url.checkout}#",
            "@iterpec/booking-utility": "#{Url.utility}#"
          }
        }
      </script>
      <% if (isLocal) { %>
      <script type="systemjs-importmap">
        {
          "imports": {
            "@iterpec/root-config": "//localhost:9000/iterpec-root-config.js",
            "@iterpec/booking-nav-bar": "//localhost:8001/iterpec-booking-nav-bar.js",
            "@iterpec/booking-dashboard": "//localhost:8080/iterpec-booking-dashboard.js",
            "@iterpec/booking-search": "//localhost:8002/iterpec-booking-search.js",
            "@iterpec/booking-checkout": "//localhost:8013/iterpec-booking-checkout.js",
            "@iterpec/booking-utility": "//localhost:8003/iterpec-booking-utility.js"
          }
        }
      </script>
      <% } %>

      <!--
      If you need to support Angular applications, uncomment the script tag below to ensure only one instance of ZoneJS is loaded
      Learn more about why at https://single-spa.js.org/docs/ecosystem-angular/#zonejs
    -->
      <!-- <script src="https://cdn.jsdelivr.net/npm/zone.js@0.11.3/dist/zone.min.js"></script> -->

      <script src="https://cdn.jsdelivr.net/npm/import-map-overrides@2.2.0/dist/import-map-overrides.js"></script>
      <% if (isLocal) { %>
      <script src="https://cdn.jsdelivr.net/npm/systemjs@6.8.3/dist/system.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/systemjs@6.8.3/dist/extras/amd.js"></script>
      <% } else { %>
      <script src="https://cdn.jsdelivr.net/npm/systemjs@6.8.3/dist/system.min.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/systemjs@6.8.3/dist/extras/amd.min.js"></script>
      <% } %>
    </head>
    <body style="margin: 0px">
      <noscript> You need to enable JavaScript to run this app. </noscript>
      <script>
        System.import("@iterpec/root-config");
      </script>
      <import-map-overrides-full
        show-when-local-storage="devtools"
        dev-libs
      ></import-map-overrides-full>
    </body>
  </html>
