/* import * as ClaroPaySDK from "@claro/sdkclaro";

const claroPay = async (onLaunch, onShow, onHide, onError, onReceivedEvent) =>
  ClaroPaySDK.getInstace(
    "appId",
    (apiKeySource, token, profileInformation, resourceInformation) => {
      console.log("onLaunch");
      onLaunch(apiKeySource, token, profileInformation, resourceInformation);
    },
    (eventName, enventInformation) => {
      console.log("onShow");
      onShow(eventName, enventInformation);
    },
    () => {
      console.log("onHide");
    },
    () => {
      console.log("onError");
    },
    (eventName: string, eventInformation: string) => {
      console.log("onReceivedEvent");
      if (eventName === "ONBACK") {
        window.history.back();
      }
      if (eventName === "otp_response") {
      }
    },
    {}
  );
 */

export {};
