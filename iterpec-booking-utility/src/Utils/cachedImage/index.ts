import { useMemo } from "react";

const WORKER_URL = "https://img.travelbi.solutions/";
// @ts-ignore
const isLocal = window.location.hostname.includes("localhost");

function cachedImage(imageUrl?: string) {
  if (isLocal) {
    return imageUrl; // 🔹 Retorna a URL original se for ambiente local
  }
  // 🔹 Verifica se a URL foi fornecida antes de tentar codificar
  if (!imageUrl) return null;

  try {
    const encodedUrl = btoa(imageUrl); // 🔹 Adiciona um try/catch para evitar erros com caracteres não-ASCII
    const proxiedImageUrl = `${WORKER_URL}?url=${encodeURIComponent(
      encodedUrl
    )}`;
    return proxiedImageUrl;
  } catch (error) {
    console.error("Erro ao codificar URL:", error);
    return null;
  }
}

export default cachedImage;
