// Anything exported from this file is importable by other in-browser modules.

import { Auth } from "./components/Auth";
import * as Hooks from "./Hooks";
import * as Utils from "./Utils";
import * as Validator from "./components/Validator";
import { SearchStore, HotelStore, UserStore, HeaderStore } from "./store";

import { IGuest, ISearch } from "./store/Search/types";
import { IHotel } from "./store/Hotels/types";
import { IUser } from "./store/User/types";
import { HeaderEvent } from "./components/Header";
import RegionStore from "./store/Language";
import { IRegion } from "./store/Language/types";
import { IHeader } from "./store/Header/types";
import { LanguageEvent } from "./components/Language";
import { useNavigate } from "react-router-dom";

export type Types = {
  IHeader: IHeader;
  ISearch: ISearch;
  IUser: IUser;
  IGuest: IGuest;
  IHotel: IHotel;
};

export { Hooks, Validator, Utils };

export function setHeader(props: IHeader) {
  const setHeader = HeaderStore.getState().setHeader;
  HeaderEvent(props);
  setHeader(props);
}
// Header Store
export function getHeader(): IHeader {
  const header = HeaderStore.getState().header;
  return header;
}
// Search Store
export function getSearch(): ISearch {
  const search = SearchStore.getState().search;
  return search;
}
export function subscribeSearch() {
  const search = SearchStore.subscribe;
  return search;
}
export function setSearch(data: ISearch) {
  const setSearch = SearchStore.getState().setSearch;
  setSearch(data);
}

// Hotel Store
export function setHotel(data: IHotel) {
  const setHotel = HotelStore.getState().setHotel;
  setHotel(data);
}

export function getHotel(): IHotel {
  const hotel = HotelStore.getState().hotel;
  return hotel;
}
// Region Store
export function setRegion(data: IRegion) {
  const setRegion = RegionStore.getState().setRegion;
  setRegion(data);
  LanguageEvent(data);
}

export function getRegion(): IRegion {
  const region = RegionStore.getState().region;
  return region;
}

// User Store
export function getUser(): IUser {
  const user = UserStore.getState().user;
  return user;
}

export function setUser(data: IUser) {
  const setUser = UserStore.getState().setUser;
  setUser(data);
}
