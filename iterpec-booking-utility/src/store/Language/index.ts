import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { IRegion } from "./types";

export interface IRegionStore {
  region: IRegion;
  setRegion: (data: IRegion) => void;
}

const DEFAULT_REG = {
  currency: "BRL",
  countryCode: "BR",
  language: "pt-BR",
  languageCode: "pt",
} as IRegion;

export const RegionStore = create<IRegionStore>()(
  persist(
    (set) => ({
      region: DEFAULT_REG,
      setRegion: (data) => set(() => ({ region: data })),
    }),
    {
      name: "RegionStore",
      storage: createJSONStorage(() => sessionStorage), // <==  pay attention
    }
  )
);

export default RegionStore;
