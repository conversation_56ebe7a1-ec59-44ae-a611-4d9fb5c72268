import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { IHotel } from "./types";

export interface IHotelStore {
  hotel: IHotel;
  setHotel: (data: IHotel) => void;
}

export const HotelStore = create<IHotelStore>()(
  persist(
    (set) => ({
      hotel: null,
      setHotel: (data) => set(() => ({ hotel: data })),
    }),
    {
      name: "HotelStore",
      storage: createJSONStorage(() => sessionStorage), // <==  pay attention
    }
  )
);

export default HotelStore;
