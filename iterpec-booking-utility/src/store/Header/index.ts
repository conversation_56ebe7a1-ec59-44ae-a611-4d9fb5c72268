import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { IHeader } from "./types";

export interface IHeaderStore {
  header: IHeader;
  setHeader: (data: IHeader) => void;
}

const DEFAULT_HEADER = {
  $showSearch: false,
  $showMenu: false,
  $showFallback: false,
  $fallback: () => {},
};
export const HeaderStorage = create<IHeaderStore>()(
  persist(
    (set, get) => ({
      header: get()?.header || DEFAULT_HEADER,
      setHeader: (data) => set(() => ({ header: data })),
    }),
    {
      name: "HeaderStore",
      storage: createJSONStorage(() => sessionStorage), // <==  pay attention
    }
  )
);

export default HeaderStorage;
