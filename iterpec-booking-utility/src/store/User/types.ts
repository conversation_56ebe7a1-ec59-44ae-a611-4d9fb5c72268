export interface IUser {
  idAcess: string;
  clientId: string;
  idUser: string;
  username: string;
  email: string;
  phoneNumber: string;
  age: number | null;
  birthDate: string;
  countryCodeISO: string;
  document: IDocument;
  enumTitle: string | null;
  name: string;
  surname: string;
  title: string;
  expiresIn: number;
  tokenType: string;
  groups: string[];
  idToken: string;
  accessToken: string;
  createAt: string;
  refreshToken: string;
}
interface IDocument {
  documentNumber: string;
  documentCountryId: string;
  documentExpirationDate: string;
  documentType: string;
}

export interface Document {
  documentNumber: string;
  documentCountryId: string;
  documentExpirationDate: string;
  documentType: string;
}
