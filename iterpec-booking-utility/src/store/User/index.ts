import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { IUser } from "./types";

export interface IUserStore {
  user: IUser;
  setUser: (data: IUser) => void;
}

const DEFAULT_USER = {} as IUser;

export const UserStore = create<IUserStore>()(
  persist(
    (set, get) => ({
      user: get()?.user || DEFAULT_USER,
      setUser: (data) => set(() => ({ user: data })),
    }),
    {
      name: "UserStore",
      storage: createJSONStorage(() => sessionStorage), // <==  pay attention
    }
  )
);

export default UserStore;
