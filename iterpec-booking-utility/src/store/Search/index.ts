import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { ISearch } from "./types";
import moment from "moment";

export interface ISearchStore {
  search: ISearch;
  setSearch: (data: ISearch) => void;
}

export const DEFAULT_DATE = {
  CHECKIN: moment().add("60", "d").toDate(),
  CHECKOUT: moment().add("65", "d").toDate(),
};

const DEFAULT_ROOM = {
  toWork: false,
  quantityRooms: 1,
  checkIn: DEFAULT_DATE.CHECKIN,
  checkOut: DEFAULT_DATE.CHECKOUT,
  rooms: [{ id: 1, adults: 1, children: 0, childrenAge: [{ age: 0, id: 1 }] }],
} as ISearch;

export const SearchStore = create<ISearchStore>()(
  persist(
    (set, get) => ({
      search: get()?.search || DEFAULT_ROOM,
      setSearch: (data) => set(() => ({ search: data })),
    }),
    {
      name: "SearchStore",
      storage: createJSONStorage(() => sessionStorage), // <==  pay attention
    }
  )
);
export default SearchStore;
