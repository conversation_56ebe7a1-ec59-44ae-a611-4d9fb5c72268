export interface ISearch {
  destiny?: string;
  searchId?: string;
  priceCheckId?: string;
  type?: string;
  destiny_id?: string;
  hotel_id?: string;
  checkIn?: Date;
  checkOut?: Date;
  quantityRooms?: number;
  toWork?: boolean;
  guestData?: IGuest[];
  payments?: BillingContact[];
  rooms?: {
    id?: number;
    adults?: number;
    children?: number;
    childrenAge?: { id: number; age: number }[];
  }[];
}

export type BillingContact = {
  given_name: string;
  document: string;
  family_name: string;
  address: {
    line_1: string;
    line_2?: string;
    line_3?: string;
    city: string;
    state_province_code: string;
    postal_code: string;
    country_code: string;
  };
};

export interface IGuest {
  id?: number;
  firstName?: string;
  familyName?: string;
  birthday?: Date;
  document?: string;
  phone?: string;
  email?: string;
  guest?: {
    firstName?: string;
    familyName?: string;
    birthday?: Date;
  }[];
}

export interface Room {
  images: Image[];
  idExpedia: string;
  name: string;
  rates: Rate[];
}
export interface Rate {
  id: string;
  availableRooms: number;
  amenities: Amenity[];
  occupancyPricing: OccupancyPricing[];
  bedGroups: BedGroup[];
  cancelPenalties: CancelPenalty[];
}
export interface CancelPenalty {
  currency: string;
  start: string;
  end: Date;
  nights?: number;
}

export interface Amenity {
  idExpedia: string;
  id: string;
  name: string;
}
export interface Links {}

export interface BedGroup {
  [x: string]: string | any;
  id: string;
  links: Links;
  checkPriceToken: string;
  description: string;
}
export interface Nightly {
  type: string;
  value: number;
  currency: string;
}
export interface Fees {
  [x: string]: MandatoryTax;
}
export interface MandatoryTax {
  value: number;
  currency: string;
}

export interface Room {
  images: Image[];
  idExpedia: string;
  roomName: string;
  rates: Rate[];
}

export interface IHotel {
  id: string;
  name: string;
  score: number;
  room?: Room;
  mandatoryFee?: string;
  instructions?: string;
  specialInstructions?: string;
  optionalFee?: string;
  knowBeforeYouGo?: string;
  links?: Links;
  stars: number;
  guestRatings: {
    overall: number;
  };
  images?: Image[];
  token?: string;
}
export interface Image {
  heroImage: boolean;
  caption: string;
  links: string;
}

export interface OccupancyPricing {
  adults: number;
  childrenAge: number[];
  nightly: Nightly[][];
  stay: any[];
  totals: Totals;
  fees?: Fees;
  inclusiveStrikethrough: OccupancyPricing[];
}
export interface Totals {
  inclusive: Inclusive;
  exclusive: Exclusive;
  propertyFees?: PropertyFees;
  inclusiveStrikethrough?: PropertyFees;
}
export interface Inclusive {
  value: number;
  currency: string;
}
export interface Exclusive {
  value: number;
  currency: string;
}

export interface PropertyFees {
  value: number;
  currency: string;
}
