import axios, { AxiosRequestHeaders } from "axios";

var headers = {
  "Access-Control-Allow-Origin": "*",
  "Content-Type": "application/json;charset=utf-8",
  "Cache-Control": "no-cache",
  crossDomain: "true",
} as unknown as AxiosRequestHeaders;

const apis = axios.create({
  baseURL: "",
  headers,
});

const api = {
  Exemple: async (guid_account: string) =>
    await apis.get<{}>(`${guid_account}`),
};

export default api;
