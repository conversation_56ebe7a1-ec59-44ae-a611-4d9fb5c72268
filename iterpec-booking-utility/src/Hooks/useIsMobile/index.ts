import { useState, useEffect } from "react";
import { useEventListener, useIsomorphicLayoutEffect, useWindowSize } from "..";
import { IWindowSize } from "../useWindowSize";

export interface IMobileDetect {
  isAndroid: () => boolean;
  isIos: () => boolean;
  isSSR: () => boolean;
  isMobile: () => boolean;
  isDesktop: () => boolean;
}

const getMobileDetect = (userAgent: string): IMobileDetect => {
  const isAndroid = (): boolean => Boolean(userAgent.match(/Android/i));
  const isIos = (): boolean => Boolean(userAgent.match(/iPhone|iPad|iPod/i));
  const isOpera = (): boolean => Boolean(userAgent.match(/Opera Mini/i));
  const isWindows = (): boolean => Boolean(userAgent.match(/IEMobile/i));
  const isSSR = (): boolean => Boolean(userAgent.match(/SSR/i));
  const isMobile = (): boolean =>
    Boolean(
      (window.innerWidth && window.innerWidth <= 720) ||
        isAndroid() ||
        isIos() ||
        isOpera() ||
        isWindows()
    );
  const isDesktop = (): boolean =>
    Boolean(
      (window.innerWidth && window.innerWidth > 720) ||
        (!isMobile() && !isSSR())
    );

  return {
    isMobile,
    isDesktop,
    isAndroid,
    isIos,
    isSSR,
  };
};
const useMobileDetect = () => {
  const userAgent =
    typeof navigator === "undefined" ? "SSR" : navigator.userAgent;

  const mobileDetect = getMobileDetect(userAgent);

  const [isMobile, setIsMobile] = useState(mobileDetect.isMobile()); // Estado inicial para isMobile
  const [isDesktop, setIsDesktop] = useState(mobileDetect.isDesktop()); // Estado inicial para isDesktop

  useEffect(() => {
    let animationFrameId: number;

    const handleResize = () => {
      cancelAnimationFrame(animationFrameId); // Cancela frames anteriores
      animationFrameId = requestAnimationFrame(() => {
        setIsMobile(mobileDetect.isMobile()); // Atualiza isMobile
        setIsDesktop(mobileDetect.isDesktop()); // Atualiza isDesktop
      });
    };

    window.addEventListener("resize", handleResize); // Adiciona listener para resize
    handleResize(); // Inicializa os valores

    return () => {
      window.removeEventListener("resize", handleResize); // Remove listener ao desmontar
      cancelAnimationFrame(animationFrameId); // Cancela qualquer frame pendente
    };
  }, [mobileDetect]);

  return {
    ...mobileDetect,
    isMobile: () => isMobile, // Retorna o estado atualizado de isMobile
    isDesktop: () => isDesktop, // Retorna o estado atualizado de isDesktop
  };
};

export default useMobileDetect;
