import { useMemo } from "react";
import { Types } from "@iterpecdev/theme-system";

const useTheme = (): Types.DefaultTheme["name"] => {
  return useMemo(() => {
    const hostname = window.location.hostname;
    const themes: Record<string, Types.DefaultTheme["name"]> = {
      "homolog.travelbi.solutions": "iterpec",
      "cliente.travelbi.solutions": "generic",
      "volus.travelbi.solutions": "volus",
    };
    return Object.keys(themes).find((key) => hostname.includes(key))
      ? themes[hostname]
      : "iterpec";
  }, []);
};

export default useTheme;
