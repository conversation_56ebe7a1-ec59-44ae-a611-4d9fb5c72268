import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

import { getUser } from "../../iterpec-booking-utility";

// Hook personalizado
const useAuth = () => {
  const userData = getUser();
  const checkAuthToken = () => {
    return !!userData?.accessToken;
  };
  const auth = checkAuthToken();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(
    checkAuthToken()
  );

  useEffect(() => {
    setIsAuthenticated(auth);
  }, []);
  useEffect(() => {
    setIsAuthenticated(auth);
  }, [userData?.accessToken, auth]);
  useEffect(() => {
    // Se não houver um id de usuário e um tiver um token de acesso, redirecione para a página de registro
    if (
      !userData?.idUser &&
      userData?.accessToken &&
      window.location.pathname !== "/singup" &&
      window.location.pathname !== "/login" &&
      window.location.pathname !== "/update-password" &&
      window.location.pathname !== "/forgot-password"
    ) {
      window.location.href = "/singup";
    }
  }, [userData?.accessToken, userData?.idUser, window.location.pathname]);

  return { isAuthenticated };
};

export default useAuth;
