const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react-ts");

module.exports = (webpackConfigEnv, argv) => {
  const defaultConfig = singleSpaDefaults({
    orgName: "iterpec",
    projectName: "booking-utility",
    webpackConfigEnv,
    argv,
  });

  return merge(defaultConfig, {
    // modify the webpack config however you'd like to by adding to this object
    externals: [
      ...defaultConfig.externals,
      "@iterpecdev/design-sistem",
      "@iterpecdev/icons-system",
      "@iterpecdev/theme-system",
      "react",
      "moment",
      "axios",
      "react-dom",
      "yup",
      /^locale($|\/)/,
      /^date-fns($|\/)/, // Usando regex para incluir submódulos
      "styled-components",
      "react-router-dom",
      "axios",
      "react-is",
      "react-date-range",
      "react-draggable",
    ],
  });
};
