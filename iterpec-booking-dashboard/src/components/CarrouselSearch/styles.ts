import styled, { css } from "styled-components";
import { ICarouselSlide } from "./types";

export const SCarouselWrapper = styled.div`
  display: flex;
  overflow: hidden;
`;
export const CarrouselWrapper = styled.div`
  position: relative;
  & > div.buttons {
    display: flex;
    align-items: center;
    background-color: none;
    position: absolute;
    top: 0;
    width: 30%;
    height: 100%;
    & > svg {
      width: 30px;
      fill: white;
    }
    &.Right {
      right: 0;
      justify-content: flex-end;
    }
    &.Left {
      justify-content: flex-sta rt;
      left: 0;
    }
  }
`;

export const SCarouselSlide = styled.div<ICarouselSlide>`
  flex: 0 0 auto;
  opacity: ${(props) => (props.active ? 1 : 0)};
  transition: all 0.5s ease;
  width: 100%;
  height: ${(props) => (props.active ? "auto" : 0)};
`;

interface ICarouselProps {
  currentSlide: number;
}

export const SCarouselSlides = styled.div<ICarouselProps>`
  display: flex;
  width: 100%;
  ${(props) => {
    return (
      props.currentSlide &&
      css`
        transform: translateX(-${props.currentSlide * 100}%);
      `
    );
  }};
  transition: all 0.5s ease;
`;
