import * as React from "react";
import styled, { css } from "styled-components";
import * as Styles from "./styles";
import * as Types from "./types";
import Icon from "@iterpecdev/icons-system";

const Carousel = ({
  children,
  useTimer = false,
  timeOut = 10,
  ...props
}: Types.IProps) => {
  const [currentSlide, setCurrentSlide] = React.useState(0);
  const [timer, setTime] = React.useState(10);
  const activeSlide = children.map((slide, index) => (
    <Styles.SCarouselSlide active={currentSlide === index} key={index}>
      {slide}
    </Styles.SCarouselSlide>
  ));
  React.useEffect(() => {
    const timeout = setTimeout(() => {
      if (useTimer) {
        if (timer) {
          setTime(timer - 1);
        } else {
          setCurrentSlide((currentSlide + 1) % activeSlide.length);
        }
      }
    }, 1000);
    return () => {
      clearTimeout(timeout);
    };
  }, [timer]);
  React.useEffect(() => {
    if (useTimer) {
      setTime(timeOut);
    }
  }, [currentSlide]);
  return (
    <Styles.CarrouselWrapper {...props} className="carrouselWrapper">
      <Styles.SCarouselWrapper>
        <Styles.SCarouselSlides currentSlide={currentSlide}>
          {activeSlide}
        </Styles.SCarouselSlides>
      </Styles.SCarouselWrapper>
      <div
        className="Left buttons"
        onClick={() => {
          setCurrentSlide(
            (currentSlide - 1 + activeSlide.length) % activeSlide.length
          );
        }}
      >
        <Icon name="ChevronLeft" />
      </div>
      <div
        className="Right buttons"
        onClick={() => {
          setCurrentSlide((currentSlide + 1) % activeSlide.length);
        }}
      >
        <Icon name="ChevronRight" />
      </div>
    </Styles.CarrouselWrapper>
  );
};

export default Carousel;
