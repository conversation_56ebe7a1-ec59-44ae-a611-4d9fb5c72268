import React, { Fragment } from "react";
import AmenitiesIcon from ".";
 
import { iconName } from "../../utils/iconName";
import Icon from "@iterpecdev/icons-system";
import { IAmenitie } from "../../Services/types/service";

export const AmenitiesIconNoRepeat: React.FC<{ amenities?: IAmenitie[] }> = ({
  amenities,
}) => {
  let uniqueArray = {};
  amenities?.map((amenitie) => {
    const iconNametext = iconName(amenitie.name);
    if (iconNametext)
      uniqueArray[iconNametext] = (
        <AmenitiesIcon aria-label={amenitie.name} name={amenitie.name} />
      );
  });

  return (
    <Fragment>
      {Object.keys(uniqueArray).map((amenitie) => (
        <Fragment key={amenitie}>{uniqueArray[amenitie]}</Fragment>
      ))}
    </Fragment>
  );
};
