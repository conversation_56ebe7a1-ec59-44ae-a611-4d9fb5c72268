import { useState, useEffect } from "react";
import * as Styles from "./styles";
import Api from "../../Services";
import { Input, <PERSON><PERSON>, Card, Select } from "@iterpecdev/design-sistem";
import {
  setHeader,
  <PERSON><PERSON>,
  setUser,
  getUser,
  // @ts-ignore
} from "@iterpec/booking-utility";
import { useNavigate } from "react-router-dom";
import InputMask from "react-input-mask";
import { obterMascara } from "../../utils/phoneMask";
import { country } from "../../utils/countryISO";
import moment from "moment";

/* {
  "age": 29,
  "birthDate": "30-08-1995",
  "countryCodeISO": "BR",
  "document": {
      "documentNumber": "4093666881",
      "documentCountryId": "BR",
      "documentExpirationDate": "30-08-1995",
      "documentType": "CPF"
  },
  "enumTitle": "dr",
  "name": "Homolog",
  "surname": "Test",
  "title": "DR",
  "email": "<EMAIL>",
  "phoneNumber": "+5511999999999"
} */

const SingUp = () => {
  const { useIsomorphicLayoutEffect } = Hooks;
  const navigate = useNavigate();
  const user = getUser();

  const [formData, setFormData] = useState({
    birthDate: "",
    countryCodeISO: "",
    documentNumber: "",
    documentCountryId: "",
    documentExpirationDate: "",
    documentType: "",
    enumTitle: "",
    name: "",
    surname: "",
    title: "",
    email: "",
    phoneNumber: "",
  }); // Removido estado para age
  const [error, setError] = useState("");
  const [mask, setMask] = useState("");
  const api = new Api();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const sendInvite = async () => {
    setError("");
    setLoading(true);
    try {
      const request = {
        birthDate: moment(formData.birthDate).format("DD-MM-YYYY"),
        countryCodeISO: formData.countryCodeISO,
        document: {
          documentNumber: formData.documentNumber,
          documentCountryId: formData.documentCountryId,
          documentExpirationDate: moment(
            formData.documentExpirationDate
          ).format("DD-MM-YYYY"),
          documentType: formData.documentType,
        },
        enumTitle: formData.enumTitle,
        name: formData.name,
        surname: formData.surname,
        title: formData.title,
        email: formData.email,
        phoneNumber: `+${formData.phoneNumber}`,
      };
      const { data } = await api.registerUser(request); // Enviar todos os dados do formulário
      console.log(data);
      setUser({ ...user, ...data });
      navigate("/");
      setSuccess(true);
    } catch (error) {
      setError(error.message);
    }
    setLoading(false);
  };

  const handleHeader = () => {
    setHeader({
      $showSearch: false,
      $showMenu: false,
      $showFallback: false,
      $fallback: () => {
        navigate(-1);
      },
    });
  };
  useIsomorphicLayoutEffect(() => {
    setTimeout(() => {
      handleHeader();
    }, 100);
    handleHeader();
  }, []);
  useIsomorphicLayoutEffect(() => {
    setMask(obterMascara(formData.phoneNumber));
  }, [formData.phoneNumber.length]); // Adicionado dependência de phoneNumber

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  return (
    <Styles.Wrapper>
      <Card>
        <form>
          <h3>Seus dados </h3>
          <Input
            label="Email"
            type="email"
            name="email"
            value={formData.email}
            placeholder="<EMAIL>"
            onChange={handleChange}
          />
          <InputMask
            mask={mask} // Adicionado máscara de telefone
            value={formData.phoneNumber}
            onChange={(e) =>
              handleChange({
                target: {
                  name: "phoneNumber",
                  value: e.target.value.replace(/\D/g, ""),
                },
              })
            } // Adicionado campo de telefone
            maskChar=" "
          >
            {(inputProps) => (
              <Input
                type="tel"
                label="phoneNumber"
                {...inputProps}
                placeHolder="+55 (11) 99999-9999"
              />
            )}
          </InputMask>
          <Input
            label="First Name"
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
          />
          <Input
            label="Last Name"
            type="text"
            name="surname"
            value={formData.surname}
            onChange={handleChange}
          />
          <Input
            label="Birth Date"
            type="date"
            name="birthDate"
            value={formData.birthDate}
            onChange={handleChange}
          />
          <Select
            label="Country"
            name="countryCodeISO"
            value={formData.countryCodeISO}
            onChange={handleChange}
          >
            {country.map((item) => (
              <option value={item["iso_3166-2"]}>{item.name}</option>
            ))}
          </Select>
          <Input
            label="Document Number"
            type="text"
            name="documentNumber"
            value={formData.documentNumber}
            onChange={handleChange}
          />
          <Input
            label="Document Country ID"
            type="text"
            name="documentCountryId"
            value={formData.documentCountryId}
            onChange={handleChange}
          />
          <Input
            label="Document Expiration Date"
            type="date"
            name="documentExpirationDate"
            value={formData.documentExpirationDate}
            onChange={handleChange}
          />
          <Input
            label="Document Type"
            type="text"
            name="documentType"
            value={formData.documentType}
            onChange={handleChange}
          />
          <Button onClick={sendInvite} disabled={loading} variant="primary">
            Salvar
          </Button>
          {error && <div>{error}</div>}
        </form>
      </Card>
    </Styles.Wrapper>
  );
};

export default SingUp;
