import styled, { css } from "styled-components";

export const Wrapper = styled.div`
  ${({ theme }) => css`
    > * {
      font-family: ${theme.fonts.base};
    }
    > h1 {
      font-size: ${theme.fontSizes.md};
    }
    > form {
      padding: 20px;
      gap: 10px;
      background-color: #f5f5f5;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 15px;
    height: 100%;
    background-color: ${(props) => props.theme.palette.neutral.lighter};

    * {
      font-family: ${(props) => props.theme.fonts.base};
    }
    & > div {
      background-color: ${(props) => props.theme.palette.neutral.white};
      width: 96%;
      > form {
        & > * {
          margin: ${(props) => props.theme.spacings.inset.md} 0;
        }
        & > h3 {
          text-align: center;
        }
        & > p {
          text-align: center;
          color: ${(props) => props.theme.palette.status.error};
        }
        & > div > div > select {
          option {
            word-wrap: break-word;
          }
        }
        display: inline;
        & > button {
          width: 100%;
          text-align: center;
          justify-content: center;
        }
      }
    `}
`;
