import styled from "styled-components";

export const wrapperCardReview = styled.div`
  display: flex;
  gap: 10px;

  & > div.wrapperAvatar {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    & > span {
      text-align: center;
      width: 100%;
      color: #878787;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px; /* 171.429% */
    }
    & > div {
      margin: 0.5rem 0;
    }
  }
  & > div.wrapperReview {
    width: 100%;
    & > span {
      width: 100%;
      color: #878787;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px; /* 171.429% */
    }
    & > h4 {
      color: #000;
      font-family: Inter;
      font-size: 13px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px; /* 184.615% */
    }
    & > div.wrapperButton {
      display: flex;
      align-items: end;
      justify-content: end;
    }
    & > div.wrapperStars {
      & > svg {
        fill: #ffb406;
        width: 20px;
        height: auto;
      }
    }
  }
`;
