import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import Icon from "@iterpecdev/icons-system";

import { useEffect, useState } from "react";
const data = {
  name: "Nizuc Resort and Spa",
  image:
    "https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500",
  review: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nunc
  sem, vestibulum non felis id, hendrerit interdum tortor. Fusce in
  odio in orci varius lacinia eu ut erat. In at ex ut justo
  scelerisque mattis nec eu lacus. Etiam mi ligula, imperdiet non mi
  vel, interdum rutrum lectus. Pellentesque lacinia, turpis eget
  vulputate elementum, turpis ipsum rutrum massa, vitae rhoncus nunc
  neque quis leo. Nulla et diam sit amet magna mattis facilisis. Duis
  auctor interdum rutrum. Aenean a fermentum quam. Vivamus blandit
  tempor nisi vitae molestie. Cras quis mollis turpis. Sed tempus,
  nisl et accumsan fermentum, diam erat consectetur erat, quis
  malesuada tellus eros nec est. Cras diam mauris, tincidunt sit amet
  dolor vel, fringilla gravida mauris. Maecenas ornare lacus id
  scelerisque placerat. Morbi fringilla dapibus felis non tincidunt.
  Vivamus condimentum placerat tellus eu scelerisque. Donec volutpat
  neque id dui ornare, a tempus erat aliquet. Nam placerat rutrum leo,
  a vulputate lorem aliquam posuere. Fusce finibus erat vel imperdiet
  pretium. Integer eget finibus nulla, molestie sagittis libero. `,
};
const CardReview = () => {
  const [expanded, setExpanded] = useState(false);
  const handleExpanded = () => setExpanded(!expanded);
  return (
    <Card>
      <Styles.wrapperCardReview>
        <div className="wrapperAvatar">
          <Avatar
            label="Luiza Mel"
            src={data.image}
            size="lg"
            variant="primary"
          />
          <span>Maria</span>
        </div>
        <div className="wrapperReview">
          <h4>{data.name} </h4>
          <div className="wrapperStars">
            <Icon name="Star" />
            <Icon name="Star" />
            <Icon name="Star" />
            <Icon name="Star" />
            <Icon name="Star" />
          </div>

          <span>
            {expanded ? data.review : data.review.substring(0, 150) + "..."}
          </span>
          <div className="wrapperButton">
        {/*     <Button variant="link" onClick={handleExpanded}>
              {!expanded
                ? text.Button.open["es-MX"]
                : text.Button.close["es-MX"]}
            </Button> */}
          </div>
        </div>
      </Styles.wrapperCardReview>
    </Card>
  );
};
export default CardReview;
