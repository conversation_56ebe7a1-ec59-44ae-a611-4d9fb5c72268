import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Card, Input } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import Api from "../../Services";
import {
  setHeader,
  setUser,
  Hooks,
  // @ts-ignore
} from "@iterpec/booking-utility";
import { useNavigate } from "react-router-dom";
import InputMask from "react-input-mask";

const UpdatePassword = () => {
  const [email, setEmail] = useState("");
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();
  const handleHeader = () => {
    setHeader({
      $showSearch: false,
      $showMenu: true,
      $showFallback: true,
      $fallback: () => {
        navigate(-1);
      },
    });
  };

  useEffect(() => {
    handleHeader();
  }, []);

  const handleSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess(false);

    if (newPassword !== confirmPassword) {
      setError("As senhas não coincidem");
      setLoading(false);
      return;
    }

    const api = new Api();
    try {
      const { status } = await api.updatePassword({
        user: email,
        oldPassword,
        newPassword,
      });
      if (status === 201) {
        setSuccess(true);
        const { data } = await api.login({
          user: email,
          password: newPassword,
        });
        setUser(data);
        navigate("/");
      }
    } catch (error) {
      setError(error.message);
    }
    setLoading(false);
  };

  return (
    <Styles.Wrapper>
      <Card>
        <form>
          <h3>Mudar Senha</h3>
          <Input
            label="Email"
            type="email"
            value={email}
            placeholder="<EMAIL>"
            onChangeValue={(e) => setEmail(e)}
          />
          <Input
            label="Senha antiga"
            type="password"
            value={oldPassword}
            onChangeValue={(e) => setOldPassword(e)}
          />
          <Input
            label="Senha nova"
            type="password"
            value={newPassword}
            onChangeValue={(e) => setNewPassword(e)}
          />
          <Input
            label="Confirme a senha"
            type="password"
            value={confirmPassword}
            onChangeValue={(e) => setConfirmPassword(e)}
          />
          <Button onClick={handleSubmit} disabled={loading} variant="primary">
            Change password
          </Button>
          {error && <div>{error}</div>}
          {success && <div>Password changed successfully!</div>}
        </form>
      </Card>
    </Styles.Wrapper>
  );
};

export default UpdatePassword;
