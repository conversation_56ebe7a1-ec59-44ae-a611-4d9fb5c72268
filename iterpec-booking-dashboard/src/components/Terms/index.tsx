import { useEffect } from "react";
import * as Styles from "./styles";
//@ts-ignore
import { setHeader, getRegion } from "@iterpec/booking-utility";
import { useNavigate } from "react-router-dom";
import Footer from "../Footer";

const Terms = () => {
  const navigate = useNavigate();
  const region = getRegion();

  useEffect(() => {
    setHeader({
      $showSearch: false,
      $showFallback: true,
      $showMenu: true,
      $fallback: () => {
        navigate("/");
      },
    });
  }, []);
  return (
    <>
      <Styles.WrapperTerms>
        <iframe
          src={
            "https://developer.expediapartnersolutions.com/terms/" +
            region.languageCode
          }
        />
      </Styles.WrapperTerms>
      <Footer />
    </>
  );
};
export default Terms;
