import styled, { css } from "styled-components";
export const Container = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 100%;
`;
export const Wrapper = styled.div`
  max-width: 1200px;
  width: 100%;
  gap: 25px;
  & > div {
    display: flex;
    flex-direction: column;
    gap: 15px;
    ${({ theme }) => css`
      padding: 16px 16px;
      display: flex;
      flex-direction: column;
      * {
        font-family: Inter;
      }
      & > h3 {
        color: #232323;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 120%; /* 16px */
      }
      & > h2 {
        color: ${({ theme }) => theme.palette.neutral.black};
        font-family: Inter;
        font-size: 24px;
        font-style: normal;
        font-weight: 900;
        line-height: 24px;
      }
      & > h4 {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        > svg {
          width: 118px;
          height: 23.751px;
        }
      }
    `}
  }
`;

export const WrapperReview = styled.div`
  width: 100%;
  & > div {
    width: 100%;
  }
`;

export const WrapperDestination = styled.div`
  ${({ theme }) => css`
    display: flex;
    width: 104%;
    ${(props) =>
      props.theme.breakpoints.mobile(`
        overflow-x: scroll;
        scroll-behavior: smooth;
    `)}

    gap: 24px;
  `}
`;

export const WrapperFAQ = styled.div`
  ${({ theme }) => css`
    display: flex;
    gap: 13px;
    flex-wrap: wrap;
    ${(props) =>
      props.theme.breakpoints.mobile(`
       flex-direction: column;
    `)}
    & > div {
      width: 47%
      height: min-content;
    }
  `}
`;
