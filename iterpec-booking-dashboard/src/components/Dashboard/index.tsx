import Parcel from "single-spa-react/parcel";
import * as Styles from "./styles";
import Carrousel from "../Carrousel";
import { Accordion, Logo } from "@iterpecdev/design-sistem";
import CardHotel from "../CardHotel";
import CardReview from "../CardReview";
import Footer from "../Footer";
import apis from "../../Services";
import { useEffect, useState } from "react";
import moment from "moment";
import {
  getSearch,
  setHeader,
  setSearch,
  getUser,
  Hooks,
  // @ts-ignore
} from "@iterpec/booking-utility";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Api from "../../Services";

export const Dashboard = () => {
  const { useAuth, useIsomorphicLayoutEffect } = Hooks;
  const navigate = useNavigate();
  const user = getUser();
  const { t } = useTranslation();
  const isMobile = Hooks.useMobileDetect().isMobile();

  const [selectedDentination, setSelectedDentination] = useState([
    {
      id: "5fad6174-6f6d-4d02-887f-52f235f53e09",
      name: "Paris - França",
      nameDisplay: "Paris",
      image:
        "https://media.istockphoto.com/id/490360522/photo/alexandre-iii-bridge-and-eiffel-tower-in-paris-france.jpg?s=612x612&w=0&k=20&c=vXTOG_gz9vBTNcMAwL47LtCuWesBkskdMhNf1PZlEzI=",
      description:
        "Paris é o principal centro urbano, cultural, econômico e político da França, além de ser uma das cidades mais visitadas do mundo. Conhecida por sua história rica, monumentos icônicos como a Torre Eiffel e o Museu do Louvre, e sua influência na moda, na arte e na gastronomia, é um epicentro global do turismo e da cultura.",
    },
    {
      id: "c6a191df-2b1b-4bea-9e7b-873a9b872198",
      name: "Miami - Flórida",
      nameDisplay: "Miami",
      image:
        "https://i.pinimg.com/474x/fd/8c/68/fd8c680128a5f8dd017110d578c6ceeb.jpg",
      description:
        "Miami é um vibrante centro cultural e econômico dos Estados Unidos, famoso por sua vida noturna, praias paradisíacas e diversidade étnica. Também é um importante hub para o comércio internacional e o turismo na América Latina.",
    },
    {
      id: "326a05be-63e6-464b-a951-a1a0d8dc4ff3",
      name: "Cidade do México - México",
      nameDisplay: "Cidade do México",
      image:
        "https://arrumaessamala.com.br/wp-content/uploads/2020/07/cidade-do-mexico-castelo-de-chapultepec-2.jpg",
      description:
        "A Cidade do México é o principal e mais desenvolvido centro urbano, econômico, cultural e político do país, além de ser uma das maiores metrópoles do mundo.",
    },
    {
      id: "201be399-2b84-4c98-a15f-b56aee16b4e6",
      name: "Cancún, Quintana Roo - México",
      nameDisplay: "Cancún",
      image:
        "https://a.travel-assets.com/findyours-php/viewfinder/images/res70/331000/331437-Quintana-Roo.jpg",
      description:
        "Seus belos cenários, praias maravilhosas, a biodiversidade dos recifes, cenotes, estuários, ilhas e lagunas convidam a viver uma experiência inesquecível!",
    },
    {
      id: "5282e879-3f1f-41e1-a0ff-11fbb27cfcfa",
      name: "Guadalajara, Jalisco - México",
      nameDisplay: "Guadalajara",
      image:
        "https://a.travel-assets.com/findyours-php/viewfinder/images/res70/62000/62632-Guadalajara.jpg",
      description:
        "É um importante centro cultural do México, considerado por muitos como o lar do Mariachi e sede de vários grandes eventos culturais.",
    },
  ]);
  const [hotelsRecommended, setHotelsRecommended] = useState([]);
  const search = getSearch();
  const hotelList = [
    "1d550540-cd19-4102-8943-6dd1c2f525ff",
    "5136c8ba-20ce-46ac-9109-f350be0319fa",
    "2d2ad129-63af-466a-bd7e-c47f139b771a",
  ];
  const getHotelsRecommended = async () => {
    try {
    } catch (error) {
      console.log(error);
    }
  };

  const SelectDestiny = (destination: (typeof selectedDentination)[0]) => {
    setSearch({
      ...search,
      destiny_id: destination.id,
      destiny: destination.name,
      type: "CITY",
    });
    navigate("/search");
  };
  const DEFAULT_DATE = {
    CHECKIN: moment().add("60", "d").toDate(),
    CHECKOUT: moment().add("65", "d").toDate(),
  };
  useEffect(() => {
    /*     getHotelsRecommended(); */
  }, []);
  useIsomorphicLayoutEffect(() => {
    setTimeout(() => {
      setHeader({
        $showSearch: true,
        $showFallback: false,
        $showMenu: true,
        $show: true
      });
    }, 10);
    setSearch({
      toWork: false,
      quantityRooms: 1,
      checkIn: DEFAULT_DATE.CHECKIN,
      checkOut: DEFAULT_DATE.CHECKOUT,
      rooms: [
        { id: 1, adults: 1, children: 0, childrenAge: [{ age: 0, id: 1 }] },
      ],
    });
  }, []);
  return (
    <>
      <Styles.Container>
        <Styles.Wrapper>
          <div>
            <h2>{t("dashboard.headerDestinations")}</h2>
            <h3>{t("dashboard.headerDestinationsDescription")}</h3>
            <Styles.WrapperDestination>
              {selectedDentination?.map((destination) => (
                <Carrousel
                  {...destination}
                  key={destination.id}
                  onClick={() => SelectDestiny(destination)}
                />
              ))}
            </Styles.WrapperDestination>
          </div>
          <div>
            <h2>{t("dashboard.FAQ")}</h2>
            <Styles.WrapperFAQ>
              <Accordion title={`${t("dashboard.titleTravelCanBeSimple")}`}>
                <div
                  dangerouslySetInnerHTML={{
                    __html: `${t("dashboard.contentTravelCanBeSimple")}`,
                  }}
                />
              </Accordion>
              <Accordion title={t("dashboard.titleLivingWithoutSurprises")}>
                <div
                  dangerouslySetInnerHTML={{
                    __html: `${t("dashboard.contentLivingWithoutSurprises")}`,
                  }}
                />
              </Accordion>
            </Styles.WrapperFAQ>
          </div>
        </Styles.Wrapper>
      </Styles.Container>
      <Footer />
    </>
  );
};
