import styled from "styled-components";

export const WrapperFooter = styled.footer`
  width: 100vw;
  height: 123px;
  background-color: ${({ theme }) => theme.palette.neutral.darkest};
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  & > h2 {
    color: #fff;
    text-align: center;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px; /* 150% */
  }
  & > svg, img {
    width: 160px;
    height: auto;
    filter: brightness(2);
    > path {
      fill: #fff;
    }
  }
`;
