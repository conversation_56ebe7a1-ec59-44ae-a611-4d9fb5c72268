import { useEffect, useState } from "react";
import * as Styles from "./styles";
import {
  getSearch,
  setHeader,
  setSearch,
  getUser,
  setUser,
  Hooks,
  // @ts-ignore
} from "@iterpec/booking-utility";
import { useNavigate, useLocation } from "react-router-dom";
import { Button, Card, Input } from "@iterpecdev/design-sistem";
import { toast, Toaster } from "sonner";

import Api from "../../Services";
import Spinner from "../Spinner";

const Login = () => {
  const [loginData, setLoginData] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const user = getUser();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get("token"); // substitua "token" pelo nome do parâmetro desejado

  const handleInput = (value: string, field: string) => {
    setLoginData((old) => ({ ...old, [field]: value }));
  };
  const { useAuth, useIsomorphicLayoutEffect } = Hooks;
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const login = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setLoading(true);
    const api = new Api();

    try {
      // @ts-ignore
      const { data } = await api.login({
        password: loginData.password,
        user: loginData.email,
      });

      console.log(data);
      setUser({
        ...data,
        createAt: new Date(loginData.createAt),
      });
      navigate("/");
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        setError(error?.response?.data?.message);
      } else if (
        error?.response?.status === 403 &&
        error?.response?.data?.message === "NEW_PASSWORD_REQUIRED"
      ) {
        navigate("/update-password");
      }
    } finally {
      setLoading(false);
    }
  };
  const loginWithJWT = async (token: string) => {
    const api = new Api();
    setLoading(true);
    try {
      const { data } = await api.loginWithJWT(token);
      setUser({
        ...data,
        createAt: new Date(),
      });
      // Sucesso: não faz nada além do fluxo normal
    } catch (error) {
      console.log(error);
      toast.error("Erro ao autenticar com JWT");
    } finally {
      setLoading(false);
    }
  };

  const handleHeader = () => {
    setHeader({
      $showSearch: false,
      $showMenu: false,
      $showFallback: false,
      $show: true,
      $fallback: () => {
        navigate(-1);
      },
    });
  };

  useIsomorphicLayoutEffect(() => {
    handleHeader();
    if (token) {
      loginWithJWT(token);
    }
    setSearch(null);
    setUser(null);
  }, []);
  return (
    <Styles.Wrapper className={token || loading ? "loading" : ""}>
      <Card>
        {token ? (
          <Spinner />
        ) : loading ? (
          <Spinner />
        ) : (
          <form>
            <h3>Login</h3>
            <Input
              type="email"
              label="E-mail"
              onChangeValue={(e) => handleInput(e, "email")}
            />
            <Input
              type="password"
              label="Senha"
              onChangeValue={(e) => handleInput(e, "password")}
            />
            {!!error && <p>{error}</p>}
            <Button onClick={(e) => login(e)}>Login</Button>
            <Button variant="link">Forgot password</Button>
          </form>
        )}
      </Card>
      <Toaster richColors />
    </Styles.Wrapper>
  );
};
export default Login;
