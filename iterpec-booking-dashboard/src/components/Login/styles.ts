import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 15px;
  height: calc(100vh - 100px);
  background-color: ${(props) => props.theme.palette.neutral.lighter};

  &.loading {
    & > div {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  * {
    font-family: ${(props) => props.theme.fonts.base};
  }
  & > div {
    background-color: ${(props) => props.theme.palette.neutral.white};
    width: 96%;
    max-width: 400px;
    height: 410px;
    > form {
      & > * {
        margin: ${(props) => props.theme.spacings.inset.md};
      }
      & > h3 {
        text-align: center;
      }
      & > p {
        text-align: center;
        color: ${(props) => props.theme.palette.status.error};
      }
      padding: 15px;
      display: inline;
      & > button {
        width: calc(100% - 30px);
        text-align: center;
        justify-content: center;
      }
    }
  }
`;
