import styled, { css } from "styled-components";

export const WrapperCardHotel = styled.div`
  ${({ theme }) => css`
    margin-top: 10px;
    & > div.card {
      display: flex;
      flex-direction: column;
      gap: 12px;
      width: 100%;
      & > div.carrouselWrapper {
        display: block;
        margin-left: -16px;
        width: 109.2%;
      }
      & > img {
        margin-left: -16px;
        width: 108%;
        object-fit: cover;
      }
      & div > img {
        margin-left: -16px;
        width: 104%;
        object-fit: contain;
        max-height: 35vh;
      }
      & > div.wrapperScoreAmenities {
        display: flex;
        gap: 8px;
        & > button {
          border-radius: 8px;
          border: 1px solid #a4ce4a;
          background: #a4ce4a;
        }
        & > svg {
          width: 20px;
          height: auto;
        }
      }
      & > div.wrapperTypeStars {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        & > button {
          font-size: 90%;
          border-radius: 8px;
          border: 1px solid #212843;
          background: #212843;
        }
        & > div.wrapperStars {
          & > div > svg {
            width: 24px;
          }
        }
      }
      div.wrapperButtons {
        display: flex;
        justify-content: space-between;
      }
      div.wrapperContent {
        display: flex;
        flex-direction: column;
        gap: 7px;
        & > span {
          color: #878787;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          &.refundable {
            color: #a4ce4a;
          }
          &.nonRefundable {
            color: #a7183c;
          }
        }
        & > div.roomPrice {
          display: flex;
          gap: 7px;
          & > h2 {
            color: #000;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 110% */
          }
          & > h2.oldPrice {
            color: rgba(255, 0, 0, 0.3);
            text-decoration: line-through;
          }
        }
        & > div.roomName {
          display: flex;
          gap: 7px;
          flex-direction: column;
          & > p {
            & > h3 {
              font-size: 1.1rem;
            }
            & > span {
              color: #878787;
              font-family: Inter;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px; /* 142.857% */
            }
          }
          > * {
            width: 100%;
          }
        }
      }
    }
  `}
`;

export const SkeletonWrapperCardHotel = styled.div`
  ${({ theme }) => css`
    margin-top: 10px;
    & > div.card {
      display: flex;
      flex-direction: column;
      gap: 12px;
      width: 100%;
      * {
        border-radius: 8px;
      }
      & > span {
        margin-left: -16px;
        width: 110%;
        height: 181px;
      }
      & > div.wrapperScoreAmenities {
        display: flex;
        gap: 8px;
        & > span {
          border-radius: 8px;
        }
      }
      & > div.wrapperTypeStars {
        display: flex;
        gap: 8px;
        & > span {
          border-radius: 8px;
          width: 59px;
          height: 24px;
        }
        & > div.wrapperStars {
          & > span {
            width: 120px;
            height: 24px;
          }
        }
      }
      div.wrapperButtons {
        display: flex;
        justify-content: space-between;
      }
      div.wrapperContent {
        display: flex;
        flex-direction: column;
        gap: 7px;
        & > span {
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          &.refundable {
          }
        }
        & > div.roomPrice {
          display: flex;
          gap: 7px;
          & > span {
            color: #000;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 110% */
          }
          & > h2.oldPrice {
            text-decoration: line-through;
          }
        }
        & > div.roomName {
          display: flex;
          gap: 7px;
          flex-direction: column;
          & > span {
            font-size: 1.1rem;
          }
          & > h3 {
            width: 108px;
          }
          > * {
            width: 100%;
          }
        }
      }
    }
  `}
`;
