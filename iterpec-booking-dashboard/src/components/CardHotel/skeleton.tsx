import { Card, Skeleton } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import { useNavigate } from "react-router-dom";
import React from "react";

const SkeletonCardHotel: React.FC<{ id?: string }> = ({ id = undefined }) => {
  const navigate = useNavigate();

  return (
    <Styles.SkeletonWrapperCardHotel id={id}>
      <Card className="card">
        <h3>
          <Skeleton />
        </h3>
        <div className="wrapperTypeStars">
          <div className="wrapperStars">
            <Skeleton />
          </div>
          <Skeleton />
        </div>
        <Skeleton />

        <div className="wrapperContent">
          <div className="roomName">
            <h3>
              <Skeleton />
            </h3>
            <span>
              <Skeleton />
            </span>
            <span>
              <Skeleton />
            </span>
          </div>
          <span>
            <Skeleton />
          </span>
        </div>
      </Card>
    </Styles.SkeletonWrapperCardHotel>
  );
};
export default SkeletonCardHotel;
