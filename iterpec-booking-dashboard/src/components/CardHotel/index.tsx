import { <PERSON><PERSON>, <PERSON>, <PERSON>s } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import Icon from "@iterpecdev/icons-system";
import { useNavigate } from "react-router-dom";
import { Fragment, useEffect, useState } from "react";
import Stars from "../Stars";
import {
  Hooks,
  getSearch,
  getHotel,
  setHotel as setHotelData,
  getRegion,
  //@ts-ignore
} from "@iterpec/booking-utility";
import moment from "moment";
import Carousel from "../CarrouselSearch";

import SkeletonCardHotel from "./skeleton";

import { useTranslation } from "react-i18next";
import { AmenitiesIconNoRepeat } from "../AmenitiesIcon/noRepeat";
import Api from "../../Services";
import { Available, IStaticData } from "../../Services/types/index";
const CardHotel = ({
  data,
  setStaticData,
}: {
  data: Available;
  setStaticData?: (e: IStaticData) => void;
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [hotel, setHotel] = useState<IStaticData>(null);
  const dataHotel = getHotel();
  const { language } = getRegion();
  const { useCurrency } = Hooks;
  moment.locale(language);
  const rate = data?.rooms[0]?.rates?.[0] ?? null;
  var [occupancyPricing] = rate ? rate?.occupancy_pricing : null;
  var [cancelPenalties] = rate ? rate?.cancel_penalties : null;
  const search = getSearch();
  const getHotelStaticData = async () => {
    const api = new Api();
    try {
      const { data: staticData } = await api.getStaticData(data.id);
      setHotel(staticData.property);
      setStaticData?.(staticData.property);
    } catch (error) {
      console.log(error);
    }
  };
  /* 
  const goBooking = () => {
    const room = hotel.rooms.find((room) => room.id === data.rooms[0].id);
    setSearch({
      ...search,
      hotel: { ...hotel, room },
      room: room,
      rate: room?.rates[0],
    });
    navigate(`/checkout`);
  }; */
  const getRooms = () => {
    navigate(
      `/search/hotel?propertyId=${data.id}&token=${data.links.additional_rates.href}`
    );
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
    //@ts-ignore  
    setHotelData({ ...dataHotel, ...hotel });
  };
  useEffect(() => {
    getHotelStaticData();
  }, []);
  if (!hotel?.id) {
    return <SkeletonCardHotel />;
  }
  return (
    <Styles.WrapperCardHotel key={hotel?.id} id={hotel?.id ? hotel?.id : ""}>
      <Card className="card">
        <h3>{hotel?.name}</h3>
        <div className="wrapperTypeStars">
          <div className="wrapperStars">
            <Stars stars={hotel?.stars} />
          </div>
          {hotel?.category?.name ? (
            <Chips active variant="tag">
              {hotel?.category?.name}
            </Chips>
          ) : null}
        </div>
        {hotel?.images?.length ? (
          <Carousel>
            {[
              ...hotel?.images?.filter((image) => !!image.heroImage),
              ...hotel?.images?.filter((image) => !image.heroImage),
            ]
              .filter((image) => image.links["350px"])
              .map((image) => {
                return (
                  <img
                    src={image.links["350px"]?.href}
                    loading="lazy"
                    alt={image.caption}
                  />
                );
              })}
          </Carousel>
        ) : (
          <img />
        )}

        <div className="wrapperScoreAmenities">
          {hotel?.guestRatings?.overall ? (
            <Chips active variant="tag" color="success">
              {hotel?.guestRatings?.overall * 2}
            </Chips>
          ) : null}
          <AmenitiesIconNoRepeat amenities={hotel?.amenities} />
        </div>
        <div className="wrapperContent">
          <div className="roomName">
            <p>
              <h3>{data?.rooms[0]?.room_name}</h3>
              <span>{t("cardhotel.from")}</span>
            </p>
          </div>
          <div className="roomPrice">
            {occupancyPricing?.totals?.inclusive_strikethrough
              ?.billable_currency?.value ? (
              <h2 className="oldPrice">
                {useCurrency(
                  Number(
                    occupancyPricing?.totals?.inclusive_strikethrough
                      ?.billable_currency?.value
                  )
                )}
              </h2>
            ) : null}
            <h2>
              {useCurrency(
                Number(
                  occupancyPricing?.totals?.inclusive?.billable_currency?.value
                )
              )}
            </h2>
          </div>
          {/* <span>{t('cardhotel.totalIncluded')}</span> */}
          {cancelPenalties &&
          moment(cancelPenalties.start).isAfter(new Date()) &&
          moment(cancelPenalties?.end).isAfter(new Date()) ? (
            <span className="refundable">
              {t("cardhotel.freeCancellation.cancel")}{" "}
              <b>{t("cardhotel.freeCancellation.free")}</b>{" "}
              {t("cardhotel.freeCancellation.untilThe")}{" "}
              {moment(moment(cancelPenalties?.end).subtract(1, "days")).format(
                "DD MMMM"
              )}
            </span>
          ) : (
            <span className="nonRefundable">
              {t("cardhotel.nonRefundable")}
            </span>
          )}
        </div>
        <div className="wrapperButtons">
          {/* <Button variant="link" onClick={() => goBooking()}>
            {t("cardhotel.book")}
          </Button> */}
          <div />
          <Button onClick={() => getRooms()}>{t("cardhotel.viewRooms")}</Button>
        </div>
      </Card>
    </Styles.WrapperCardHotel>
  );
};
export default CardHotel;
