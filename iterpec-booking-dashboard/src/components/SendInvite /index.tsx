import { useState } from "react";
import * as Styles from "./styles";
import Api from "../../Services";
import { Input, <PERSON><PERSON>, <PERSON> } from "@iterpecdev/design-sistem";
import {
  setHeader,
  getUser,
  Hooks,
  // @ts-ignore
} from "@iterpec/booking-utility";
import { useNavigate } from "react-router-dom";
import InputMask from "react-input-mask";
import { obterMascara } from "../../utils/phoneMask";

const SendInvite = () => {
  const { useIsomorphicLayoutEffect } = Hooks;
  const navigate = useNavigate();
  const user = getUser();

  const [email, setEmail] = useState<string>("");
  const [phoneNumber, setPhoneNumber] = useState(""); // Adicionado estado para telefone
  const [error, setError] = useState("");
  const [mask, setMask] = useState("");
  const api = new Api();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const sendInvite = async () => {
    setError("");
    setLoading(true);
    setSuccess(false);
    try {
      await api.sendInvite({
        email: email?.trim(),
        phoneNumber: `+${phoneNumber}`,
      }); // Enviar email e telefone
      setSuccess(true);
    } catch (error) {
      console.log(error);
      const message: string = error?.response?.data?.message ?? error.message;
      setError(message.split("(")[0]);
    }
    setLoading(false);
  };

  const clearForm = () => {
    setEmail("");
    setPhoneNumber("");
  };

  const handleHeader = () => {
    setHeader({
      $showSearch: false,
      $showMenu: true,
      $showFallback: true,
      $fallback: () => {
        navigate(-1);
      },
    });
  };
  useIsomorphicLayoutEffect(() => {
    setTimeout(() => {
      handleHeader();
    }, 100);
    handleHeader();
  }, []);
  useIsomorphicLayoutEffect(() => {
    setMask(obterMascara(phoneNumber));
  }, [phoneNumber.length]); // Adicionado dependência de phoneNumber
  useIsomorphicLayoutEffect(() => {
    success ? clearForm() : null;
  }, [success]);

  return (
    <Styles.Wrapper>
      <Card>
        <form>
          <h3>Convidar usuarios</h3>
          <Input
            label="Email"
            type="email"
            value={email}
            placeholder="<EMAIL>"
            onChange={(e) => setEmail(e.target.value)}
          />
          <InputMask
            mask={mask} // Adicionado máscara de telefone
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value.replace(/\D/g, ""))} // Adicionado campo de telefone
            maskChar=" "
          >
            {(inputProps) => (
              <Input
                type="tel"
                label="phoneNumber"
                {...inputProps}
                placeHolder="+55 (11) 99999-9999"
              />
            )}
          </InputMask>

          <Button onClick={sendInvite} disabled={loading} variant="primary">
            Enviar convite
          </Button>

          {error && <div>{error}</div>}
          {success && <div>Convite enviado!</div>}
        </form>
      </Card>
    </Styles.Wrapper>
  );
};

export default SendInvite;
