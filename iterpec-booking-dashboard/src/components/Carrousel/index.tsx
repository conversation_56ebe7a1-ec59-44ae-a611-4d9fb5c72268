import React from "react";
import * as Styles from "./styles";
import { ICarrousel } from "./type";
const Carrousel = (props: ICarrousel) => {
  return (
    <Styles.wrapperCarrousel
      key={props.nameDisplay}
      image={props.image}
      onClick={props.onClick}
    >
      <h3>{props.nameDisplay}</h3>
      <span>{props.description}</span>
    </Styles.wrapperCarrousel>
  );
};

export default Carrousel;
