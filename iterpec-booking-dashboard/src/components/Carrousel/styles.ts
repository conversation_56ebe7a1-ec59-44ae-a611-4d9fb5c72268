import styled, { css } from "styled-components";

export const wrapperCarrousel = styled.div<{ image: string }>`
  position: relative;
  padding: 0px;
  border: 1px solid #ebebeb;
  background-image: url(${(props) => props.image});
  background-size: cover;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  ${(props) =>
    props.theme.breakpoints.desktop(`
      display: flex;
      justify-content: flex-start;
      align-items: flex-end;
      padding: 10px;
      font-size: 24px;
      height: 350px;
      width: 220px;
      border-radius: 20px;

    `)}
  ${(props) =>
    props.theme.breakpoints.mobile(`
      height: 450px;
      border-radius: 8px;
      min-width: 278px;
    `)}
  & > h3 {
    ${(props) =>
      props.theme.breakpoints.mobile(`
      position: absolute;
      padding: 0 16px;
      margin: 0;
      font-size: 40px;
      bottom: 40%;
    `)}
    ${(props) =>
      props.theme.breakpoints.desktop(`
      font-size: 1.5rem;
    `)}
    color: rgba(255, 255, 255, 0.92);
    font-family: Inter;
    font-style: normal;
    font-weight: 900;
    line-height: 110%;
  }
  & > span {
    ${(props) =>
      props.theme.breakpoints.desktop(`
      display: none;
    `)}
    padding: 16px;
    width: 100%;
    font-family: Inter;
    height: auto;
    max-height: 140px;
    position: absolute;
    color: rgba(255, 255, 255, 0.92);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.25);
    top: 60%;
    left: 0;
    line-height: 120%;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;

    overflow: scroll;
    text-overflow: ellipsis;
  }
`;
