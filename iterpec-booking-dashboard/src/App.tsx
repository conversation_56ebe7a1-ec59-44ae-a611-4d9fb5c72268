import { Provider } from "@iterpecdev/theme-system";
import { Routes } from "./Routes";
import { BrowserRouter } from "react-router-dom";
//@ts-ignore
import { Hooks } from "@iterpec/booking-utility";
import { WrapperDesktop } from "./components/WrapperDesktop";
import { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";

const App: React.FC = () => {
  const { i18n, t } = useTranslation();
  const useTheme = Hooks.useTheme();

  useEffect(() => {
    window.addEventListener(
      "languageChange",
      (customEvent: CustomEvent<{ language: string }>) => {
        i18n.changeLanguage(customEvent?.detail?.language);
      }
    );
  }, []);

  return (
    <Provider theme={useTheme}>
      <BrowserRouter>
        <Routes />
      </BrowserRouter>
    </Provider>
  );
};

export default App;
