import React from "react";
import { Routes as Switch, Route, Navigate } from "react-router-dom";
import { Dashboard } from "../components/Dashboard";
import Terms from "../components/Terms";
import Login from "../components/Login";
import SendInvite from "../components/SendInvite ";
import UpdatePassword from "../components/UpdatePassword";
import SingUp from "../components/SingUp";
//@ts-ignore
import { Hooks } from "@iterpec/booking-utility";

const PrivateRoute = ({ children }: { children: JSX.Element }) => {
  const { useAuth } = Hooks;
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? children : <Navigate to="/login" replace />;
};

export const Routes = () => {
  const { useAuth } = Hooks;
  const { isAuthenticated } = useAuth();

  return (
    <Switch>
      <Route
        path="/login"
        element={!isAuthenticated ? <Login /> : <Navigate to="/" replace />}
      />
      <Route path="/terms" element={<Terms />} />
      <Route path="/send-invite" element={<SendInvite />} />
      <Route path="/singup" element={<SingUp />} />
      <Route path="/forgot-password" element={<UpdatePassword />} />
      <Route path="/update-password" element={<UpdatePassword />} />
      <Route
        path="/*"
        element={
          <PrivateRoute>
            <Dashboard />
          </PrivateRoute>
        }
      />
    </Switch>
  );
};
