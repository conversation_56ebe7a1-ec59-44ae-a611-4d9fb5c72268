export interface IGetFilters {
  starsFilters: ComumFilter[];
  ratingFilters: ComumFilter[];
  amenitiesGroup: AmenityCategory[];
  princingFilters: PriceFilter;
}

export interface ComumFilter {
  name: string;
  query: string;
}

export interface PriceFilter {
  biggeerValue: Price;
  smallerValue: Price;
}
export interface Price {
  name: string;
  query: string;
  value: number;
}

export interface AmenityCategory {
  groupName: string;
  amenityCategory: {
    name: string;
    query: string;
  }[];
}
