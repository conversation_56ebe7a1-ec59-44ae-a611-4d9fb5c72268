export interface ILogin {
  idAcess: string;
  client: string;
  idUser: string;
  username: string;
  email: string;
  phoneNumber: string;
  age: number | null;
  birthDate: string;
  countryCodeISO: string;
  document: IDocument;
  enumTitle: string | null;
  name: string;
  surname: string;
  title: string;
  expiresIn: number;
  tokenType: string;
  idToken: string;
  accessToken: string;
  createAt: string;
  refreshToken: string;
}
interface IDocument {
  documentNumber: string;
  documentCountryId: string;
  documentExpirationDate: string;
  documentType: string;
}

export interface ILoginError {
  timestamp: Date;
  status: number;
  error: string;
  message: string;
  path: string;
}
