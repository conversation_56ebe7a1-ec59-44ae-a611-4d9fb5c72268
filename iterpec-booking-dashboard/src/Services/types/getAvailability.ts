export interface IAvailables {
  searchId: string;
  pageNumber: number;
  totalAvailables: number;
  totalPages: number;
  mainProperty: Available;
  availables: Available[];
}

export interface Available {
  id: string;
  property_id: string;
  status: string;
  score: string;
  min_price: number;
  rooms: Room[];
  links: Links2;
}

interface Room {
  id: string;
  room_name: string;
  rates: Rate[];
}

interface Rate {
  id: string;
  status: string;
  available_rooms: string;
  amenities: Amenity[];
  occupancy_pricing: OccupancyPricing[];
  bed_groups: BedGroup[];
  cancel_penalties: CancelPenalty[];
  nonrefundable_date_ranges: any;
}

interface Amenity {
  idExpedia: string;
  id: string;
  name: string;
}

interface OccupancyPricing {
  nightly: Nightly[][];
  stay?: Stay[];
  totals: Totals;
  fees?: Fees;
}

interface Nightly {
  type: string;
  value: string;
  currency: string;
}

interface Stay {
  type: string;
  value: string;
  currency: string;
}

interface Totals {
  inclusive: Inclusive;
  exclusive: Exclusive;
  inclusive_strikethrough?: InclusiveStrikethrough;
  strikethrough?: Strikethrough;
  marketing_fee: MarketingFee;
  gross_profit: GrossProfit;
  minimum_selling_price: any;
  property_fees?: PropertyFees;
}

interface Inclusive {
  billable_currency: BillableCurrency;
  request_currency: RequestCurrency;
}

interface BillableCurrency {
  value: string;
  currency: string;
}

interface RequestCurrency {
  value: string;
  currency: string;
}

interface Exclusive {
  billable_currency: BillableCurrency2;
  request_currency: RequestCurrency2;
}

interface BillableCurrency2 {
  value: string;
  currency: string;
}

interface RequestCurrency2 {
  value: string;
  currency: string;
}

interface InclusiveStrikethrough {
  billable_currency: BillableCurrency3;
  request_currency: RequestCurrency3;
}

interface BillableCurrency3 {
  value: string;
  currency: string;
}

interface RequestCurrency3 {
  value: string;
  currency: string;
}

interface Strikethrough {
  billable_currency: BillableCurrency4;
  request_currency: RequestCurrency4;
}

interface BillableCurrency4 {
  value: string;
  currency: string;
}

interface RequestCurrency4 {
  value: string;
  currency: string;
}

interface MarketingFee {
  billable_currency: BillableCurrency5;
  request_currency: RequestCurrency5;
}

interface BillableCurrency5 {
  value: string;
  currency: string;
}

interface RequestCurrency5 {
  value: string;
  currency: string;
}

interface GrossProfit {
  billable_currency: BillableCurrency6;
  request_currency: RequestCurrency6;
}

interface BillableCurrency6 {
  value: string;
  currency: string;
}

interface RequestCurrency6 {
  value: string;
  currency: string;
}

interface PropertyFees {
  billable_currency: BillableCurrency7;
  request_currency: RequestCurrency7;
}

interface BillableCurrency7 {
  value: string;
  currency: string;
}

interface RequestCurrency7 {
  value: string;
  currency: string;
}

interface Fees {
  mandatory_fee: any;
  resort_fee: ResortFee;
  mandatory_tax: MandatoryTax;
}

interface ResortFee {
  billable_currency: BillableCurrency8;
  request_currency: RequestCurrency8;
}

interface BillableCurrency8 {
  value: string;
  currency: string;
}

interface RequestCurrency8 {
  value: string;
  currency: string;
}

interface MandatoryTax {
  billable_currency: BillableCurrency9;
  request_currency: RequestCurrency9;
}

interface BillableCurrency9 {
  value: string;
  currency: string;
}

interface RequestCurrency9 {
  value: string;
  currency: string;
}

interface BedGroup {
  id: string;
  links: Links;
  description: string;
  configuration: Configuration[];
}

interface Links {
  price_check: PriceCheck;
}

interface PriceCheck {
  method: string;
  href: string;
  expires: any;
}

interface Configuration {
  type: string;
  size: string;
  quantity: string;
}

interface CancelPenalty {
  currency: string;
  start: string;
  end: string;
  amount: any;
  nights?: string;
  percent?: string;
}

interface Links2 {
  additional_rates: AdditionalRates;
  recommendations: Recommendations;
}

interface AdditionalRates {
  method: string;
  href: string;
  expires: any;
}

interface Recommendations {
  method: string;
  href: string;
  expires: any;
}
