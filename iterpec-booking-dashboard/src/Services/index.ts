import axios, { AxiosRequestHeaders, AxiosInstance, AxiosHeaders } from "axios";

import {
  getRegion,
  getSearch,
  getUser,
  //@ts-ignore
} from "@iterpec/booking-utility";
import {
  IAutoComplete,
  IStaticData,
  TopLevel,
  IAdicionalRates,
  ILogin,
  IUserRegisterData,
} from "./types/index";
import { ILoginError } from "./types/login";

class Api {
  private expediaApi: AxiosInstance;
  private authApi: AxiosInstance;
  private headers: AxiosRequestHeaders;
  private language: string;
  private token: string;
  private searchId: string;
  private user: string;
  private client: string;

  constructor() {
    this.language = getRegion()?.language;
    this.searchId = getSearch()?.searchId;
    this.token = getUser()?.accessToken ?? null;
    this.user = getUser()?.idUser ?? null;
    this.client = getUser()?.clientId ?? null;

    // Criando uma instância de AxiosHeaders
    this.headers = new AxiosHeaders();
    this.headers.set("Content-Type", "application/json;charset=utf-8");
    this.headers.set("Cache-Control", "no-cache");
    this.headers.set("language", this.language);
    this.headers.set("X-UserId", this.user);
    this.headers.set("x-clientId", this.client);
    this.headers.set("Refer", window.location.host);
    if (this.token) this.headers.set("Authorization", `Bearer ${this.token}`);
    this.expediaApi = axios.create({
      baseURL: process.env.API_SEARCH,
      timeout: 40000,
      headers: this.headers,
    });
    this.authApi = axios.create({
      baseURL: process.env.API_AUTH,
      timeout: 40000,
      headers: this.headers,
    });
  }

  async getReviews(data: { language: string; propertyId: string }) {
    return this.expediaApi.post(`/search/reviews`, data, {});
  }
  async registerUser(data: IUserRegisterData) {
    return this.authApi.post(`/user/register`, data, {});
  }
  async sendInvite(data) {
    return this.authApi.post(`/user/invite/send`, data, {});
  }
  async updatePassword(data) {
    return this.authApi.post(`/user/force-newpassword`, data, {});
  }
  async login(data: { user: string; password: string }) {
    return await this.authApi.post(`/login`, data, {});
  }
  async loginWithJWT(data: string) {
    return await this.authApi.get(`/login/` + data);
  }

  async adicionalRates(
    data: IAutoComplete & { propertyId: string; token: string }
  ) {
    return await this.expediaApi.post<{ payload: IAdicionalRates }>(
      `/search/availability/additonalRates`,
      { ...data, searchId: this.searchId }
    );
  }

  async getStaticData(propertyId: string) {
    const response = await this.expediaApi.get<{ property: IStaticData }>(
      `/static-data/property/` + propertyId
    );
    return response;
  }
}

export default Api;
