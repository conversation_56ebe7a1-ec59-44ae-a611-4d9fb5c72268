import i18n from "i18next";
import { initReactI18next } from "react-i18next";

// Importar arquivos de tradução
import translationEN from "./locales/en/translation.json";
import translationES from "./locales/es/translation.json";
import translationPT from "./locales/pt/translation.json";
//@ts-ignore
import { getRegion } from "@iterpec/booking-utility";

// Configuração das traduções
const resources = {
  "en-US": {
    translation: translationEN,
  },
  "es-MX": {
    translation: translationES,
  },
  "pt-BR": {
    translation: translationPT,
  },
};

const { language } = getRegion();

i18n.use(initReactI18next).init({
  resources,
  lng: language, // Idioma padrão
  fallbackLng: "en-US", // Idioma de fallback
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
