export const iconName = (Inputname: string) => {
  const name = Inputname.toLowerCase()
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "");
  const INTERNET_REGEX = new RegExp(/\b(?:wifi|internet|rede|network)\b/g);
  const FITNESS_REGEX = new RegExp(/\b(?:fitness|academias|ginasio)\b/g);
  const BREAKFAST_REGEX = new RegExp(/\b(?:desayuno|breakfast)\b/g);
  const PARKING_REGEX = new RegExp(
    /\b(?:parking|estacionamiento|estacionamento)\b/g
  );
  const PETS_REGEX = new RegExp(/\b(?:pet|pets|animal|mascotas)\b/g);
  const WATER_REGEX = new RegExp(/\b(?:acuatico|aquatico|water)\b/g);
  const AIRCONDITIONING_REGEX = new RegExp(
    /\b(?:conditioning|condicionado|ar-condicionado|aire-acondicionado|acondicionado)\b/g
  );
  const SPA_REGEX = new RegExp(/\b(?:spa)\b/g);
  const POOL_REGEX = new RegExp(/\b(?:pool|piscina)\b/g);
  const LAUNDRY_REGEX = new RegExp(/\b(?:lavanderia|laundry)\b/g);
  const ALL_INCLUSIVE = new RegExp(/\b(?:todo|all)\b/g);
 /*  if (ALL_INCLUSIVE.test(name)) {
    return "Infinity";
  } */
  if (INTERNET_REGEX.test(name)) {
    return "Wifi";
  }
  if (FITNESS_REGEX.test(name)) {
    return "WeightLifter";
  }
  if (BREAKFAST_REGEX.test(name)) {
    return "Coffee";
  }
  if (PARKING_REGEX.test(name)) {
    return "Car";
  }
  if (PETS_REGEX.test(name)) {
    return "Paw";
  }
  if (WATER_REGEX.test(name)) {
    return "Pool";
  }
  if (AIRCONDITIONING_REGEX.test(name)) {
    return "SnowflakeVariant";
  }
  if (SPA_REGEX.test(name)) {
    return "Spa";
  }
  if (POOL_REGEX.test(name)) {
    return "Swim";
  }
  if (LAUNDRY_REGEX.test(name)) {
    return "WashingMachine";
  }
};
