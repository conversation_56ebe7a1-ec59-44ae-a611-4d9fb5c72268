function obterMascaraPais(ddi: string): string {
  switch (ddi) {
    case "55": // Brasil
      return "+55 (99) 99999-9999";
    case "54": // Argentina
      return "+54 (99) 9999-9999";
    case "56": // Chile
      return "+56 (99) 9999-9999";
    case "57": // Colômbia
      return "+57 (99) 999-9999";
    case "58": // Venezuela
      return "+58 (99) 999-9999";
    case "51": // Peru
      return "+51 (99) 999-999";
    case "52": // México
      return "+52 (99) 9999-9999";
    case "53": // Cuba
      return "+53 (99) 999-9999";
    case "505": // Nicarágua
      return "+505 (999) 9999";
    case "506": // Costa Rica
      return "+506 (999) 999-9999";
    case "507": // Panamá
      return "+507 (999) 999-9999";
    case "593": // Equador
      return "+593 (99) 999-9999";
    case "595": // Paraguai
      return "+595 (99) 999-9999";
    case "598": // Uruguai
      return "+598 (99) 999-999";
    // Adicione mais países conforme necessário
    default:
      return "+99 (99) 99999-9999";
  }
}

export function obterMascara(numero: string): string {
  // Remover caracteres não numéricos
  const numeroLimpo = numero.replace(/\D/g, "");

  // Identificar o DDI e retornar a máscara apropriada
  const ddi = numeroLimpo.slice(0, numeroLimpo.length - 11); // Considerando 11 dígitos para DDD e número
  return obterMascaraPais(ddi);
}
