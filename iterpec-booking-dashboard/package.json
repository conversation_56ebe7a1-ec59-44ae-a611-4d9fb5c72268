{"name": "@iterpec/booking-dashboard", "license": "MIT", "scripts": {"start": "webpack serve --port 8080 --env isLocal", "start:standalone": "webpack serve --env standalone", "build": "concurrently yarn:build:*", "build:webpack": "webpack --mode=production", "analyze": "webpack --mode=production --env analyze", "lint": "eslint src --ext js,ts,tsx", "format": "prettier --write .", "check-format": "prettier --check .", "test": "cross-env BABEL_ENV=test jest", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "coverage": "cross-env BABEL_ENV=test jest --coverage"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "@types/styled-components": "^5.1.26", "@types/testing-library__jest-dom": "^5.14.1", "babel-jest": "^27.0.6", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-plugin-prettier": "^3.4.1", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "prettier": "^2.3.2", "pretty-quick": "^3.1.1", "ts-config-single-spa": "^3.0.0", "typescript": "^4.3.5", "webpack": "^5.51.1", "webpack-cli": "^4.8.0", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@brazilian-utils/brazilian-utils": "^1.0.0-rc.12", "@iterpecdev/design-sistem": "^1.6.1", "@iterpecdev/icons-system": "^1.11.2", "@iterpecdev/theme-system": "^1.5.4", "@types/jest": "^27.0.1", "@types/react": "^18.0.18", "@types/react-dom": "^17.0.9", "@types/systemjs": "^6.1.1", "@types/webpack-env": "^1.16.2", "axios": "^1.5.1", "dotenv-webpack": "^8.0.1", "html-webpack-plugin": "^5.6.3", "i18next": "^23.12.3", "moment": "^2.30.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-i18next": "^15.0.1", "react-input-mask": "^2.0.4", "react-router-dom": "^6.17.0", "single-spa": "^5.9.3", "single-spa-react": "^4.3.1", "sonner": "^2.0.6", "styled-components": "^5.3.5"}, "types": "dist/iterpec-booking-dashboard.d.ts"}