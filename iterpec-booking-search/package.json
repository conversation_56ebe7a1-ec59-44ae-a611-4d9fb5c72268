{"name": "@iterpec/booking-search", "scripts": {"start": "webpack serve --port 8002 --env isLocal", "start:standalone": "webpack serve --env standalone", "build": "concurrently yarn:build:*", "build:webpack": "webpack --mode=production", "analyze": "webpack --mode=production --env analyze", "lint": "eslint src --ext js,ts,tsx", "format": "prettier --write .", "check-format": "prettier --check .", "test": "cross-env BABEL_ENV=test jest", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "prepare": "husky install", "coverage": "cross-env BABEL_ENV=test jest --coverage"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "@types/google-map-react": "^2.1.9", "@types/mocha": "^10.0.10", "@types/react-date-range": "^1.4.9", "@types/testing-library__jest-dom": "^5.14.1", "babel-jest": "^27.0.6", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-plugin-prettier": "^3.4.1", "husky": "^7.0.2", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "prettier": "^2.3.2", "pretty-quick": "^3.1.1", "ts-config-single-spa": "^3.0.0", "ts-jest": "^29.3.2", "typescript": "^4.3.5", "webpack": "^5.75.0", "webpack-cli": "^4.10.0", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@iterpecdev/design-sistem": "^1.6.1", "@iterpecdev/icons-system": "^1.11.2", "@iterpecdev/theme-system": "^1.5.4", "@types/jest": "^29.5.14", "@types/react": "^18.0.18", "@types/react-dom": "^17.0.9", "@types/systemjs": "^6.1.1", "@types/webpack-env": "^1.16.2", "axios": "^1.5.1", "crypto": "^1.0.1", "date-fns": "^3.6.0", "diacritics": "^1.3.0", "dotenv": "^16.3.1", "dotenv-webpack": "^8.0.1", "google-map-react": "^2.2.1", "i18next": "^23.12.3", "js-levenshtein": "^1.1.6", "moment": "^2.30.1", "react": "^17.0.2", "react-currency-mask": "^1.3.2", "react-date-range": "^2.0.1", "react-dom": "^17.0.2", "react-i18next": "^15.0.1", "react-router-dom": "^6.3.0", "single-spa": "^5.9.3", "single-spa-react": "^4.3.1", "styled-components": "^6.1.0"}, "types": "dist/iterpec-booking-search.d.ts"}