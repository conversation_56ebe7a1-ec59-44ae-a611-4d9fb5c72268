module.exports = {
  preset: "ts-jest",
  testPathIgnorePatterns: ["/node_modules/", "/*/*.stories.tsx/"],
  transformIgnorePatterns: ["/*.png/"],
  modulePathIgnorePatterns: ["node_modules", "jest-test-results.json"],
  transform: {
    "^.+\\.(ts|tsx)?$": "ts-jest",
    "^.+\\.(js|jsx)$": "babel-jest",
  },
  testEnvironment: "jsdom",
  globals: {
    "ts-jest": {
      tsconfig: "jest.tsconfig.json",
      isolatedModules: true,
    },
  },
  collectCoverage: true,
  collectCoverageFrom: [
    "src/**/*.tsx",
    "!src/**/*.test.tsx",
    "!src/**/*styles.tsx",
    "!src/**/*styles.ts",
    "!src/**/*.stories.tsx",
  ],
  coverageReporters: ["lcov", "json"],
  moduleNameMapper: {
    "^types(.*)$": "<rootDir>/src/@types/$1",
    "^components(.*)$": "<rootDir>/src/components/$1",
    "^tests(.*)$": "<rootDir>/src/tests/$1",
  },
};
