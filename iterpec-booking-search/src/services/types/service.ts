import { Rate } from "./getAvailability";

export interface ISearch {
  payload: IHotel[];
  page: number;
  pageSize: number;
  total: number;
  searchCoordenate: {
    latitude: number;
    longitute: number;
  };
  totalPages: number;
}

export interface TopLevel<t> {
  payload: t;
  errorCode: number;
  errorMessage: string;
  responseTime: number;
}

export interface IStaticData {
  id: string;
  modificationDate: string;
  creationDate: string;
  name: string;
  description: Description;
  twentyFourHour: string;
  checkinBeginTime: string;
  checkinEndTime: string;
  instructions: string;
  specialInstructions: string;
  checkOutTime: string;
  optionalFee: string;
  mandatoryFee: string;
  knowBeforeYouGo: string;
  provinceState: ProvinceState;
  highLevelRegion: HighLevelRegion;
  multiCityVicinity: MultiCityVicinity;
  city: City;
  neighborhood: Neighborhood;
  stars: number;
  guestRatings: GuestRatings;
  category: Category;
  images: Image[];
  location: Location;
  amenities: Amenity[];
  rooms: Room[];
  address: Address;
  rates: Rate[];
  supplySource: string;
  active: boolean;
  blocked: boolean;
  emphasis: boolean;
  rank: number;
  minAge: number;
  expediaCollect: boolean;
  propertyCollect: boolean;
  phone: string;
  taxId: string;
  multiUnit: boolean;
  chain: Chain;
  brand: Brand;
  paymentRegistrationRecommended: boolean;
  onSitePayments: any[];
  statistics: Statistic[];
}

export interface Description {
  amenities: string;
  dining: string;
  businessAmenities: string;
  rooms: string;
  attractions: string;
  location: string;
  headline: string;
}

export interface ProvinceState {
  id: string;
  name: string;
}

export interface HighLevelRegion {
  id: string;
  name: string;
}

export interface MultiCityVicinity {
  id: string;
  name: string;
}

export interface City {
  id: string;
  name: string;
}

export interface Neighborhood {
  id: string;
  name: string;
}

export interface GuestRatings {
  overall: number;
  cleanliness: number;
  service: number;
  comfort: number;
  condition: number;
  location: number;
  neighborhood: number;
  value: number;
  amenities: number;
  recommendationPercent: number;
}

export interface Category {
  id: string;
  name: string;
}

export interface Image {
  heroImage: boolean;
  caption: string;
  links: Links;
}

export interface Links {
  "350px": string;
  "70px": string;
  "1000px": string;
}

export interface Location {
  longitude: number;
  latitude: number;
}

export interface Amenity {
  id: string;
  idExpedia: string;
  name: string;
}

export interface Room {
  id: string;
  idExpedia: string;
  name: string;
  description: string;
  amenities: Amenity[];
  images: Image2[];
  bedGroups: BedGroup[];
  views: View[];
  area: Area;
  ageCategories: any[];
  children: number;
  adults: number;
}

export interface Image2 {
  heroImage: boolean;
  caption: string;
  links: Links2;
}

export interface Links2 {
  "350px": string;
  "70px": string;
  "200px": string;
  "1000px": string;
}

export interface BedGroup {
  id: string;
  idExpedia: string;
  description: string;
  configurations: Configuration[];
}

export interface Configuration {
  type: string;
  size: string;
  quantity: number;
}

export interface View {
  id: string;
  idExpedia: string;
  name: string;
}

export interface Area {
  squareMeters?: number;
  squareFeet?: number;
}

export interface Address {
  lineOne: string;
  postalCode: string;
  obfuscationRequired: boolean;
}

export interface Chain {
  name: string;
}

export interface Brand {
  name: string;
}

export interface Statistic {
  name: string;
  value: string;
}

export interface IAmenitie {
  id: string;
  idExpedia: string;
  name: string;
}
export interface IDescription {
  amenities: string;
  dining: string;
  businessAmenities: string;
  rooms: string;
  attractions: string;
  location: string;
  headline: string;
}

export interface ICity {
  id: string;
  name: string;
}
export interface IAdress {
  lineOne: string;
  lineTwo: string;
  lineTree: string;
  city: string;
  state_province_code: string;
  postalCode: string;
  country_code: string;
}

export interface Ancestor {
  id: string;
  name: string;
  idExpedia: string;
}

export interface Category {
  name: string;
  id: string;
  modificationDate: string;
  creationDate: string;
  idExpedia: string;
}

export interface GuestRatings {
  count: number;
  overall: number;
  cleanliness: number;
  service: number;
  comfort: number;
  condition: number;
  location: number;
  recommendationPercent: number;
  neighborhood: number;
  quality: number;
  value: number;
  amenities: number;
  recommendation_percent: number;
}

export interface Location {
  longitude: number;
  latitude: number;
}

export interface IPriceCheckRequest {
  priceCheckId: string;
  createItinerary: {
    affiliate_reference_id: string;
    hold: true;
    email: string;
    phone: {
      country_code: string;
      area_code: string;
      number: string;
    };
    rooms: {
      given_name: string;
      family_name: string;
      smoking: string;
      special_request: string;
      loyalty_id: string;
    }[];
    payments: {
      type: string;
      number: string;
      security_code: string;
      expiration_month: string;
      expiration_year: string;
      billing_contact: {
        given_name: string;
        family_name: string;
        address: IAdress;
      };
      third_party_authentication: {
        cavv: string;
        eci: string;
        three_ds_version: string;
        ds_transaction_id: string;
        pa_res_status: string;
        ve_res_status: string;
        xid: string;
        cavv_algorithm: string;
        ucaf_indicator: string;
      };
      enrollment_date: string;
    }[];
    affiliate_metadata: string;
    tax_registration_number: string;
    traveler_handling_instructions: string;
  };
}

export interface IHotel {
  id: string;
  name: string;
  score: number;
  links: Links;
  stars: number;

  guestRatings: {
    overall: number;
  };
  images: any[];
  token: string;
  location: { longitude: number; latitude: number };
}

export interface OccupancyPricing {
  adults: number;
  childrenAge: number[] | number;
  nightly: Nightly[][];
  stay: Nightly[];
  totals: Totals;
  feesBillable: FeesBillable;
  feesRequested: FeesBillable;
}
export interface FeesBillable {
  resortFee: ResortFee;
  mandatoryTax: MandatoryTax;
}
export interface ResortFee {
  value: number;
  currency: string;
}
export interface Totals {
  inclusive: Inclusive;
  exclusive: Exclusive;
  propertyFees?: PropertyFees;
  inclusiveStrikethrough?: PropertyFees;
}

export interface Nightly {
  type: string;
  value: number;
  currency: string;
}

export interface Inclusive {
  value: number;
  currency: string;
}

export interface Exclusive {
  value: number;
  currency: string;
}

export interface MandatoryTax {
  value: number;
  currency: string;
}

export interface Links {}

export interface CancelPenalty {
  currency: string;
  start: string;
  end: Date;
  nights?: number;
}

export interface Links2 {}

export interface IAvailabityRoomRequest {
  checkIn: Date;
  checkOut: Date;
  currency: string;
  countryCode: string;
  language: string;
  occupancy: string[];
  propertyIdExpedia: string[];
  ratePlanCount: string;
}

export interface IAvailabityRoom {
  property_id: string;
  status: string;
  score: string;
  min_price: number;
  rooms: {
    id: string;
    room_name: string;
    rates: {
      id: string;
      status: string;
      available_rooms: string;
      amenities: {
        [x: string]: {
          id: string;
          name: string;
          valeu: null;
        };
      };
      occupancy_pricing: {
        [x: string]: {
          nightly: Array<
            [
              {
                type: string;
                value: string;
                currency: string;
              }
            ]
          >;
          stay: null;
          totals: {
            inclusive: {
              billable_currency: {
                value: string;
                currency: string;
              };
              request_currency: {
                value: string;
                currency: string;
              };
            };
            exclusive: {
              billable_currency: {
                value: string;
                currency: string;
              };
              request_currency: {
                value: string;
                currency: string;
              };
            };
            inclusive_strikethrough: null;
            strikethrough: null;
            marketing_fee: {
              billable_currency: {
                value: string;
                currency: string;
              };
              request_currency: {
                value: string;
                currency: string;
              };
            };
            gross_profit: {
              billable_currency: {
                value: string;
                currency: string;
              };
              request_currency: {
                value: string;
                currency: string;
              };
            };
            minimum_selling_price: null;
            property_fees: null;
          };
          fees: null;
        };
      };
      bed_groups: {
        [x: string]: {
          id: string;
          links: {
            price_check: {
              method: string;
              href: string;
              expires: null;
            };
          };
          description: string;
          configuration: {
            type: string;
            size: string;
            quantity: string;
          }[];
        };
      };
      cancel_penalties: {
        currency: string;
        start: string;
        end: string;
        amount: null;
        nights: null;
        percent: string;
      }[];
    }[];
  }[];
  links: {
    recommendations: {
      method: string;
      href: string;
      expires: null;
    };
  };
}

export interface IPriceCheck {
  status: string;
  occupancy_pricing: {
    [x: string]: {
      nightly: Array<
        {
          type: string;
          value: string;
          currency: string;
        }[]
      >;
      stay: null;
      totals: {
        inclusive: {
          billable_currency: {
            value: string;
            currency: string;
          };
          request_currency: {
            value: string;
            currency: string;
          };
        };
        exclusive: {
          billable_currency: {
            value: string;
            currency: string;
          };
          request_currency: {
            value: string;
            currency: string;
          };
        };
        inclusive_strikethrough: null;
        strikethrough: null;
        marketing_fee: {
          billable_currency: {
            value: string;
            currency: string;
          };
          request_currency: {
            value: string;
            currency: string;
          };
        };
        gross_profit: {
          billable_currency: {
            value: string;
            currency: string;
          };
          request_currency: {
            value: string;
            currency: string;
          };
        };
        minimum_selling_price: null;
        property_fees: null;
      };
      fees: null;
    };
  };
  links: {
    book: {
      method: string;
      href: string;
      expires: null;
    };
  };
}
export interface IReview {
  verified: {
    recent: {
      verification_source: string;
      title: null | string;
      date_submitted: Date;
      rating: string;
      reviewer_name: string;
      trip_reason: null | string;
      travel_companion: null | string;
      text: string;
    }[];
  };
}

export interface Inclusive {
  value: number;
  currency: string;
}

export interface Exclusive {
  value: number;
  currency: string;
}

export interface PropertyFees {
  value: number;
  currency: string;
}

export interface Fees {
  [x: string]: MandatoryTax;
}

export interface MandatoryTax {
  value: number;
  currency: string;
}

export interface BedGroup {
  [x: string]: string | any;
  id: string;
  links: Links;
  checkPriceToken: string;
  description: string;
}

export interface Links3 {}

export interface GuestRatings {
  overall: number;
}

export interface IAutoComplete {
  offSet?: number;
  size?: number;
  toWork?: boolean;
  checkIn?: Date | string;
  checkOut?: Date | string;
  pageSize: number;
  currency: string;
  countryCode: string;
  page: number;
  rooms: {
    adults: number;
    childrenAge: number[];
  }[];
  destiny?: {
    id: string;
    type: string;
  };
}
