export interface IGetRegion {
  payload: Payload
}

export interface Payload {
  id: string
  idExpedia: string
  type: string
  name: string
  fullName: string
  properties: Property[]
  descendants: Descendant[]
  tags: string[]
  multiCityVicinity: MultiCityVicinity
  countryCode: string
  coordinates: Coordinates
  polygonId: string
  emphasis: boolean
}

export interface Property {
  id: string
  name: string
  idExpedia: string
  category: Category
  rank: number
  stars?: number
}

export interface Category {
  id: string
  name: string
  idExpedia: string
}

export interface Descendant {
  id: string
  name: string
  type: string
}

export interface MultiCityVicinity {
  id: string
  name: string
  type: string
}

export interface Coordinates {
  longitude: number
  latitude: number
}
