export interface IAvailables {
  searchId: string;
  pageNumber: number;
  totalAvailables: number;
  totalPages: number;
  pageSize: number;
  mainProperty: any;
  availables: Available[];
  filters: Filter[];
  orders: string[];
}

export interface Available {
  propertyIdTBI: string;
  supplierId: string;
  propertyName: string;
  rooms: Room[];
  amenities: Amenity3[];
  allAmenities: AllAmenity[];
  amenitiesCategories: string[];
  photos: Photo2[];
  stars: string;
  propertyType: string;
  hotelDescription: any;
  guestRatings: GuestRatings;
  available: boolean;
  score: string;
  address: string;
  phone1: any;
  phone2: any;
  zipCode: string;
  webSite: any;
  localization: Localization;
  pointOfInterests: any;
  checkInBegin: any;
  checkOut: any;
  warning: any;
}

export interface Room {
  roomName: string;
  roomId: string;
  roomDescription: string;
  amenities: Amenity[];
  photos: Photo[];
  bedConfiguration: BedConfiguration[];
  roomSize: number;
  viewDescription: any;
  childrenTotal: number;
  adultTotal: number;
  rates: Rate[];
  available: any;
  mediaRoomId: any;
}

export interface Amenity {
  name: string;
}

export interface Photo {
  description: string;
  urlSize70px: string;
  urlSize350px: string;
  urlSize1000px: string;
}

export interface BedConfiguration {
  token: string;
  description: string;
}

export interface Rate {
  rateId: string;
  bedConfiguration: BedConfiguration2[];
  amenities: Amenity2[];
  rateDescription: any;
  availbleQuantity: number;
  refundable: boolean;
  allInclusive: boolean;
  totalFeesRequest: TotalFeesRequest;
  totalFeesBilling: TotalFeesBilling;
  perNight: PerNight[];
  averagePerNight: AveragePerNight;
  totalRound: TotalRound;
  totalPriceInclusive: TotalPriceInclusive;
  totalPriceExclusive: TotalPriceExclusive;
  totalPriceInclusiveStrikethrough: TotalPriceInclusiveStrikethrough;
  totalPriceExclusiveStrikethrough: TotalPriceExclusiveStrikethrough;
  mandatoryFeesRequest: any;
  mandatoryTaxRequest: MandatoryTaxRequest;
  resortFeesRequest: ResortFeesRequest;
  mandatoryFeesBilling: any;
  mandatoryTaxBilling: MandatoryTaxBilling;
  resortFeesBilling: ResortFeesBilling;
  cancelPenalties: CancelPenalty[];
  nonRefundableDataRage: any[];
}

export interface BedConfiguration2 {
  token: string;
  description: string;
}

export interface Amenity2 {
  name: string;
}

export interface TotalFeesRequest {
  value: number;
  currency: string;
}

export interface TotalFeesBilling {
  value: number;
  currency: string;
}

export interface PerNight {
  value: number;
  currency: string;
}

export interface AveragePerNight {
  value: number;
  currency: string;
}

export interface TotalRound {
  value: number;
  currency: string;
}

export interface TotalPriceInclusive {
  value: number;
  currency: string;
}

export interface TotalPriceExclusive {
  value: number;
  currency: string;
}

export interface TotalPriceInclusiveStrikethrough {
  value: number;
  currency: string;
}

export interface TotalPriceExclusiveStrikethrough {
  value: number;
  currency: string;
}

export interface MandatoryTaxRequest {
  displayText: string;
  priceTotal: PriceTotal;
  pricePerNight: PricePerNight;
}

export interface PriceTotal {
  value: number;
  currency: string;
}

export interface PricePerNight {
  value: number;
  currency: string;
}

export interface ResortFeesRequest {
  displayText: string;
  priceTotal: PriceTotal2;
  pricePerNight: PricePerNight2;
}

export interface PriceTotal2 {
  value: number;
  currency: string;
}

export interface PricePerNight2 {
  value: number;
  currency: string;
}

export interface MandatoryTaxBilling {
  displayText: string;
  priceTotal: PriceTotal3;
  pricePerNight: PricePerNight3;
}

export interface PriceTotal3 {
  value: number;
  currency: string;
}

export interface PricePerNight3 {
  value: number;
  currency: string;
}

export interface ResortFeesBilling {
  displayText: string;
  priceTotal: PriceTotal4;
  pricePerNight: PricePerNight4;
}

export interface PriceTotal4 {
  value: number;
  currency: string;
}

export interface PricePerNight4 {
  value: number;
  currency: string;
}

export interface CancelPenalty {
  text: string;
  startDate: string;
  endDate: string;
}

export interface Amenity3 {
  name: string;
}

export interface AllAmenity {
  name: string;
}

export interface Photo2 {
  description: string;
  urlSize70px: string;
  urlSize350px: string;
  urlSize1000px: string;
}

export interface GuestRatings {
  count: number;
  overall: number;
  cleanliness: number;
  service: number;
  comfort: number;
  condition: number;
  location: number;
  recommendationPercent: number;
  neighborhood: number;
  quality: number;
  value: number;
  amenities: number;
  recommendation_percent: number;
}

export interface Localization {
  latitude: number;
  longitude: number;
}

export interface Filter {
  filterName: string;
  filterValues: FilterValue[];
  filterDisplayName: string;
  filterQuery: string;
}

export interface FilterValue {
  displayValue: string;
  query: string;
}
