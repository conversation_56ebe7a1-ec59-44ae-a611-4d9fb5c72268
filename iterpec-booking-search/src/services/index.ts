import axios, { AxiosRequestHeaders, AxiosInstance, AxiosHeaders } from "axios";
import {
  IAutoComplete,
  IReview,
  IAvailables,
  IStaticData,
  TopLevel,
  IGetRegion,
  IGetFilters,
  IFilterAutoComplete,
  Filter as IFilters,
  IFiltersRequest,
} from "./types/index";
//@ts-ignore
import { getRegion, getSearch, getUser } from "@iterpec/booking-utility";
import { Static } from "./types/getStaticData";

class Api {
  private expediaApi: AxiosInstance;
  private staticApi: AxiosInstance;
  private expediaLocalApi: AxiosInstance;
  private headers: AxiosRequestHeaders;
  private language: string;
  private searchId: string;
  private client: string;
  private token: string;
  private user: string;

  constructor() {
    this.language = getRegion()?.language;
    this.searchId = getSearch()?.searchId;
    this.token = getUser()?.accessToken ?? null;
    this.user = getUser()?.idUser ?? null;
    this.client = getUser()?.clientId ?? null;

    // Criando uma instância de AxiosHeaders
    this.headers = new AxiosHeaders();
    this.headers.set("Access-Control-Allow-Origin", "*");
    this.headers.set("Content-Type", "application/json;charset=utf-8");
    this.headers.set("Cache-Control", "no-cache");
    this.headers.set("language", this.language);
    this.headers.set("X-UserId", this.user);
    this.headers.set(
      "x-clientId",
      this.client ? this.client : "127f50b0-9789-451b-b9a0-057a619420b5"
    );

    this.headers.set("crossDomain", "true");
    this.headers.set("Authorization", `Bearer ${this.token}`);

    this.expediaApi = axios.create({
      baseURL: process.env.API_SEARCH,
      timeout: 400000,
      headers: this.headers,
    });
    this.staticApi = axios.create({
      baseURL: process.env.API_SEARCH,
      timeout: 40000,
      headers: this.headers,
    });
  }

  async getAvailabilityNextPage(searchId: string, pageNumber: string | number) {
    return await this.expediaApi.get<IAvailables>(
      `/search/availability/updated/page/` + searchId + `/` + pageNumber,

      { headers: { language: this.language } }
    );
  }

  async getAvailability(data: IAutoComplete) {
    return await this.expediaApi.post<IAvailables>(
      `/search/availability/updated/search`,
      data,
      { headers: { language: this.language } }
    );
  }

  async getAllAvailability(data: { searchId: string; returnLimit?: number }) {
    return await this.expediaApi.post<IAvailables>(
      `/search/availability/all`,
      data,
      { headers: { language: this.language } }
    );
  }

  async getRegion(regionId: string) {
    return await this.staticApi.get<IGetRegion>(
      `/static-data/region/` + regionId,
      { headers: { language: this.language } }
    );
  }

  async getFilters(searchId: string) {
    return await this.expediaApi.get<IGetFilters>(
      `/search/availability/filters?searchId=` + searchId,
      { headers: { language: this.language } }
    );
  }

  async getAutoCompleteFilters(
    searchId: string,
    mainTerm: string,
    returnLimit = 6
  ) {
    return await this.expediaApi.post<Array<IFilterAutoComplete>>(
      `/search/availability/autocomplete`,
      { searchId, mainTerm, returnLimit },
      { headers: { language: this.language } }
    );
  }

  async setFilter({ searchId, ...data }: IFiltersRequest) {
    return await this.expediaApi.post<IAvailables>(
      `/search/availability/updated/filter/` + searchId,
      data,
      { headers: { language: this.language } }
    );
  }

  async getStaticData(propertyId: string) {
    return await this.staticApi.get<Static>(
      `/static-data/property/` + propertyId,
      { headers: { language: this.language } }
    );
  }

  /*   async adicionalRates(
    data: IAutoComplete & { propertyId: string; token: string }
  ) {
    return await this.expediaApi.post(
      `/search/availability/additonalRates`,
      { ...data, searchId: this.searchId },
      { headers: { language: this.language } }
    );
  } */

  async adicionalRates(data: { searchId: string; supplierId: string }) {
    return await this.expediaApi.get<IAvailables>(
      `/search/availability/updated/additional-rates/` +
        data.searchId +
        "/" +
        data.supplierId
    );
  }

  async getReviews(data: { searchId: string; propertyId: string }) {
    return await this.expediaApi.get<IReview>(
      `/search/availability/updated/guest-reviews/${data.searchId}/${data.propertyId}`,
      {
        headers: { language: this.language },
      }
    );
  }
}

export default Api;
