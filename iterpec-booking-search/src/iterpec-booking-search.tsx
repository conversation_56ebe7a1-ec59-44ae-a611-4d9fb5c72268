import React from "react";
import ReactDOM from "react-dom";
import singleSpaReact from "single-spa-react";
import Root from "./App";
import './i18n';  
const lifecycles = singleSpaReact({
  React,
  ReactDOM,
  rootComponent: Root,
  loadRootComponent: (props) =>
    new Promise((resolve, reject) =>
      resolve(() => {
        return <Root {...props} />;
      })
    ),
  errorBoundary(err, info, props) {
    // Customize the root error boundary for your microfrontend here.
    return null;
  },
});

export const { bootstrap, mount, unmount } = lifecycles;
