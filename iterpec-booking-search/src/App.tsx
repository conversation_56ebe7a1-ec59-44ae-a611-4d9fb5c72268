import { Provider } from "@iterpecdev/theme-system";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { Routes } from "./Routes";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";
//@ts-ignore
import { Hooks } from "@iterpec/booking-utility";
const App: React.FC<any> = (props) => {
  const { i18n, t } = useTranslation();
  const useTheme = Hooks.useTheme();

  useEffect(() => {
    window.addEventListener(
      "languageChange",
      (customEvent: CustomEvent<{ language: string; languageCode: string }>) => {
        i18n.changeLanguage(customEvent?.detail?.language);
      }
    );
  }, []);
  return (
    <Provider theme={useTheme}>
      <BrowserRouter>
        <Routes {...props} />
      </BrowserRouter>
    </Provider>
  );
};

export default App;
