import React from "react";
import { render } from "@testing-library/react";
import { ThemeProvider } from "styled-components";
import { iterpec } from "@iterpecdev/theme-system";
import * as Themes from "@iterpecdev/theme-system";

type themeEnum = "iterpec";

const { Provider, ...nameThemes } = Themes;
const themes = Object.keys(nameThemes).reduce((acc, key) => {
  //@ts-ignore
  acc[key] = nameThemes[key];
  return acc;
}, {});

export const styledRender = (
  ui: JSX.Element,
  theme = "iterpec",
  options = {}
) => {
  return render(ui, {
    wrapper: (props: any) => {
      const themeList = themes;
      //@ts-ignore
      return (
        <ThemeProvider theme={themeList[theme]}>{props.children}</ThemeProvider>
      );
    },
    ...options,
  });
};
