import React, { useEffect } from "react";
import { Routes as Switch, Route } from "react-router-dom";
import Search from "../components/Search";
import HotelDetails from "../components/HotelDetails";

export const Routes = (props) => {
  /* useEffect(() => {
    console.log(props);
  }, []); */
  return (
    <Switch>
      <Route path="/search" element={<Search />} />
      <Route path="/search/hotel" element={<HotelDetails />} />
    </Switch>
  );
};
