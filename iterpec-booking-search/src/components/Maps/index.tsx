import React, { useCallback, useEffect, useRef, useState } from "react";
import * as Styles from "./styles";
import GoogleMapReact from "google-map-react";
import { <PERSON><PERSON>, Chips, Drawer } from "@iterpecdev/design-sistem";
import { Available, IHotel, ISearch } from "../../services/types";
interface ButtonGroup {
  indices: number[]; // Todos os índices dos botões no grupo
  center: { lat: number; lng: number };
}
import Marker from "../Marker";
import CardHotel from "../CardHotel";
import Api from "../../services";
const Maps: React.FC<{
  searchCoordenate: {
    latitude: number;
    longitute: number;
  };
  hotels: Partial<Array<Available & ISearch["payload"]>>;
  searchId: string;
}> = ({ hotels: initialHotel, searchCoordenate, searchId }) => {
  const api = new Api();
  const [hotel, setHotel] = useState<Available & ISearch["payload"]>(null);
  const [hotels, setHotels] = useState<
    Partial<Array<Available & ISearch["payload"]>> | Available[]
  >(initialHotel);
  const center = {
    lat: searchCoordenate?.latitude,
    lng: searchCoordenate?.longitute,
  };
  const defaultProps = {
    center,
    zoom: 12,
  };
  const buttonsRef = useRef<(HTMLDivElement | null)[]>([]);
  const [buttonGroups, setButtonGroups] = useState<Array<ButtonGroup>>([]);
  const [open, setOpen] = useState(false);
  const [render, setRender] = useState(false);
  const handleDrawer = () => setOpen(!open);
  const handleHotel = (hotel) => {
    handleDrawer();
    setHotel(hotel);
  };
  const getAllHotels = async () => {
    try {
      const { data } = await api.getAllAvailability({
        searchId,
        returnLimit: 100,
      });
      setHotels(data.availables);
      handleButtons();
    } catch (error) {}
  };
  const handleButtons = () => {
    const newButtonGroups: ButtonGroup[] = [];
    const processedButtons = new Set<number>();

    for (let i = 0; i < buttonsRef.current.length; i++) {
      if (processedButtons.has(i)) continue;

      const overlappingWith: number[] = [];
      let latSum = 0;
      let lngSum = 0;
      let count = 0;

      for (let j = 0; j < buttonsRef.current.length; j++) {
        if (i !== j && !processedButtons.has(j)) {
          const button1 = buttonsRef.current[i]?.getBoundingClientRect();
          const button2 = buttonsRef.current[j]?.getBoundingClientRect();

          if (button1 && button2) {
            if (
              button1.left < button2.right &&
              button1.right > button2.left &&
              button1.top < button2.bottom &&
              button1.bottom > button2.top
            ) {
              overlappingWith.push(j);

              // Somar as coordenadas de latitude e longitude dos botões sobrepostos
              const btnJ = buttonsRef.current[j];
              const lngJ = parseFloat(btnJ?.getAttribute("lng") ?? "0");
              const latJ = parseFloat(btnJ?.getAttribute("lat") ?? "0");

              lngSum += lngJ;
              latSum += latJ;
              count++;
              processedButtons.add(j); // Marcar botão j como processado
            }
          }
        }
      }

      if (count > 0) {
        // Incluir o botão atual no grupo
        overlappingWith.push(i);
        const btnI = buttonsRef.current[i];
        const lngI = parseFloat(btnI?.getAttribute("lng") ?? "0");
        const latI = parseFloat(btnI?.getAttribute("lat") ?? "0");

        lngSum += lngI;
        latSum += latI;
        count++;
        processedButtons.add(i); // Marcar botão i como processado

        const center = {
          lng: lngSum / count,
          lat: latSum / count,
        };

        newButtonGroups.push({
          indices: overlappingWith,
          center,
        });
      }
    }

    setButtonGroups(newButtonGroups);
  };
  useEffect(() => {
    handleButtons();
  }, [buttonsRef.current, hotels.length]);
  const handleApiLoaded = (map, maps) => {
    /*  console.log(map);
    console.log(maps); */
    handleButtons();
  };

  const isButtonInGroup = (buttonIndex: number): boolean => {
    return buttonGroups.some((group) => group.indices.includes(buttonIndex));
  };
  useEffect(() => {
    /*   getAllHotels(); */
  }, []);
  return (
    <Styles.WrapperMaps>
      <GoogleMapReact
        bootstrapURLKeys={{ key: process.env.API_GOOGLE_KEY }}
        center={defaultProps.center}
        defaultZoom={defaultProps.zoom}
        onGoogleApiLoaded={({ map, maps }) => handleApiLoaded(map, maps)}
        options={{
          clickableIcons: false,
          fullscreenControl: false,
          keyboardShortcuts: false,
          streetViewControl: false,
          mapTypeControl: false,
          minZoom: 9,
          maxZoom: 18,
        }}
        onChange={handleButtons}
      >
        {[
          ...hotels?.map((hotel, index) => {
            const rate = hotel?.rooms[0]?.rates?.[0] ?? null;
            var occupancyPricing = rate
              ? rate?.totalPriceInclusive.value
              : null;
            return (
              <Marker
                key={hotel.id}
                visible={!isButtonInGroup(index)}
                btnRef={(el) => (buttonsRef.current[index] = el)}
                value={
                  //@ts-ignore
                  occupancyPricing ?? 0
                }
                onClick={() => {
                  handleHotel(hotel);
                }}
                //@ts-ignore
                lng={hotel?.location?.longitude ?? center.lng}
                //@ts-ignore
                lat={hotel?.location?.latitude ?? center.lat}
              />
            );
          }),
          ...buttonGroups.map((group, index) => {
            return (
              <Marker
                key={index}
                //@ts-ignore
                lng={group.center.lng}
                //@ts-ignore
                lat={group.center.lat}
                groupValue={group.indices.length}
              />
            );
          }),
        ]}
      </GoogleMapReact>
      {open ? (
        <Drawer position="bottom" open={open} onDismiss={handleDrawer}>
          <CardHotel data={hotel} />
        </Drawer>
      ) : null}
    </Styles.WrapperMaps>
  );
};
export default Maps;
