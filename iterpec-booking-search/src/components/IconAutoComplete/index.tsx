import Icon, { IconProps } from "@iterpecdev/icons-system";
import React from "react";

const IconAutoComplete: React.FC<{ destiny: { type: string } }> = ({
  destiny,
}) => {
  const { type }: { [x: string]: string } = destiny;
  const ICONSAUTOCOMPETE: { [x: string]: IconProps["name"] } = {
    BUS_STATION: "BusStop",
    AIRPORT: "AirplaneLanding",
    METRO_STATION: "SubwayVariant",
    CITY: "MapMarkerOutline",
    PROVINCE_STATE: "MapLegend",
    MULTI_CITY_VICINITY: "MapLegend",
    HIGH_LEVEL_REGION: "MapLegend",
    NEIGHBORHOOD: "MapMarkerPath",
    POINT_OF_INTEREST: "MapMarkerPath",
    TRAIN_STATION: "SubwayVariant",
    PROPERTY: "Bed",
    SEARCH: "SearchSelectorsSearchZoomSearchNormal",
  };

  return <Icon name={ICONSAUTOCOMPETE[type]} />;
};

export default IconAutoComplete;
