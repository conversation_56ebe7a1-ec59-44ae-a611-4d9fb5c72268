import {
  Checkbox,
  Button,
  Input,
  Divider,
} from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import { Filter as ISearchFilter } from "../../services/types";
import { Fragment, useEffect, useState, useRef, useCallback, useMemo } from "react";
import Api from "../../services";
import IconAutoComplete from "../IconAutoComplete";
import { IFilterAutoComplete } from "../../services/types";
import MultiRangeSlider from "../InputRage";
import { useTranslation } from "react-i18next";
import { IFilters } from "../Search";
import Stars from "../Stars";
//@ts-ignore
import { Hooks } from "@iterpec/booking-utility";

const Filter = ({
  handleFilters,
  filters,
  searchId,
}: {
  handleFilters: (e: IFilters[]) => Promise<void>;
  filters: ISearchFilter[];
  searchId: string;
}) => {
  const { t } = useTranslation();
  const api = useMemo(() => new Api(), []);
  const [propertyID, setPropertyID] = useState("");
  const priceRange = useMemo(() => {
    const priceFilter = filters?.find((filter) => filter.filterName === "price");
    return priceFilter?.filterValues.reduce((acc, item) => {
      acc[item.query] = parseFloat(item.displayValue);
      return acc;
    }, {} as { minPrice?: number; maxPrice?: number }) || {};
  }, [filters]);
  const [price, setPrice] = useState<{ min: number; max: number }>({
    max: priceRange?.maxPrice ?? 99999,
    min: priceRange?.minPrice ?? 100,
  });
  const [inputSearch, setInputSearch] = useState("");
  const [term, setTerm] = useState("");
  const [autocomplete, setAutocomplete] = useState<IFilterAutoComplete[]>([]);
  const [stars, setStars] = useState<string[]>([]);
  const [filtersList, setFiltersList] = useState<IFilters[]>([]);
  const isMobile = Hooks.useMobileDetect().isMobile();
  const lastFiltersSent = useRef<IFilters[] | null>(null);

  const initialMinPrice = priceRange?.minPrice;
  const initialMaxPrice = priceRange?.maxPrice;

  const resetFilters = useCallback(() => {
    setPrice({
      max: priceRange?.maxPrice ?? 99999,
      min: priceRange?.minPrice ?? 100,
    });
    setPropertyID("");
    setInputSearch("");
    setTerm("");
    setAutocomplete([]);
    setStars([]);
    setFiltersList([]);
  }, [priceRange?.maxPrice, priceRange?.minPrice]);

  const handleAmenities = useCallback((checked: boolean, filterQuery: string, query: string) => {
    setFiltersList((prev) => {
      const existingFilter = prev.find((filter) => filter.filterQuery === filterQuery);
      if (!checked) {
        if (existingFilter) {
          const updatedValues = existingFilter.filterValues.filter((value) => value.query !== query);
          if (updatedValues.length > 0) {
            return prev.map((filter) =>
              filter.filterQuery === filterQuery
                ? { ...filter, filterValues: updatedValues }
                : filter
            );
          }
          return prev.filter((filter) => filter.filterQuery !== filterQuery);
        }
        return prev;
      }
      if (existingFilter) {
        return prev.map((filter) =>
          filter.filterQuery === filterQuery
            ? { ...filter, filterValues: [...filter.filterValues, { query }] }
            : filter
        );
      }
      return [...prev, { filterQuery, filterValues: [{ query }] }];
    });
    if (filterQuery === "STARS") {
      setStars((prev) =>
        checked ? [...prev, query] : prev.filter((star) => star !== query)
      );
    }
  }, []);

  const getAutoComplete = useCallback(async (value: string) => {
    if (!value) return;
    try {
      const { data } = await api.getAutoCompleteFilters(searchId, value);
      setAutocomplete(data);
    } catch (error) {
      // Pode logar ou tratar erro se necessário
    }
  }, [api, searchId]);

  const setValueDestiny = useCallback((destiny: string) => {
    setInputSearch(destiny);
  }, []);

  const setHotel = useCallback((destiny: IFilterAutoComplete) => {
    setInputSearch(destiny.name);
    setPropertyID(destiny.id);
  }, []);

  const handlePriceFilter = useCallback((min: number, max: number) => {
    const priceFilterQuery = "PRICE";
    if (min === initialMinPrice && max === initialMaxPrice) {
      setFiltersList((prev) =>
        prev.filter((filter) => filter.filterQuery !== priceFilterQuery)
      );
      return;
    }
    setFiltersList((prev) => {
      const existingFilter = prev.find((filter) => filter.filterQuery === priceFilterQuery);
      if (existingFilter) {
        return prev.map((filter) =>
          filter.filterQuery === priceFilterQuery
            ? {
                ...filter,
                filterValues: [
                  { query: `minPrice`, displayValue: min },
                  { query: `maxPrice`, displayValue: max },
                ],
              }
            : filter
        );
      }
      return [
        ...prev,
        {
          filterQuery: priceFilterQuery,
          filterValues: [
            { query: `minPrice`, displayValue: min },
            { query: `maxPrice`, displayValue: max },
          ],
        },
      ];
    });
  }, [initialMinPrice, initialMaxPrice]);

  const handlePrice = useCallback((newPrice) => {
    if (newPrice?.min !== price?.min || newPrice?.max !== price?.max) {
      setPrice(newPrice);
      handlePriceFilter(newPrice.min, newPrice.max);
    }
  }, [price?.min, price?.max, handlePriceFilter]);

  useEffect(() => {
    const resetListener = () => resetFilters();
    window.addEventListener("resetFilters", resetListener);
    return () => window.removeEventListener("resetFilters", resetListener);
  }, [resetFilters]);

  useEffect(() => {
    resetFilters();
  }, [searchId, resetFilters]);

  useEffect(() => {
    if (!inputSearch) {
      setAutocomplete([]);
      return;
    }
    const timeout = setTimeout(() => {
      getAutoComplete(inputSearch);
    }, 500);
    return () => clearTimeout(timeout);
  }, [inputSearch, getAutoComplete]);

  useEffect(() => {
    if (isMobile || !filtersList.length) return;
    const isSame = JSON.stringify(filtersList) === JSON.stringify(lastFiltersSent.current);
    if (isSame) return;
    const timeout = setTimeout(() => {
      handleFilters(filtersList);
      lastFiltersSent.current = filtersList;
    }, 2000);
    return () => clearTimeout(timeout);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filtersList, isMobile, handleFilters]);

  return (
    <Styles.WrapperOrder>
      <div className="WrapperRow">
        <Styles.WrapperAutoComplete>
          <Input
            disabled={!searchId}
            placeholder={t("filter.searchPlaceholder")}
            label={t("filter.searchLabel")}
            type="search"
            value={inputSearch}
            name="destiny"
            autoComplete="off"
            submitSearch={() => getAutoComplete(inputSearch)}
            onChangeValue={setValueDestiny}
          />
          <div className="autoComplete">
            {!!autocomplete.length &&
              autocomplete.map((destiny, index) => (
                <Fragment key={index}>
                  <Styles.WrapperItemAutoComplete
                    onClick={() => setHotel(destiny)}
                  >
                    <IconAutoComplete destiny={destiny} />
                    <div>
                      <p>{destiny.name}</p>
                      <p>
                        {destiny?.localization
                          ? `${destiny?.localization} `
                          : ""}
                      </p>
                    </div>
                  </Styles.WrapperItemAutoComplete>
                  <Divider />
                </Fragment>
              ))}
            {inputSearch && (
              <Fragment>
                <Styles.WrapperItemAutoComplete
                  onClick={() => setTerm(inputSearch)}
                >
                  <IconAutoComplete destiny={{ type: "SEARCH" }} />
                  <div>
                    <p>Buscar {inputSearch}</p>
                  </div>
                </Styles.WrapperItemAutoComplete>
                <Divider />
              </Fragment>
            )}
          </div>
        </Styles.WrapperAutoComplete>
        <div className="WrapperPrice">
          <h3>{t("filter.yourBudget")}</h3>
          <div className="WrapperRange">
            <MultiRangeSlider
              max={priceRange?.maxPrice ?? 99999}
              min={priceRange?.minPrice ?? 0}
              value={price && price.max && price.min ? price : null}
              onChange={handlePrice}
            />
          </div>
        </div>
        {filters?.map((field) => {
          if (field.filterName === "price") return null;
          return (
            <div key={field.filterName}>
              <h3>{field.filterDisplayName}</h3>
              {field.filterValues?.map((option, index) => {
                if (field.filterName === "stars") {
                  return (
                    <Styles.WrapperStars key={index}>
                      <Checkbox
                        checked={stars.includes(option.query)}
                        onChangeValue={(e) =>
                          handleAmenities(e, field.filterQuery, option.query)
                        }
                      >
                        <Stars stars={option.query} />
                      </Checkbox>
                    </Styles.WrapperStars>
                  );
                }
                return (
                  <Fragment key={index}>
                    <Checkbox
                      onChangeValue={(e) =>
                        handleAmenities(e, field.filterQuery, option.query)
                      }
                    >
                      {option.displayValue}
                    </Checkbox>
                  </Fragment>
                );
              })}
            </div>
          );
        })}
      </div>
      {isMobile && (
        <Button onClick={() => handleFilters(filtersList)}>
          {t("filter.filterButton")}
        </Button>
      )}
    </Styles.WrapperOrder>
  );
};

export default Filter;
