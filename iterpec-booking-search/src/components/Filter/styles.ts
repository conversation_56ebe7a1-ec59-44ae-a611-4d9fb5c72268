import styled from "styled-components";
export const WrapperStars = styled.div`
  & > div {
    padding-left: 0;
  }
  svg {
    width: 24px;
  }
`;

export const WrapperOrder = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: space-between;
  height: 95%;
  overflow: hidden;
  ${(props) =>
    props.theme.breakpoints.desktop(`
      min-width: 250px;
    `)}
  & > div.WrapperRow {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    height: 98%;
    gap: 25px;
    overflow: auto;
    ${(props) =>
      props.theme.breakpoints.desktop(`
        overflow: hidden;
    `)}
    & > div.WrapperPrice {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      padding-bottom: 20px;
      & > div.WrapperRange {
        margin: 15px 0;
        width: 98%;
      }
      & > div.WrapperInput {
        display: flex;
        gap: 4%;
        & > div {
          width: 48%;
        }
      }
    }
    & > div {
      display: flex;
      flex-direction: column;
      gap: 5px;
      & > div {
        padding-left: 0;
      }
      & > span {
        height: 20px;
        margin: 12px 0;
      }

      & > h3 {
        font-size: 18px;
        margin-bottom: 8px;
      }
      & > h3,
      h4 {
        color: #000;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 133.333% */
      }
      & > h4 {
        font-size: 16px;
        margin: 10px 0;
      }
    }
    ${(props) =>
      props.theme.breakpoints.desktop(`
        height: 100%;
        overflow: show;
        label{
          font-size: 13px;
        }
         & > div {
          & > h3 {
            font-size: 16px;
            margin-bottom: 8px;
          }
          & > h3,
          h4 {
            color: #000;
            font-style: normal;
            font-weight: 600;
            line-height: 24px; /* 133.333% */
          }
          & > h4 {
            font-size: 16px;
            margin: 10px 0;
          }
      }
    `)}
  }
  & > button {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }
`;
export const WrapperItemAutoComplete = styled.div`
  padding: 12px 41px 12px 16px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
  & > div > :first-child {
    font-weight: 600;
  }
  & > svg {
    min-width: 26px;
    width: 26px;
    height: 26px;
    fill: #404040;
  }
`;

export const WrapperAutoComplete = styled.div`
  font-family: Inter;
  position: relative;
  & > div.autoComplete {
    background-color: #fff;
    width: 100%;
    min-height: 72vh;
    overflow-x: auto;
    position: absolute;
    left: 0;
    top: 100%;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0.5ms;
    z-index: 99;
  }
  label {
    color: #000;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 133.333% */
  }
  & ${WrapperItemAutoComplete}:focus {
    display: block;
  }
  &
    ${WrapperItemAutoComplete}
    *:focus:focus-within:active:target:hover:focus-visible {
    display: block;
  }
  &:focus-within,
  & *:focus:focus-within:active:target:hover:focus-visible,
  & > * > * > * use:hover use:active use:focus {
    & > div.autoComplete {
      display: block;
      opacity: 1;
      visibility: visible;
      &** > :focus-within {
        opacity: 1;
        transition: opacity 1ms;
        visibility: visible;
        display: block;
      }
    }
  }
`;
