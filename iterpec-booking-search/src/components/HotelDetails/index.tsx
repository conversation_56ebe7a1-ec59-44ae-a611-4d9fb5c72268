import {
  Fragment,
  useEffect,
  useRef,
  useState,
  useCallback,
  useMemo,
} from "react";
import * as Styles from "./styles";
import {
  setHeader,
  getSearch,
  setHotel,
  getHotel,
  Hooks,
  Utils,
  //@ts-ignore
} from "@iterpec/booking-utility";

import {
  Accordion,
  Button,
  Card,
  Chips,
  Divider,
  Drawer,
  IconButton,
  Skeleton,
} from "@iterpecdev/design-sistem";
import moment from "moment";
import CardRoom from "../CardRoom/expedia";
import CardReview from "../CardReview";
import Carousel from "../Carrousel";
import Footer from "../Footer";
import { useNavigate, useSearchParams } from "react-router-dom";
import Stars from "../Stars";
import RoomDetails from "../RoomDetails";
import Api from "../../services";
import { Available, Room, IStaticData } from "../../services/types";
import ImageNotFoud from "../ImageNotFound";
import { ScrollFixedTab } from "../ScrollFixedTab";
import { useTranslation } from "react-i18next";
import Icon from "@iterpecdev/icons-system";
import { Property, Static } from "../../services/types/getStaticData";
interface ParamTypes {
  token: string;
  email: string;
}

const HotelDetails = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { t } = useTranslation();
  const [hotelRates, setHotelRates] = useState<Available>(null);
  const [staticData, setStaticData] = useState<Property>(null);
  const [selectedRoom, setSelectedRoom] = useState<Room>(null);
  const [openDescription, setOpenDescription] = useState(false);
  const [openAmenities, setOpenAmenities] = useState(false);
  const [open, setOpen] = useState(false);
  const isMobile = Hooks.useMobileDetect().isMobile();
  const { cachedImage } = Utils;
  const [activeTab, setActiveTab] = useState<number | null>(null);

  // Refs estáticos
  const refHotelDetails = useRef<HTMLDivElement>(null);
  const refCardRoom = useRef<HTMLDivElement>(null);
  const refAmenities = useRef<HTMLDivElement>(null);
  const refUserReviews = useRef<HTMLDivElement>(null);
  const refLocation = useRef<HTMLDivElement>(null);
  const refPolicies1 = useRef<HTMLDivElement>(null);
  const refPolicies2 = useRef<HTMLDivElement>(null);
  const refPolicies3 = useRef<HTMLDivElement>(null);
  const refReview = useRef<HTMLDivElement>(null);

  // refs array fixo
  const refs = useMemo(
    () => [
      refHotelDetails,
      refCardRoom,
      refAmenities,
      refUserReviews,
      refLocation,
      refPolicies1,
      refPolicies2,
      refPolicies3,
      refReview,
    ],
    []
  );

  // Memoize search/hotelData para evitar recomputação
  const search = useMemo(() => getSearch(), []);
  const hotelData = useMemo(() => getHotel(), []);

  // Memoize rooms para evitar sort/map desnecessário
  const sortedRooms = useMemo(() => {
    const rooms = staticData?.rooms?.length
      ? staticData.rooms
      : hotelRates?.rooms ?? [];
    return rooms?.slice().sort((roomA, roomB) => {
      // Disponibilidade: quartos com rates primeiro
      const ratesA = hotelRates?.rooms?.find(
        (rateRoom) => rateRoom.roomId === roomA.id
      )?.rates;
      const ratesB = hotelRates?.rooms?.find(
        (rateRoom) => rateRoom.roomId === roomB.id
      )?.rates;

      const hasRatesA = ratesA && ratesA.length > 0;
      const hasRatesB = ratesB && ratesB.length > 0;

      if (hasRatesA && !hasRatesB) return -1;
      if (!hasRatesA && hasRatesB) return 1;

      // Ambos têm rates ou ambos não têm rates, ordenar por preço se possível
      if (hasRatesA && hasRatesB) {
        // Ordena pelo menor preço total (rate?.totalPriceInclusive?.value)
        const priceA = Math.min(
          ...ratesA.map((r) => r?.totalPriceInclusive?.value ?? Infinity)
        );
        const priceB = Math.min(
          ...ratesB.map((r) => r?.totalPriceInclusive?.value ?? Infinity)
        );
        return priceA - priceB;
      }

      // Se nenhum tem rates, mantém a ordem original
      return 0;
    });
  }, [staticData?.rooms, hotelRates?.rooms]);

  // Memoize amenities
  const amenitiesList = useMemo(
    () =>
      !openAmenities
        ? staticData?.amenities?.slice(0, 6)
        : staticData?.amenities,
    [openAmenities, staticData?.amenities]
  );

  // Memoize guestRatings
  const guestRatings = useMemo(
    () => staticData?.ratings?.guest ?? null,
    [staticData?.ratings?.guest]
  );

  // Memoize location description
  const locationDescription = useMemo(
    () => staticData?.descriptions?.location ?? null,
    [staticData?.descriptions?.location]
  );

  // Memoize images
  const images = useMemo(
    () =>
      staticData?.images.length &&
      staticData?.images
        ?.filter((image) => image?.links?.["1000px"] ?? image?.links?.["350px"])
        ?.sort((a, b) => (a.heroImage ? -1 : 1))
        ?.map((img) => ({
          ...img,
          links: {
            ...img.links,
            "1000px": cachedImage(
              img.links["1000px"]?.href ?? img.links["350px"]?.href
            ), // ✅ Sem risco de re-render desnecessário
          },
        })),
    [staticData?.images]
  );

  // Memoize transformToList
  const transformToList = useCallback(
    (htmlString) => {
      const sections: string[] = htmlString.match(/<p>(.*?)<\/p>/g);
      const distanceList: string[] = [];
      const airportList: string[] = [];
      if (sections) {
        sections.forEach((section, index) => {
          const content = section.replace(/<\/?p>/g, "").split(/<br\s*\/?>/);
          content.forEach((item, indexContent) => {
            if (index === 1 && indexContent === 0) return;
            const trimmedItem = item.trim();
            if (trimmedItem) {
              if (index === 0) distanceList.push(trimmedItem);
              else if (index === 1) airportList.push(trimmedItem);
            }
          });
        });
      }
      return (
        <>
          {distanceList?.length > 0 && (
            <>
              <h2>{t("hotelDetails.nearby")}</h2>
              <Divider />
              <ul>
                {distanceList.map((item, index) => (
                  <li key={`distance-${index}`}>
                    <Icon name="MapMarker" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </>
          )}
          {airportList?.length > 0 && (
            <>
              <h2>{t("hotelDetails.airports")}</h2>
              <Divider />
              <ul>
                {airportList.map((item, index) => (
                  <li key={`airport-${index}`}>
                    <Icon name="Airplane" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </>
          )}
        </>
      );
    },
    [t]
  );

  // Funções de toggle
  const handleDescription = useCallback(
    () => setOpenDescription((v) => !v),
    []
  );
  const handleAmenities = useCallback(() => setOpenAmenities((v) => !v), []);
  const handleDrawer = useCallback(() => setOpen((v) => !v), []);

  // Scroll/tab handler
  const handleTab = useCallback(
    (id: number) => {
      if (id != null && refs[id]?.current) {
        const offset = 200; // Pixels extras
        const scrollPosition = refs[id].current.offsetTop - offset;
        window.scrollTo({ top: scrollPosition, behavior: "smooth" });
        setActiveTab(id);
      }
    },
    [refs]
  );

  // Room handler
  const handleRoom = useCallback(
    (room) => {
      handleDrawer();
      setSelectedRoom(room);
      //@ts-ignore
      setHotel({
        ...hotelData,
        ...staticData,
        room,
      });
    },
    [handleDrawer, hotelData, staticData]
  );

  // Memoize useFloat
  const useFloat = useCallback((value: number) => value.toFixed(1), []);

  // Header
  const handleHeader = useCallback(() => {
    setHeader({
      $showSearch: isMobile,
      $showMenu: isMobile,
      $showFallback: isMobile,
      $show: isMobile,
      $fallback: () => navigate("/search/"),
    });
  }, [navigate, isMobile]);

  // Data fetchers
  const getHotelData = useCallback(async () => {
    const api = new Api();
    try {
      const { data } = await api.getStaticData(searchParams.get("propertyId"));
      setStaticData(data.property);
    } catch (error) {
      console.log(error);
    }
  }, [searchParams]);

  const getHotelRates = useCallback(async () => {
    const api = new Api();
    const { searchId } = search;
    try {
      const { data } = await api.adicionalRates({
        searchId,
        supplierId: searchParams.get("supplierId"),
      });
      setHotelRates(data.availables[0]);
    } catch (error) {
      console.log(error);
    }
  }, [hotelData, search, searchParams]);

  // Effects
  useEffect(() => {
    handleHeader();
    getHotelRates();
    getHotelData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    handleHeader();
  }, [isMobile]);

  useEffect(() => {
    const onLangChange = () => getHotelData();
    window.addEventListener("languageChange", onLangChange);
    return () => window.removeEventListener("languageChange", onLangChange);
  }, [getHotelData]);

  useEffect(() => {
    // Garante que a página role para o topo ao carregar
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  // Container
  const Container = useCallback(
    ({ children }) =>
      isMobile ? (
        <>{children}</>
      ) : (
        <Styles.WrapperContainer>{children}</Styles.WrapperContainer>
      ),
    [isMobile]
  );

  // Função utilitária para separar blocos de descrição multilíngue e remover todo HTML
  const parseDescriptionBlocks = (
    html: string
  ): Record<"location" | "rooms" | "amenities" | "food", string> => {
    if (!html)
      return {} as Record<"location" | "rooms" | "amenities" | "food", string>;
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = html;
    const blocks: Record<"location" | "rooms" | "amenities" | "food", string> =
      {} as any;
    const titleMap: Record<string, keyof typeof blocks> = {
      // Português
      "Localização do estabelecimento": "location",
      Quartos: "rooms",
      Comodidades: "amenities",
      Alimentação: "food",
      // Inglês
      "Property Location": "location",
      Rooms: "rooms",
      Amenities: "amenities",
      Dining: "food",
      // Espanhol
      "Ubicación del establecimiento": "location",
      Habitaciones: "rooms",
      Alimentación: "food",
    };
    Array.from(tempDiv.querySelectorAll("p")).forEach((p) => {
      const bold = p.querySelector("b");
      if (bold) {
        const title = bold.textContent?.trim() || "";
        const key = titleMap[title];
        if (key) {
          bold.remove();
          // Remove todo HTML do conteúdo do bloco
          const text = p.textContent?.trim() || "";
          blocks[key] = text;
        }
      }
    });
    return blocks;
  };

  // Memo para extrair blocos de descrição multilíngue
  const cangoorooDescriptions = useMemo(() => {
    if (!staticData?.descriptions?.general) {
      return { location: "", rooms: "", amenities: "", food: "" };
    }
    return parseDescriptionBlocks(staticData.descriptions.general);
  }, [staticData?.descriptions?.general]);

  // Render
  return (
    <Container>
      <Styles.WrapperHotelDetails ref={refHotelDetails}>
        {images?.length ? (
          <Carousel>
            {images.map((image, index) => (
              <img
                src={image?.links?.["1000px"]}
                key={index}
                loading="lazy"
                alt={image.caption}
              />
            ))}
          </Carousel>
        ) : (
          <Carousel>
            <ImageNotFoud />
            <ImageNotFoud />
          </Carousel>
        )}

        <div className="WrapperResume">
          <h1>{staticData?.name ?? <Skeleton />} </h1>
          <p>
            {staticData?.address && staticData
              ? [
                  staticData.address.lineOne,
                  staticData?.address?.city,
                  staticData?.address?.postalCode,
                  staticData?.address?.countryCode,
                ]
                  .filter(Boolean)
                  .join(", ") + "."
              : null}
            {!staticData?.address && !staticData && <Skeleton />}
          </p>
          <div className="WrapperRate">
            {staticData?.ratings?.property?.rating ||
            staticData?.category?.value ? (
              <Stars
                stars={Number(
                  staticData.ratings?.property.rating ??
                    staticData?.category?.value
                )}
              />
            ) : (
              !staticData?.address && !staticData && <Skeleton />
            )}
            {guestRatings?.overall ? (
              <>
                <Chips onClick={() => handleTab(3)}>
                  {Number(guestRatings?.overall) * 2}
                </Chips>
                <div className="WrapperLink">
                  <Button onClick={() => handleTab(3)} variant="link">
                    {t("hotelDetails.seeMore")}
                  </Button>
                </div>
              </>
            ) : (
              !staticData?.name && !staticData && <Skeleton />
            )}
          </div>
          <div className="WrapperDescription">
            {!!(
              staticData?.descriptions.rooms ??
              staticData?.descriptions?.general
            ) ? (
              <>
                <p>
                  {openDescription || !isMobile
                    ? staticData?.descriptions.rooms ??
                      cangoorooDescriptions.rooms
                    : (
                        staticData?.descriptions.rooms ??
                        cangoorooDescriptions.rooms
                      )?.substring(0, 150) + "..."}
                </p>

                {isMobile ? (
                  <Button variant="link" onClick={handleDescription}>
                    {!openDescription
                      ? t("hotelDetails.seeMore")
                      : t("hotelDetails.seeLess")}
                  </Button>
                ) : null}
              </>
            ) : (
              <>
                {!staticData?.name && !staticData && <Skeleton />}
                {!staticData?.name && !staticData && <Skeleton />}
              </>
            )}
          </div>
        </div>
        <ScrollFixedTab handleTab={handleTab} />
        <Styles.WrapperCardRoom ref={refCardRoom}>
          {sortedRooms?.length
            ? sortedRooms.map((room) => {
                const rates =
                  hotelRates?.rooms?.find((rateRoom) => {
                    return rateRoom.roomId === room.id;
                  })?.rates ?? [];
                return (
                  <Fragment key={room.idExpedia}>
                    <CardRoom
                      {...room}
                      onClick={() => handleRoom({ ...room, rates })}
                      rates={rates}
                    />
                  </Fragment>
                );
              })
            : null}
        </Styles.WrapperCardRoom>
        <Styles.WrapperAmenities ref={refAmenities}>
          <Card>
            <h2>{t("hotelDetails.hotelServices")}</h2>
            <p>
              {staticData?.descriptions?.amenities ??
                cangoorooDescriptions?.amenities}
            </p>
            <div className="WrapperAmenities">
              <div className="WrapperRow">
                <div className="WrapperCategory amenities">
                  {!openAmenities && isMobile
                    ? staticData?.amenities
                        ?.slice(0, 6)
                        ?.map((amenitie) => <p>{amenitie.name}</p>)
                    : staticData?.amenities?.map((amenitie) => (
                        <p>{amenitie.name}</p>
                      ))}
                  {isMobile && (
                    <Button variant="link" onClick={handleAmenities}>
                      {!openAmenities
                        ? t("hotelDetails.seeMore")
                        : t("hotelDetails.seeLess")}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </Styles.WrapperAmenities>
        {guestRatings ? (
          <Styles.WrapperAmenities ref={refUserReviews}>
            <Card className="flex">
              <h2>{t("hotelDetails.userReviews")}</h2>
              <div className="WrapperReview WrapperAmenities">
                <div className="WrapperRow">
                  <div className="WrapperCategory">
                    <ul>
                      {guestRatings.overall && (
                        <li>
                          {t("hotelDetails.general")}
                          <b>
                            <Stars stars={guestRatings.overall} />
                            {useFloat(Number(guestRatings.overall))}
                          </b>
                        </li>
                      )}
                      {guestRatings.cleanliness && (
                        <li>
                          {t("hotelDetails.cleanliness")}
                          <b>
                            <Stars stars={guestRatings.cleanliness} />
                            {useFloat(Number(guestRatings.cleanliness))}
                          </b>
                        </li>
                      )}
                      {guestRatings.service && (
                        <li>
                          {t("hotelDetails.service")}
                          <b>
                            <Stars stars={guestRatings.service} />
                            {useFloat(Number(guestRatings.service))}
                          </b>
                        </li>
                      )}
                      {guestRatings.comfort && (
                        <li>
                          {t("hotelDetails.comfort")}
                          <b>
                            <Stars stars={guestRatings.comfort} />
                            {useFloat(Number(guestRatings.comfort))}
                          </b>
                        </li>
                      )}
                      {guestRatings.condition && (
                        <li>
                          {t("hotelDetails.condition")}
                          <b>
                            <Stars stars={guestRatings.condition} />
                            {useFloat(Number(guestRatings.condition))}
                          </b>
                        </li>
                      )}
                      {guestRatings.neighborhood && (
                        <li>
                          {t("hotelDetails.neighborhood")}
                          <b>
                            <Stars stars={guestRatings.neighborhood} />
                            {useFloat(Number(guestRatings.neighborhood))}
                          </b>
                        </li>
                      )}
                      {guestRatings.amenities && (
                        <li>
                          {t("hotelDetails.amenities")}
                          <b>
                            <Stars stars={guestRatings.amenities} />
                            {useFloat(Number(guestRatings.amenities))}
                          </b>
                        </li>
                      )}
                    </ul>
                  </div>
                </div>
              </div>
              <Styles.WrapperReview ref={refReview}>
                <CardReview
                  idHotel={staticData?.propertyIdTBI ?? searchParams.get("propertyId")}
                  hotelName={staticData?.name ?? ""}
                />
              </Styles.WrapperReview>
            </Card>
          </Styles.WrapperAmenities>
        ) : null}
        {staticData?.location?.coordinates ? (
          <Styles.WrapperLocation ref={refLocation}>
            <Card>
              <h2>{t("hotelDetails.location")}</h2>
              <p>{!isMobile ? locationDescription : null}</p>
              {staticData?.location ? (
                <img
                  loading="lazy"
                  src={cachedImage(
                    `https://maps.googleapis.com/maps/api/staticmap?&center=${staticData?.location?.coordinates.latitude},${staticData?.location?.coordinates.longitude}&zoom=17%20&size=400x400&key=AIzaSyAl_CSAhGr1fWqHer6_1T9XaOrFHOAg4dU`
                  )}
                />
              ) : null}
              {staticData?.descriptions?.attractions ? (
                <Styles.WrapperLocationDescripition>
                  <p>{isMobile ? locationDescription : null}</p>
                  {transformToList(staticData?.descriptions?.attractions)}
                </Styles.WrapperLocationDescripition>
              ) : null}
            </Card>
          </Styles.WrapperLocation>
        ) : null}

        <Styles.WrapperPolicies ref={refPolicies1}>
          {staticData?.policies?.knowBeforeYouGo ? (
            <Accordion
              title={t("hotelDetails.hotelPolicies")}
              defaultOpen={true}
            >
              <article
                dangerouslySetInnerHTML={{
                  __html: staticData?.policies?.knowBeforeYouGo,
                }}
              />
            </Accordion>
          ) : null}
        </Styles.WrapperPolicies>
        <Styles.WrapperPolicies ref={refPolicies2}>
          {staticData?.checkin?.instructions ? (
            <Accordion
              title={t("hotelDetails.checkinCheckout")}
              defaultOpen={true}
            >
              <>
                <article
                  dangerouslySetInnerHTML={{
                    __html: staticData?.checkin.instructions,
                  }}
                />
                <article></article>
              </>
            </Accordion>
          ) : null}
        </Styles.WrapperPolicies>
        <Styles.WrapperPolicies ref={refPolicies3}>
          {staticData?.fees?.optional ? (
            <Accordion
              title={t("hotelDetails.optionalFees")}
              defaultOpen={true}
            >
              <article
                dangerouslySetInnerHTML={{
                  __html: staticData?.fees?.optional,
                }}
              />
            </Accordion>
          ) : null}
        </Styles.WrapperPolicies>
        <Drawer
          position={isMobile ? "bottom" : "right"}
          open={open}
          onDismiss={handleDrawer}
          title={selectedRoom?.name ?? ""}
        >
          {selectedRoom ? (
            <RoomDetails
              onDismiss={handleDrawer}
              //@ts-ignore
              selectedRoom={selectedRoom}
            />
          ) : null}
        </Drawer>
      </Styles.WrapperHotelDetails>
      <Footer />
    </Container>
  );
};
export default HotelDetails;
