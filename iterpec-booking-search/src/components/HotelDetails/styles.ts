import styled from "styled-components";

export const WrapperContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #f1f1f1;
  & > div {
    max-width: 1200px;
    width: 100%;
    padding-bottom: 2rem;
  }
`;

export const WrapperButton = styled.div`
  position: fixed;
  right: 10px;
  bottom: 20px;
  & > button:active {
    background-color: rgb(0, 0, 0);
  }
`;
export const WrapperLocationDescripition = styled.div`
  font-family: Inter;
  margin: 15px 0;
  ${(props) =>
    props.theme.breakpoints.desktop(`
      width: 65%;

    `)}
  > hr {
    margin-bottom: 1rem;
  }
  & > p {
    color: rgb(135, 135, 135);
    font-family: Inter;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 120%;
    margin: 15px 0px;
    flex: 0 0 auto;
  }
  > h2 {
    margin-top: 1rem;
    color: rgb(35, 35, 35);
    font-family: Inter;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
  }
  & > ul {
    list-style: none;
    ${(props) =>
      props.theme.breakpoints.desktop(`
        column-count: 2;
        column-gap: 24px;
        padding: 0;
        margin: 0;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        flex: 1 1 0;
        align-self: stretch;
      `)}

    > li {
      break-inside: avoid;
      display: flex;
      align-items: center;
      gap: 5px;
      margin: 10px 0;
      & > svg {
        fill: #878787;
        width: 25px;
        ${(props) =>
          props.theme.breakpoints.desktop(`
          width: 20px;
        `)}
      }
      & > span {
        max-width: 90%;
        word-wrap: break-word;
        color: #878787;
        font-size: 15px;
        ${(props) =>
          props.theme.breakpoints.desktop(`
            font-size: 12px;
          `)}
        line-height: 110%;
        font-style: normal;
        font-weight: 400;
      }
    }
  }
`;
export const WrapperHotelDetails = styled.div`
  * {
    font-family: Inter;
  }
  ${(props) =>
    props.theme.breakpoints.mobile(`
    background: rgba(135, 135, 135, 0.2);
  `)}
  ${(props) =>
    props.theme.breakpoints.desktop(`
    
    background: #f1f1f1;
  `)}

  img {
    height: 223px;
    ${(props) =>
      props.theme.breakpoints.desktop(`
        height: 440px;
      `)}
    width: 100%;
    object-fit: cover;
  }

  & > div.WrapperResume {
    background-color: #fff;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    gap: 6px;
    padding-left: 17px;
    padding-right: 17px;
    padding-top: 16px;
    & > h1 {
      color: #000;
      font-family: Inter;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 120%;
    }
    > p {
      color: #878787;
      font-size: 15px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      text-justify: inter-word;
    }
    & > div.WrapperDescription {
      > p {
        color: #878787;
        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        text-justify: inter-word;
      }
      & > button {
        padding: 0;
        height: min-content;
      }
    }
    & > div.WrapperRate {
      display: flex;
      gap: 10px;
      & > div.WrapperLink {
        > button {
          height: min-content;
          padding: 0;
        }
      }
      & > button {
        height: min-content;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0;
        min-width: 42px;
        border-radius: 8px;
        border: 1px solid #a4ce4a;
        background: #a4ce4a;
        color: #fff;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
      }
      & > div.WrapperStar {
        display: flex;
        & > svg {
          width: 24px;
          height: auto;
        }
      }
    }
  }
`;

export const WrapperCardRoom = styled.div`
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  ${(props) =>
    props.theme.breakpoints.mobile(`
    background: rgba(135, 135, 135, 0.2);
  `)}
  ${(props) =>
    props.theme.breakpoints.desktop(`
    
    background: #f1f1f1;
  `)}

  padding: 13px 0;
  gap: 20px;
  ${(props) =>
    props.theme.breakpoints.desktop(`
      background-color: rgb(241, 241, 241);
      flex-direction: row;
      flex-wrap: wrap;
      align-items: stretch;
      justify-content: flex-start;
      & > div {
        width: 380px;
        align-self: stretch;
        display: flex;
        flex-direction: column;

        /* Se houver mais uma div interna, garanta que ela também estique */
        & > div {
         height: 100%;
        }
      }
      & img{
        height: 190px;
      }
    `)}
`;

export const WrapperAmenities = styled.div`
  width: 100%;
  background-color: #fff;
  margin: 13px 0;

  & > div {
    width: 100%;
    padding-top: 20px;
    ${(props) =>
      props.theme.breakpoints.desktop(`
            &.flex {
              display: flex;
              gap: 20px;
              flex-wrap: wrap;
              align-items: flex-start;
              justify-content: space-around;
              & > div {
                margin: 0;
                & > div {
                  max-height: 400px;
                }
              }
            }
      `)}

    & > h2 {
      width: 100%;
      color: #232323;
      font-family: Inter;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px; /* 133.333% */
    }
    & p {
      color: #878787;
      font-family: Inter;
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      line-height: 120%;
      margin: 15px 0;
    }

    & > div.WrapperAmenities {
      width: 100%;
      padding: 0px 0px 20px 0px;
      & > div.WrapperRow {
        & > div.WrapperCategory {
          & > button {
            margin-top: 15px;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          ${(props) =>
            props.theme.breakpoints.desktop(`
             &.amenities {
                display: list-item;
                list-style-type: none;
                column-count: 2;
                & > p {
                  line-height: 220%;
                  margin: 0;
                  }
              }
              

            `)}
          & > h3 {
            width: 100%;
            color: #232323;
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: 40px;
          }
          & > ul {
            list-style-type: none;
            margin-top: 15px;
            & > li {
              display: flex;
              justify-content: space-between;
              ${(props) =>
                props.theme.breakpoints.desktop(`
                  width: 100%;
                  max-width: 400px;
                `)}
              & > b {
                display: flex;
                justify-content: center;
                align-items: center;
                > div {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  > svg {
                    width: 20px;
                  }
                }
              }
              & > div > svg {
                width: 30px;
              }
              color: #a5a5a5;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 24px; /* 171.429% */
            }
          }
        }
      }
    }

    & > div.WrapperReview {
      ${(props) =>
        props.theme.breakpoints.desktop(`
          max-width: 40%;
      `)}
    }
  }
`;

export const WrapperReview = styled.div`
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  ${(props) =>
    props.theme.breakpoints.desktop(`
      max-width: 50%;
      max-height: 450px;
      overflow-x: auto;
      `)}
  & > div {
    width: 100%;
    background-color: #fff;
  }
`;
export const WrapperPolicies = styled.div`
  background-color: #fff;
  margin-top: 20px;
  width: 100%;
  ul {
    list-style-position: inside;
  }
`;
export const WrapperLocation = styled.div`
  background-color: #fff;
  margin-top: 20px;
  width: 100%;

  & > div {
    width: 100%;
    padding: 20px 20px;
    ${(props) =>
      props.theme.breakpoints.desktop(`
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
        `)}
    > img {
      margin-top: 20px;
      width: 100%;
      height: auto;

      ${(props) =>
        props.theme.breakpoints.desktop(`
          width: 350px;
          height: 287px;
          object-fit: cover;
        `)}
    }
    & > p {
      color: rgb(135, 135, 135);
      font-family: Inter;
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      line-height: 120%;
    }
    & > h2 {
      color: #232323;
      font-family: Inter;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px; /* 133.333% */
    }
    & > ul {
      margin-top: 1rem;
      list-style-position: inside;
      & > li {
        color: #a5a5a5;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 200%; /* 171.429% */
      }
    }
  }
`;
