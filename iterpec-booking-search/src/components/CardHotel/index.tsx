import React, { useCallback, memo, useState, useMemo } from "react";
import { <PERSON><PERSON>, Card, Chips, Divider } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import { useNavigate } from "react-router-dom";
import { Available, IStaticData } from "../../services/types";
import Stars from "../Stars";
import {
  Hooks,
  getHotel,
  setHotel as setHotelData,
  getRegion,
  Utils,
  //@ts-ignore
} from "@iterpec/booking-utility";
import moment from "moment";
import ImageNotFoud from "../ImageNotFound";
import Carousel from "../Carrousel";
import SkeletonCardHotel from "./skeleton";
import { useTranslation } from "react-i18next";
import { AmenitiesIconNoRepeat } from "../AmenitiesIcon/noRepeat";
import { Rate } from "../../services/types/getAvailability";

type Photo = {
  urlSize1000px: string;
  description: string;
};

interface PhotosProps {
  photos: Photo[];
  cachedImage: (imageUrl?: string) => string;
}

const Photos: React.FC<PhotosProps> = memo(({ photos, cachedImage }) => {
  const [brokenImages, setBrokenImages] = useState<string[]>([]);

  const handleError = useCallback((url: string) => {
    setBrokenImages((prev) => [...prev, url]);
  }, []);

  const validPhotos = useMemo(
    () =>
      photos?.filter((image) => !brokenImages.includes(image.urlSize1000px)),
    [photos, brokenImages]
  );

  if (!validPhotos?.length) {
    return <ImageNotFoud style={{ maxHeight: "300px", maxWidth: "389px" }} />;
  }

  return (
    <Carousel useTimer={false}>
      {validPhotos.map((image) => (
        <img
          src={cachedImage(image.urlSize1000px)}
          loading="lazy"
          alt={image.description}
          key={image.urlSize1000px}
          onError={() => handleError(image.urlSize1000px)}
        />
      ))}
    </Carousel>
  );
});

const CardHotel: React.FC<{
  data: Available;
  setStaticData?: (e: IStaticData) => void;
  orientation?: string;
}> = memo(({ data, setStaticData, orientation = "horizontal" }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const dataHotel = getHotel();
  const { useCurrency } = Hooks;
  const { cachedImage } = Utils;

  const rate = useMemo(
    () => (data?.rooms?.length ? data?.rooms[0]?.rates?.[0] : null),
    [data]
  );

  const navigateToRooms = useCallback(() => {
    navigate(
      `/search/hotel?supplierId=${data.supplierId}&propertyId=${data.propertyIdTBI}`
    );
    window.scrollTo({ top: 0, behavior: "smooth" });
    //@ts-ignore
    setHotelData({ ...dataHotel, ...data });
  }, [data, dataHotel, navigate]);

  const getAmenities = useMemo(
    () => [
      ...(data?.amenities || []),
      ...(data?.rooms?.[0]?.amenities || []),
      ...(data?.rooms?.[0]?.rates?.[0]?.amenities || []),
    ],
    [data]
  );

  if (!data.propertyIdTBI) {
    return <SkeletonCardHotel orientation={orientation} />;
  }

  return (
    <Styles.WrapperCardHotel
      key={data.propertyIdTBI}
      id={data.propertyIdTBI}
      $orientation={orientation}
    >
      {orientation === "vertical" ? (
        <Card className="card">
          <h3>{data.propertyName}</h3>
          <div className="wrapperTypeStars">
            <div className="wrapperStars">
              {data?.stars ? <Stars stars={data.stars} /> : null}
            </div>

            <Chips active variant="tag">
              {data?.propertyType}
            </Chips>
          </div>
          <Photos photos={data.photos} cachedImage={cachedImage} />
          <div className="wrapperScoreAmenities">
            {data?.guestRatings?.overall && (
              <Chips active variant="tag" color="success">
                {data.guestRatings.overall * 2}
              </Chips>
            )}
            <AmenitiesIconNoRepeat amenities={getAmenities} />
          </div>
          <CardHotelContent
            data={data}
            rate={rate}
            useCurrency={useCurrency}
            t={t}
          />
          {data.available && (
            <div className="wrapperButtons">
              <Button onClick={navigateToRooms}>
                {t("cardhotel.viewRooms")}
              </Button>
            </div>
          )}
        </Card>
      ) : (
        <Card className="card">
          <Photos photos={data.photos} cachedImage={cachedImage} />
          <Styles.WrapperContent>
            <h3>{data.propertyName}</h3>
            <div className="wrapperTypeStars">
              <div className="wrapperStars">
                <Stars stars={data.stars} />
              </div>
              {data.propertyType && (
                <Chips active variant="tag">
                  {data.propertyType}
                </Chips>
              )}
            </div>
            <Divider />
            <div className="wrapperScoreAmenities">
              {data?.guestRatings?.overall && (
                <Chips active variant="tag" color="success">
                  {data.guestRatings.overall * 2}
                </Chips>
              )}
              <AmenitiesIconNoRepeat amenities={getAmenities} />
            </div>
            <CardHotelContent
              data={data}
              rate={rate}
              useCurrency={useCurrency}
              t={t}
            />
            {data.available && (
              <div className="wrapperButtons">
                <Button onClick={navigateToRooms}>
                  {t("cardhotel.viewRooms")}
                </Button>
              </div>
            )}
          </Styles.WrapperContent>
        </Card>
      )}
    </Styles.WrapperCardHotel>
  );
});

// Componente extraído para o conteúdo do CardHotel
const CardHotelContent: React.FC<{
  data: Available;
  rate: Rate;
  useCurrency: (value: number) => string;
  t: any;
}> = ({ data, rate, useCurrency, t }) => {
  if (data.available) {
    return (
      <div className="wrapperContent">
        <div className="roomName">
          <h3>{data?.rooms?.[0]?.roomName || null}</h3>
        </div>
        <div className="roomPrice">
          {!!rate?.averagePerNight?.value && (
            <p>
              {useCurrency(Number(rate?.averagePerNight?.value))}{" "}
              {t("cardhotel.perNight")}
            </p>
          )}
          <p>
            {!!rate?.totalPriceInclusiveStrikethrough?.value && (
              <h4 className="oldPrice">
                {useCurrency(
                  Number(rate.totalPriceInclusiveStrikethrough.value)
                )}
              </h4>
            )}

            <h4>
              {useCurrency(
                Number(
                  rate?.totalPriceInclusive?.value ??
                    rate?.totalPriceExclusive?.value
                )
              )}
            </h4>
            {t("cardhotel.total")}
          </p>
          <span>{t("cardhotel.totalIncluded")}</span>
        </div>
        {rate?.cancelPenalties?.map((penalty, index) =>
          penalty &&
          rate.refundable &&
          moment(penalty.startDate).isAfter(new Date()) &&
          moment(penalty?.endDate).isAfter(new Date()) ? (
            <p className={"refundable"} key={index}>
              {t("selectPrice.freeCancellationUntil")}{" "}
              <b>
                {moment(moment(penalty?.startDate).subtract(1, "days")).format(
                  "DD/MM/YYYY"
                )}
              </b>
            </p>
          ) : (
            <p className={"noRefundable"} key={index}>
              <span>{t("selectPrice.nonRefundable")}</span>
            </p>
          )
        )}
      </div>
    );
  }
  return (
    <div className="wrapperContent">
      <div className="roomPrice">
        <h4 className="notAvailable">
          {t("cardhotel.notAvailableForSelectedDates")}
        </h4>
      </div>
    </div>
  );
};

export default CardHotel;
