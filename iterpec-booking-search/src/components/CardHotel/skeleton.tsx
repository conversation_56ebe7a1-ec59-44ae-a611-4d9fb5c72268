import { Card, Skeleton } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import React from "react";

const SkeletonCardHotel: React.FC<{
  id?: string;
  orientation?: "horizontal" | "vertical" | string;
}> = ({ id = undefined, orientation = "horizontal" }) => {
  return (
    <Styles.SkeletonWrapperCardHotel id={id} $orientation={orientation}>
      {orientation === "vertical" ? (
        <Card className="card">
          <h3>
            <Skeleton />
          </h3>
          <div className="wrapperTypeStars">
            <div className="wrapperStars">
              <Skeleton />
            </div>
          </div>
          <Skeleton />
          <div className="wrapperScoreAmenities">
            <Skeleton />
          </div>
          <div className="wrapperContent">
            <div className="roomName">
              <h3>
                <Skeleton />
              </h3>
              <span>
                <Skeleton />
              </span>
            </div>
            <div className="roomPrice">
              <Skeleton />
            </div>
          </div>
          <div className="wrapperButtons">
            <Skeleton />
          </div>
        </Card>
      ) : (
        <Card className="card">
          <Skeleton />
          <Styles.WrapperContent>
            <h3>
              <Skeleton />
            </h3>
            <div className="wrapperScoreAmenities">
              <Skeleton />
            </div>
            <div className="wrapperContent">
              <div className="roomName">
                <h3>
                  <Skeleton />
                </h3>
              </div>
              <div className="roomPrice">
                <Skeleton />
              </div>
            </div>
            <div className="wrapperButtons">
              <Skeleton />
            </div>
          </Styles.WrapperContent>
        </Card>
      )}
    </Styles.SkeletonWrapperCardHotel>
  );
};

export default SkeletonCardHotel;
