import styled, { css } from "styled-components";

export const WrapperContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 7px;
  padding: 16px 20px;
  width: 100%;
`;
export const WrapperCardHotel = styled.div<{ $orientation: string }>`
  ${({ theme, $orientation }) => css`
    margin-top: 10px;
    & > div.card {
      display: flex;
      flex-direction: column;
      gap: 12px;
      width: 100%;
      overflow: hidden;
      ${(props) =>
        $orientation === "horizontal"
          ? props.theme.breakpoints.desktop(`
            gap: 0;
          `)
          : null}
      & > div.carrouselWrapper,
      & > div > div.carrouselWrapper {
        display: block;
        margin-left: -16px;
        width: 109.2%;
      }
      & > img {
        margin-left: -16px;
        width: 108%;
      }
      & div {
        & > img {
          margin-left: -16px;
          width: 104%;
          object-fit: cover;
          max-height: 35vh;
          object-fit: cover;
          ${(props) =>
            $orientation === "horizontal"
              ? props.theme.breakpoints.desktop(`
            height: 100%;
            max-height: 100%;
            margin-bottom:  -16px;
          `)
              : null}
        }
      }
      & > div > div.wrapperScoreAmenities,
      & > div.wrapperScoreAmenities {
        display: flex;
        gap: 8px;
        & > button {
          border-radius: 8px;
          border: 1px solid #a4ce4a;
          background: #a4ce4a;
        }
        & > svg {
          width: 20px;
          height: auto;
        }
      }
      & > div > div.wrapperTypeStars,
      & > div.wrapperTypeStars {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        & > button {
          font-size: 90%;
          border-radius: 8px;
          border: 1px solid #212843;
          background: #212843;
        }
        & > div > div.wrapperStars,
        & > div.wrapperStars {
          & > div > svg {
            width: 24px;
          }
        }
      }
      div.wrapperButtons,
      > div > div.wrapperButtons {
        display: flex;
        justify-content: flex-end;
      }
      div.wrapperContent {
        display: flex;
        flex-direction: column;
        gap: 7px;
        & > span,
        & > div > p,
        & > div > span,
        p {
          color: #878787;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          &.refundable {
            color: #a4ce4a;
          }
          &.nonRefundable {
            color: #a7183c;
          }
        }
        & > div.roomPrice {
          display: flex;
          gap: 4px;
          flex-direction: column;
          & > p {
            display: flex;
            gap: 7px;
            flex-wrap: wrap;
            align-items: end;
          }
          & > h4,
          > p > h4 {
            width: min-content;
            color: ${(props) => props.theme.palette.neutral.black};
            font-size: 18px;
            font-style: normal;
            font-weight: 500;
            line-height: 22px; /* 110% */
          }
          & > h4.oldPrice,
          > p > h4.oldPrice {
            color: rgba(255, 0, 0, 0.3);
            text-decoration: line-through;
          }
          & > h4.notAvailable,
          > span > h4.notAvailable {
            color: rgba(255, 0, 0, 0.6);
            font-size: 14px;
            text-decoration: none;
          }
        }
        & > div.roomName {
          display: flex;
          gap: 7px;
          flex-direction: column;
          & > h3 {
            font-size: 16px;
          }
          & > p {
            & > span {
              color: #878787;
              font-family: Inter;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px; /* 142.857% */
            }
          }
          > * {
            width: 100%;
          }
        }
      }
    }
  `}

  ${(props) =>
    props.$orientation === "vertical"
      ? props.theme.breakpoints.desktop(`
      & > div {
        width: 350px;
      }
    `)
      : props.theme.breakpoints.desktop(`
      & > div {
        width: 100%;
      }
      & > div.card {
        flex-direction: row;
        padding: 0;
        & > div.carrouselWrapper {
          width: 100%;
          max-width: 378px;
          margin-left: 0;
          & > div:not(.dots) {
            border-radius: 8px 0  0 8px;
            height: 100%;
          }
        }
      }
    `)}
`;

export const SkeletonWrapperCardHotel = styled.div<{ $orientation?: string }>`
  width: 100%;
  ${({ theme, $orientation }) => css`
    margin-top: 10px;
    & > div.card {
      display: flex;
      flex-direction: column;
      gap: 12px;
      width: 100%;
      * {
        border-radius: 8px;
      }
      & > span {
        margin-left: -17px;
        width: 110%;
        height: 181px;
        ${$orientation == "vertical" &&
        css`
          margin-top: -10px;
        `}
      }
      & > div.wrapperScoreAmenities {
        display: flex;
        gap: 8px;
        & > span,
        & > div {
          border-radius: 8px;
        }
      }
      & > div.wrapperTypeStars {
        display: flex;
        gap: 8px;
        & > span,
        & > div {
          border-radius: 8px;
          width: 59px;
          height: 24px;
        }
        & > div.wrapperStars {
          & > span {
            width: 120px;
            height: 24px;
          }
        }
      }
      div.wrapperButtons,
      > div > div.wrapperButtons {
        display: flex;
        justify-content: flex-end;
      }
      div.wrapperContent {
        display: flex;
        flex-direction: column;
        gap: 7px;
        & > span {
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
        }
        & > div.roomPrice {
          display: flex;
          gap: 7px;
          & > span {
            color: #000;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px; /* 110% */
          }
        }
        & > div.roomName {
          display: flex;
          gap: 7px;
          flex-direction: column;

          & > h3 {
            font-size: 16px;
          }
          > * {
            width: 100%;
          }
        }
      }
      ${(props) =>
        $orientation === "horizontal"
          ? props.theme.breakpoints.desktop(`
            gap: 0;
          `)
          : null}
      & > div.carrouselWrapper,
      & > div > div.carrouselWrapper {
        display: block;
        margin-left: -16px;
        width: 109.2%;
      }
      & > img {
        margin-left: -16px;
        width: 108%;
      }
      & div {
        & > img {
          margin-left: -16px;
          width: 104%;
          object-fit: cover;
          max-height: 35vh;
          object-fit: cover;
          ${(props) =>
            $orientation === "horizontal"
              ? props.theme.breakpoints.desktop(`
            height: 100%;
            max-height: 100%;
            margin-bottom:  -16px;
          `)
              : null}
        }
      }
    }
    ${$orientation === "vertical"
      ? theme.breakpoints.desktop(`
        & > div {
          width: 350px;
        }
      `)
      : theme.breakpoints.desktop(`
        & > div {
          width: 100%;
        }
        & > div.card {
          flex-direction: row;
          padding: 0;
          min-height: 240px;
          & > span, & > div.carrouselWrapper {
            width: 100%;
            height: auto;
            max-width: 378px;
            margin-left: 0;
          }
        }
      `)}
  `}
`;
