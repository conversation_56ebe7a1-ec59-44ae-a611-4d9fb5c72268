import { Chips } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import React from "react";
//@ts-ignore
import { Hooks } from "@iterpec/booking-utility";

const Marker: React.FC<{
  value?: number | string;
  onClick?: () => void | void;
  lat: number;
  lng: number;
  visible?: boolean;
  btnRef?: React.LegacyRef<HTMLDivElement>;
  groupValue?: number;
}> = ({ value, onClick, btnRef, groupValue, lat, lng, visible = true }) => {
  const { useCurrency } = Hooks;

  return (
    <Styles.WrapperMarker
      onClick={onClick}
      ref={btnRef}
      lat={lat}
      lng={lng}
      visible={visible}
    >
      <Chips> {value ? useCurrency(Number(value)) : groupValue}</Chips>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="15"
        height="7"
        viewBox="0 0 15 7"
        fill="none"
      >
        <path d="M6.8125 6L2 1H13L6.8125 6Z" fill="#3E6474" stroke="#3E6474" />
      </svg>
    </Styles.WrapperMarker>
  );
};
export default Marker;
