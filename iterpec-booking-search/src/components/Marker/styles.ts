import styled from "styled-components";

export const WrapperMarker = styled.div<{
  lat: number;
  lng: number;
  visible?: boolean;
}>`
  visibility: ${(props) => (props.visible ? "visible" : "hidden")};
  display: flex;
  align-items: center;
  width: max-content;
  flex-direction: column;
  justify-content: center;
  & > svg {
    margin-top: -2px;
  }
  & > button {
    background-color: rgba(62, 100, 116, 1);
    width: max-content;
    color: #fff;
    text-align: center;
    font-family: Inter;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    line-height: 15px;
  }
`;
