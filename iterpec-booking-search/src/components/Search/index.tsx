import { <PERSON><PERSON>, Drawer, Tab, TabItem } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import CardHotel from "../CardHotel";
import React, { useEffect, useState, useCallback } from "react";
import Order from "../Order";
import Maps from "../Maps";
import {
  setHeader,
  getSearch,
  setSearch as setStateSearch,
  getRegion as getRegionStore,
  Hooks,
  //@ts-ignore
} from "@iterpec/booking-utility";
import { useNavigate } from "react-router-dom";
import Filter from "../Filter";
import Api from "../../services";
import {
  IAutoComplete,
  IAvailables,
  ISearch,
  IStaticData,
  Filter as ISearchFilter,
} from "../../services/types";
import SkeletonCardHotel from "../CardHotel/skeleton";
import moment from "moment";
import NotFound from "./NotFound";
import Icon from "@iterpecdev/icons-system";
import { useTranslation } from "react-i18next";

export interface IFilters {
  filterQuery: string;
  filterValues: Array<{
    query: string;
    displayValue?: string | number;
  }>;
}
export interface IOrder {
  field: string;
  ascending: boolean;
}

const CardOrientation = {
  HORIZONTAL: "horizontal",
  VERTICAL: "vertical",
};

const Search = () => {
  const api = new Api();
  const [open, setOpen] = useState(false);
  const [end, setEnd] = useState(false);
  const [searchId, setSearchId] = useState<string>();
  const [order, setOrder] = useState<IOrder>(null);
  const [filters, setFilters] = useState<IFilters[]>([]);
  const [searchFilters, setSearchFilters] = useState<ISearchFilter[]>(null);
  const [page, setPage] = useState(0);
  const isMobile = Hooks.useMobileDetect().isMobile();
  const [cardOrientation, setCardOrientation] = useState<string>(
    isMobile ? CardOrientation.VERTICAL : CardOrientation.HORIZONTAL
  );
  const [searchCoordenate, setSearchCoordenate] = useState<{
    latitude: number;
    longitute: number;
  }>(null);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [search, setSearch] = useState({ ...getSearch() });
  const [staticData, setStaticData] = useState<IStaticData[]>([]);
  const [isFetchingOrUpdating, setIsFetchingOrUpdating] = useState(false);
  const [hotels, setHotels] = useState<IAvailables["availables"]>([]);
  const [hotel, setHotel] = useState<IAvailables["mainProperty"]>(null);

  const [drawer, setDrawer] = useState({
    title: "Orden",
    component: () => component("Orden"),
  });

  const handleDrawer = (index: number) => {
    const drawerTitles = ["Orden", "Filter", "Mapa"];
    setOpen(true);
    setDrawer({
      title: drawerTitles[index - 1],
      component: () => component(drawerTitles[index - 1], hotels),
    });
  };

  const handleOrder = async (e: IOrder) => {
    setOrder(e);
    setOpen(false);
  };
  const handleFilters = async (e: IFilters[]) => {
    setFilters(e);
    setOpen(false);
  };

  const component = (componentName: string, hotels?: any) => {
    const components = {
      Orden: <Order handleOrder={handleOrder} order={order} />,
      Mapa: (
        <Maps
          hotels={hotels?.map((hotel) => {
            const staticHotel = staticData?.find(
              (htl) => htl?.id === hotel?.id
            );
            return {
              ...staticHotel,
              ...hotel,
              location: staticHotel?.location,
            };
          })}
          searchId={searchId}
          searchCoordenate={searchCoordenate}
        />
      ),
      Filter: (
        <Filter
          searchId={searchId}
          handleFilters={handleFilters}
          filters={searchFilters}
        />
      ),
    };
    return components[componentName];
  };

  const getRegion = useCallback(async () => {
    try {
      const { data } = await api.getRegion(search?.destiny_id);
      if (data?.payload?.coordinates)
        setSearchCoordenate({
          latitude: data.payload.coordinates.latitude,
          longitute: data.payload.coordinates.longitude,
        });
    } catch (error) {
      console.log(error);
    }
  }, [search?.destiny_id]);

  const buildRequest = (searchState, page) => {
    const {
      destiny_id = null,
      hotel_id = null,
      type = null,
      ...rest
    } = { ...searchState };
    const local = getRegionStore();
    return {
      checkIn: moment(rest.checkIn).format("YYYY-MM-DD"),
      checkOut: moment(rest.checkOut).format("YYYY-MM-DD"),
      destiny: {
        id: destiny_id ?? hotel_id,
        type: type,
      },
      searchType: "HOTEL",
      rooms: searchState?.rooms?.map((room) => ({
        adults: room?.adults,
        childrenAge:
          room?.children > 0 ? room?.childrenAge?.map((c) => c.age) : [],
      })),
      currency: local.currency,
      countryCode: local.countryCode,
      page: page + 1,
      pageSize: 10,
    };
  };

  const getSearchHotel = useCallback(
    async (page: number, searchState = search) => {
      if (isFetchingOrUpdating) return;
      setIsFetchingOrUpdating(true);
      const request = buildRequest(searchState, page);
      try {
        if (!page) {
          const { data } = await api.getAvailability(request);
          setSearchId(data.searchId);
          if (
            data?.availables === undefined ||
            !data?.availables?.length ||
            data?.totalPages === page + 1
          ) {
            setEnd(true);
          }
          setHotel(data?.mainProperty);
          setSearchFilters(data?.filters);
          setHotels((hotels) => [...hotels, ...(data.availables ?? [])]);
        } else {
          const { data } = await api.getAvailabilityNextPage(
            searchId,
            String(request.page)
          );
          if (
            data.availables === undefined ||
            !data.availables.length ||
            data.totalPages === page + 1
          ) {
            setEnd(true);
          }
          setHotels((hotels) => [...hotels, ...(data.availables ?? [])]);
        }
      } catch (error) {
        console.log(error);
        setEnd(true);
      } finally {
        setIsFetchingOrUpdating(false);
        window.dispatchEvent(new CustomEvent("resetFilters")); // <-- reset filtros ao terminar busca
      }
    },
    [isFetchingOrUpdating, search, searchId]
  );

  const updateSearch = useCallback(
    async (searchState = search) => {
      if (isFetchingOrUpdating) return;
      setIsFetchingOrUpdating(true);
      if (!searchId) {
        setIsFetchingOrUpdating(false);
        window.dispatchEvent(new CustomEvent("resetFilters")); // <-- reset filtros ao terminar busca
        return;
      }
      setHotels([]);
      setPage(0);
      setEnd(false);
      const request = buildRequest(searchState, 0);
      try {
        const { data, status } = await api.setFilter({
          filtersList: filters,
          sortCriteria: order,
          ...request,
          searchId,
        });
        if (status !== 200) {
          setTimeout(() => {
            updateSearch();
          }, 1000);
          return;
        }
        setHotel(data?.mainProperty);
        setHotels(data?.availables);
        if (!data?.availables?.length && !data?.mainProperty?.propertyIdTBI) {
          setEnd(true);
        }
      } catch (error) {
        setEnd(true);
        console.log(error);
      } finally {
        setIsFetchingOrUpdating(false);
        window.dispatchEvent(new CustomEvent("resetFilters")); // <-- reset filtros ao terminar busca
      }
    },
    [filters, order, search, searchId, isFetchingOrUpdating]
  );

  const addToStaticData = (e: IStaticData) =>
    setStaticData((old) => [...old, e]);

  const clearFilters = () => {
    window.dispatchEvent(new CustomEvent("resetFilters"));
  };

  const reSearchEvent = useCallback(() => {
    setHotels([]);
    setSearch(getSearch());
    setPage(0);
    getSearchHotel(0, getSearch());
    setEnd(false);
  }, [getSearchHotel]);

  // Efeito para inicialização e busca de região
  useEffect(() => {
    if (!search.destiny_id && !search.hotel_id) {
      navigate(`/`);
      return;
    }
    getRegion();
    // eslint-disable-next-line
  }, []); // Executa apenas uma vez na montagem

  // Atualiza busca ao mudar filtros ou ordenação
  useEffect(() => {
    updateSearch();
    // eslint-disable-next-line
  }, [order, filters]); // Remove updateSearch das dependências

  // Atualiza orientação do card ao mudar mobile/desktop
  useEffect(() => {
    setCardOrientation(
      isMobile ? CardOrientation.VERTICAL : CardOrientation.HORIZONTAL
    );
  }, [isMobile]);

  // Atualiza searchId no estado global
  useEffect(() => {
    setStateSearch({
      ...search,
      searchId: searchId,
    });
  }, [searchId]);

  // Evento para resetar busca
  useEffect(() => {
    window.addEventListener("searchEvent", reSearchEvent);
    return () => window.removeEventListener("searchEvent", reSearchEvent);
  }, [reSearchEvent]);

  // IntersectionObserver para scroll infinito
  useEffect(() => {
    const sentinela = document.querySelector("#sentinela");
    if (!sentinela) return;
    const intersectionObserver = new IntersectionObserver((entries) => {
      if (entries.some((entry) => entry.isIntersecting)) {
        hotels?.length > 0 && setPage((old) => old + 1);
      }
    });
    intersectionObserver.observe(sentinela);
    return () => intersectionObserver.disconnect();
  }, [hotels?.length]);

  // Header e primeira página
  useEffect(() => {
    setHeader({
      $showMenu: true,
      $showSearch: true,
      $showFallback: true,
      $show: true,
      $fallback: () => navigate("/"),
    });
    setPage((old) => old + 1);
    // eslint-disable-next-line
  }, []);

  // Busca hotéis ao mudar página
  useEffect(() => {
    if (search.destiny_id || search.hotel_id) getSearchHotel(page);
    // eslint-disable-next-line
  }, [page]); // OK

  // Atualiza busca ao mudar destino/hotel
  useEffect(() => {
    setSearch(getSearch());
    setHotels([]);
    setPage(0);
    setEnd(false);
    // eslint-disable-next-line
  }, [search.destiny_id, search.hotel_id]); // Corrija para depender de search, não getSearch()

  return (
    <Styles.Container>
      <Styles.WrapperSearch
        $maps={drawer.title === "Mapa"}
        $isFetchingOrUpdating={isFetchingOrUpdating}
      >
        <Styles.WrapperChips $isFetchingOrUpdating={isFetchingOrUpdating}>
          {isMobile && (
            <Tab>
              <TabItem style={{ display: "none" }} />
              <TabItem
                onClick={() => handleDrawer(1)}
                disabled={isFetchingOrUpdating}
              >
                {t("search.drawerTitle.order")} <Icon name="SwapVertical" />
              </TabItem>
              <TabItem
                onClick={() => handleDrawer(2)}
                disabled={isFetchingOrUpdating}
              >
                {t("search.drawerTitle.filter")} <Icon name="FilterOutline" />
              </TabItem>
              <TabItem
                onClick={() => handleDrawer(3)}
                disabled={isFetchingOrUpdating}
              >
                {t("search.drawerTitle.map")} <Icon name="MapMarkerOutline" />
              </TabItem>
            </Tab>
          )}
        </Styles.WrapperChips>
        {isMobile && (
          <h1>
            {t("search.headings.hotelsIn")} {search?.destiny}
          </h1>
        )}

        <Styles.WrapperSearchContent $cardOrientation={cardOrientation}>
          {!isMobile && (
            <Filter
              searchId={searchId}
              handleFilters={handleFilters}
              filters={searchFilters}
            />
          )}
          <div style={{ width: "100%" }}>
            {!isMobile && (
              <h1>
                {t("search.headings.hotelsIn")} {search?.destiny}
              </h1>
            )}

            {hotels?.length ? (
              <Styles.WrapperCardHotel $cardOrientation={cardOrientation}>
                {hotel?.propertyIdTBI && (
                  <>
                    <CardHotel
                      orientation={cardOrientation}
                      data={hotel}
                      setStaticData={addToStaticData}
                    />
                    <h2>{t("search.headings.otherOptions")}</h2>
                  </>
                )}
                {hotels?.map(
                  (hotel, index) =>
                    hotel?.rooms?.length && (
                      <React.Fragment key={index}>
                        <CardHotel
                          data={hotel}
                          setStaticData={addToStaticData}
                          orientation={cardOrientation}
                        />
                      </React.Fragment>
                    )
                )}
                {!end && (
                  <SkeletonCardHotel
                    orientation={cardOrientation}
                    id="sentinela"
                  />
                )}
              </Styles.WrapperCardHotel>
            ) : null}
            {!end ? (
              !hotels?.length && (
                <SkeletonCardHotel
                  orientation={cardOrientation}
                  id="sentinela"
                />
              )
            ) : hotels?.length ? (
              <>
                <h2>{t("search.headings.endOfSearch")}</h2>
                <div id="sentinela" style={{ display: "none" }} />
              </>
            ) : (
              <>
                <div id="sentinela" style={{ display: "none" }} />
                <Styles.WrapperNotfound>
                  <NotFound style={{ maxHeight: "450px" }} />
                  <h2>{t("search.headings.noHotelsFound")}</h2>
                </Styles.WrapperNotfound>
              </>
            )}
          </div>
        </Styles.WrapperSearchContent>

        <Drawer
          position="left"
          open={open}
          title={drawer.title}
          onDismiss={() => setOpen(false)}
          {...(drawer.title === "Filter"
            ? {
                Link: () => (
                  <a onClick={clearFilters}>{t("search.clearFilters")}</a>
                ),
              }
            : {})}
        >
          <>{drawer.component()}</>
        </Drawer>
      </Styles.WrapperSearch>
    </Styles.Container>
  );
};

export default Search;
