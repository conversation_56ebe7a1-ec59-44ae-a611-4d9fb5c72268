import styled, { css } from "styled-components";
export const WrapperSearchContent = styled.div<{ $cardOrientation: string }>`
  display: flex;
  gap: 24px;
`;
export const WrapperNotfound = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  & > h2 { 
    text-align: center;
  }
`;
export const WrapperCardHotel = styled.div<{ $cardOrientation: string }>`
  ${(props) =>
    props.$cardOrientation === "vertical"
      ? props.theme.breakpoints.desktop(`
      display: flex;
      flex-wrap: wrap;
      gap: 24px;
      & > div {
        width: 350px;
      }
        & > h2{
          width: 100%;
          text-align: center;

        }
    `)
      : props.theme.breakpoints.desktop(`
      display: flex;
      flex-direction: column;
      gap: 24px;
        & > h2{
          width: 100%;
          text-align: center;

        }
    `)}
`;
export const Container = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
`;
export const WrapperChips = styled.div<{
  $isFetchingOrUpdating?: boolean;
}>`
  display: flex;
  gap: 7px;
  position: fixed;
  width: 100%;
  top: calc(194px);
  left: 0;
  background-color: #fff;
  z-index: 10;
  & > div {
    width: 100%;
    > div {
      min-width: 33%;
      border-bottom: 2px solid rgb(204, 204, 204);
      display: flex;
      align-items: flex-start;
      justify-content: center;
      & > svg {
        width: 18px;
        height: auto;
        & > path {
          fill: ${(props) =>
            props.$isFetchingOrUpdating
              ? "rgb(204, 204, 204)"
              : "rgb(0, 0, 0)"};
        }
      }
    }
  }
  & > button {
    display: flex;
    gap: 5px;
    border-radius: 8px;
    border: 1px solid #3e6474;
  }
`;
export const WrapperSearch = styled.div<{
  $maps?: boolean;
  $isFetchingOrUpdating?: boolean;
}>`
  font-family: Inter;
  position: relative;
  width: 100%;
  ${(props) =>
    props.theme.breakpoints.desktop(`
      max-width: 1200px;
      padding: 16px;
      padding-top: 0;
    `)}
  ${(props) =>
    props.$maps
      ? css`
          & > div > article {
            padding-left: 0;
            padding-right: 0;
            & > div.wrapperIcon {
              padding: 0 16px;
            }
          }
        `
      : null}
  > h1 {
    width: 100%;
    margin: 16px 0;
    margin-top: 50px;
    color: #000;
    font-size: 24px;
    font-style: normal;
    font-weight: 900;
    line-height: 120%;
  }
  > svg {
    max-width: 100%;
    width: auto;
    height: auto;
  }
  > h2 {
    margin: 16px 0;
    color: #000;
    font-size: 19px;
    font-style: normal;
    font-weight: 900;
    line-height: 24px; /* 100% */
    text-align: center;
  }
`;
