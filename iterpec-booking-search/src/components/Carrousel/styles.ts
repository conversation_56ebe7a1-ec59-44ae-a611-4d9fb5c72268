import styled, { css } from "styled-components";
import { ICarouselSlide } from "./types";

export const SCarouselWrapper = styled.div`
  display: flex;
  overflow: hidden;
  position: relative;
`;
export const CarrouselWrapper = styled.div<{ "data-children-size": number }>`
  position: relative;
  & > div.buttons {
    display: ${(props) => (props["data-children-size"] > 1 ? "flex" : "none")};
    align-items: center;
    background-color: none;
    position: absolute;
    top: 0;
    width: 30%;
    height: 100%;
    & > svg {
      background-color: ${(props) => props.theme.palette.neutral.lighter};
      border-radius: 100%;
      width: 30px;
      fill: ${(props) => props.theme.palette.neutral.darker};
      opacity: 0.5;
    }
    &.Right {
      right: 5px;
      justify-content: flex-end;
    }
    &.Left {
      justify-content: flex-start;
      left: 5px;
    }
  }
`;

export const SCarouselSlide = styled.div<ICarouselSlide>`
  flex: 0 0 auto;
  opacity: ${(props) => (props["data-active"] ? 1 : 0)};
  width: 105%;
  height: ${(props) =>
    props["data-active"]
      ? "auto"
      : "100%"}; // Mantém o tamanho durante a transição
  max-height: 380px;
  transition: all 0.5s ease-in-out; // Transição suave para ambos
`;

export const SCarouselSlides = styled.div`
  display: flex;
  width: 100%;
  transform: translateX(
    calc(-${(props: any) => props["data-current-slide"] * 106}%)
  );
  transition: ${(props: any) =>
    props["data-enable-transitions"] ? "transform 0.5s ease-in-out" : "none"};
`;

export const CarouselDotsWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
  width: 100%;
  position: absolute;
  top: 85%;
  > div {
    display: flex;
    gap: 8px;
    z-index: 11;
  }
`;

export const CarouselDot = styled.button<{ "data-active"?: boolean }>`
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: none;
  background: ${({ "data-active": active, theme }) =>
    active ? theme.palette.primary.main : theme.palette.neutral.lighter};
  opacity: ${({ "data-active": active }) => (active ? 1 : 0.5)};
  cursor: pointer;
  transition: background 0.2s, opacity 0.2s;
  padding: 0;
  outline: none;
`;
