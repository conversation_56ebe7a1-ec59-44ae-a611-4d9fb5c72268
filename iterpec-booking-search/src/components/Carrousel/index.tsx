import React, {
  isValidElement,
  useState,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import * as Styles from "./styles";
import * as Types from "./types";
import Icon from "@iterpecdev/icons-system";

const Carousel = ({
  children,
  useTimer = false,
  timeOut = 10,
  ...props
}: Types.IProps) => {
  const slides = useMemo(() => React.Children.toArray(children), [children]);
  const total = slides.length;

  const [currentSlide, setCurrentSlide] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [timer, setTimer] = useState(timeOut);
  const [enableTransitions, setEnableTransitions] = useState(true);

  // Sempre renderize 3 slides para manter a animação, mas use slides válidos
  const getVisibleSlides = useCallback(
    (index: number) => {
      if (total === 1) return [0, 0, 0];
      if (total === 2)
        return [(index - 1 + total) % total, index, (index + 1) % total];
      return [(index - 1 + total) % total, index, (index + 1) % total];
    },
    [total]
  );

  const [visibleSlides, setVisibleSlides] = useState(() => getVisibleSlides(0));

  // Atualiza os slides visíveis após a transição
  useEffect(() => {
    if (!isTransitioning) return;
    const transitionDuration = 500;
    setEnableTransitions(true);

    const timeout = setTimeout(() => {
      setVisibleSlides(getVisibleSlides(currentSlide));
      setIsTransitioning(false);
    }, transitionDuration);

    return () => clearTimeout(timeout);
  }, [isTransitioning, currentSlide, getVisibleSlides]);

  // Timer automático
  useEffect(() => {
    if (!useTimer || isTransitioning) return;
    const timeout = setTimeout(() => {
      if (timer > 0) {
        setTimer((prev) => prev - 1);
      } else {
        handleNext();
      }
    }, 1000);
    return () => clearTimeout(timeout);
  }, [timer, useTimer, isTransitioning]);

  // Atualiza slides visíveis ao mudar o total de slides
  useEffect(() => {
    setVisibleSlides(getVisibleSlides(currentSlide));
  }, [total, currentSlide, getVisibleSlides]);

  const handleNext = useCallback(() => {
    if (isTransitioning || total <= 1) return;
    setIsTransitioning(true);
    setCurrentSlide((prev) => (prev + 1) % total);
    setTimer(timeOut);
  }, [isTransitioning, total, timeOut]);

  const handlePrevious = useCallback(() => {
    if (isTransitioning || total <= 1) return;
    setIsTransitioning(true);
    setCurrentSlide((prev) => (prev - 1 + total) % total);
    setTimer(timeOut);
  }, [isTransitioning, total, timeOut]);

  // Calcula o índice relativo para o deslocamento correto
  const relativeSlideIndex = useMemo(() => {
    // Sempre 1 (slide do meio) para garantir a animação
    return 1;
  }, [visibleSlides, currentSlide, total]);

  // Renderiza sempre 3 slides para garantir a animação
  const renderedSlides = useMemo(() => {
    return visibleSlides.map((visibleIndex, idx) => {
      const slide = slides[visibleIndex];
      const isActive = idx === 1; // O slide do meio é sempre o ativo
      return (
        <Styles.SCarouselSlide
          data-active={isActive}
          key={idx + "-" + visibleIndex}
        >
          {isValidElement(slide) && slide.type === "img"
            ? React.cloneElement(slide as React.ReactElement<any>, {
                ...(isActive ? {} : { loading: "lazy" }),
              })
            : slide}
        </Styles.SCarouselSlide>
      );
    });
  }, [slides, visibleSlides]);

  return (
    <Styles.CarrouselWrapper
      {...props}
      className="carrouselWrapper"
      data-children-size={total}
      data-testid="carousel-wrapper"
    >
      <Styles.SCarouselWrapper
        className="slides"
        data-testid="carousel-slides-wrapper"
      >
        <Styles.SCarouselSlides
          data-current-slide={relativeSlideIndex}
          data-enable-transitions={enableTransitions}
          data-testid="carousel-slides"
        >
          {renderedSlides}
        </Styles.SCarouselSlides>
      </Styles.SCarouselWrapper>
      {/* Bolinhas de navegação */}
      {total > 1 && (
        <Styles.CarouselDotsWrapper className="dots">
          {(() => {
            // Agrupa as bolinhas em grupos de 5
            const groups = [];
            for (let i = 0; i < total; i += 5) {
              groups.push(
                slides.slice(i, i + 5).map((_, idx) => {
                  const realIdx = i + idx;
                  return (
                    <Styles.CarouselDot
                      key={realIdx}
                      data-active={currentSlide === realIdx}
                      onClick={() => {
                        if (!isTransitioning) {
                          setCurrentSlide(realIdx);
                          setVisibleSlides(getVisibleSlides(realIdx));
                          setTimer(timeOut);
                        }
                      }}
                    />
                  );
                })
              );
            }
            // Mostra apenas o grupo da bolinha ativa
            const activeGroup = Math.floor(currentSlide / 5);
            return <div>{groups[activeGroup]}</div>;
          })()}
        </Styles.CarouselDotsWrapper>
      )}
      {total > 1 && (
        <>
          <div
            className="Left buttons"
            onClick={handlePrevious}
            data-testid="carousel-prev-button"
          >
            <Icon name="ArrowLeft" />
          </div>
          <div
            className="Right buttons"
            onClick={handleNext}
            data-testid="carousel-next-button"
          >
            <Icon name="ArrowRight" />
          </div>
        </>
      )}
    </Styles.CarrouselWrapper>
  );
};

export default React.memo(Carousel);
