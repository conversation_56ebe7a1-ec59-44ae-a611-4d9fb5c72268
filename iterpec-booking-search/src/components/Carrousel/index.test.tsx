import React from "react";
import "@testing-library/jest-dom";

import {
  screen,
  fireClickEvent,
  styledRender,
  waitFor,
  act,
} from "../../tests";
import Carousel from "./index";

jest.useFakeTimers();

describe("Carousel Component", () => {
  const slides = [
    <img key="1" src="slide1.jpg" alt="Slide 1" />,
    <img key="2" src="slide2.jpg" alt="Slide 2" />,
    <img key="3" src="slide3.jpg" alt="Slide 3" />,
  ];

  it("should render the Carousel with slides", () => {
    styledRender(<Carousel>{slides}</Carousel>);
    expect(screen.getByAltText("Slide 1")).toBeInTheDocument();
    expect(screen.getByAltText("Slide 2")).toBeInTheDocument();
    expect(screen.getByAltText("Slide 3")).toBeInTheDocument();
  });

  it("should show only the current slide as active", async () => {
    styledRender(<Carousel>{slides}</Carousel>);
    const activeSlide = screen.getByAltText("Slide 1").parentElement;

    await waitFor(() => {
      expect(activeSlide).toHaveAttribute("data-active", "true");
    });
  });

  it("should navigate to the next slide when the next button is clicked", async () => {
    styledRender(<Carousel>{slides}</Carousel>);
    const nextButton = screen.getByTestId("carousel-next-button");

    act(() => {
      fireClickEvent(nextButton);
      jest.advanceTimersByTime(500); // Simula o tempo de transição
    });

    await waitFor(() => {
      const activeSlide = screen.getByAltText("Slide 2").parentElement;
      expect(activeSlide).toHaveAttribute("data-active", "true");
    });
  });

  it("should navigate to the previous slide when the previous button is clicked", () => {
    styledRender(<Carousel>{slides}</Carousel>);
    const prevButton = screen.getByTestId("carousel-prev-button");

    act(() => {
      fireClickEvent(prevButton);
      jest.advanceTimersByTime(500); // Simula o tempo de transição
    });

    const activeSlide = screen.getByAltText("Slide 3").parentElement;
    expect(activeSlide).toHaveStyle("opacity: 1");
  });

  it("should automatically navigate to the next slide when useTimer is true", async () => {
    styledRender(
      <Carousel useTimer timeOut={3}>
        {slides}
      </Carousel>
    );

    act(() => {
      jest.advanceTimersByTime(4000); // Simula o tempo do timer com margem para transição
    });

    await waitFor(() => {
      const activeSlide = screen.getByAltText("Slide 2").parentElement;
      expect(activeSlide).toHaveStyle("opacity: 1");
    });
  });

  it("should reset the timer when navigating manually", async () => {
    styledRender(
      <Carousel useTimer timeOut={1}>
        {slides}
      </Carousel>
    );
    const nextButton = screen.getByTestId("carousel-next-button");

    act(() => {
      fireClickEvent(nextButton);
      jest.advanceTimersByTime(1000); // Simula o tempo de transição
    });

    await waitFor(() => {
      const activeSlide = screen.getByAltText("Slide 2").parentElement;
      expect(activeSlide).toHaveStyle("opacity: 1");
    });

    act(() => {
      jest.advanceTimersByTime(4000); // Simula o tempo restante após o reset
    });

    await waitFor(() => {
      const nextActiveSlide = screen.getByAltText("Slide 3").parentElement;
      expect(nextActiveSlide).toHaveStyle("opacity: 1");
    });
  });
});
