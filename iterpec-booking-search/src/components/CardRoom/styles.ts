import styled from "styled-components";

export const WrapperCardRoom = styled.div`
  * {
    font-family: Inter;
  }
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
  width: 100%;
  & > div {
    background-color: #fff;
    padding: 0;
    width: 95%;
    display: flex;
    gap: 8px;
    flex-direction: column;
    & > img {
      width: 100%;
    }
    & > div.WrapperDescription {
      padding: 0 10px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      & > h2 {
        width: 100%;
        color: #000;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 133.333% */
      }
      & > div > p,
      & > p {
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px; /* 160% */
        &.refundable {
          color: #a4ce4a;
        }
        &.noRefundable {
          color: #a7183c;
        }
      }
    }
    & > div.WrapperFooter {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 10px;
      margin-top: auto;
      & > div > p {
        color: #878787;
        font-family: Inter;
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px; /* 160% */

        & > b {
          color: #000;
          font-family: Inter;
          font-size: 18px;
          font-style: normal;
          font-weight: 600;
          line-height: 20px; /* 100% */
        }
        &.totalPrice {
          & > b {
            font-size: 13px;
          }
        }
      }
    }
    & > div.WrapperAmenities {
      display: flex;
      flex-wrap: wrap;
      gap: 0 12px;
      padding: 0 10px;
      & > span {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        color: #878787;
        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        gap: 4px;
      }
      & > svg {
        width: 20px;
        height: auto;
      }
    }
  }
`;
