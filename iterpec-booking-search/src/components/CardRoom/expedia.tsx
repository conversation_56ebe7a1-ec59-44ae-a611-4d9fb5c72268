import * as Styles from "./styles";
import { <PERSON><PERSON>, <PERSON> } from "@iterpecdev/design-sistem";
import React, { useEffect, useMemo } from "react";
import Carousel from "../Carrousel";
import moment from "moment";
import { Rate } from "../../services/types/getAvailability";
import { IStaticData } from "../../services/types";
//@ts-ignore
import { Hooks, Utils } from "@iterpec/booking-utility";
import ImageNotFoud from "../ImageNotFound";
import AmenitiesIcon from "../AmenitiesIcon";
import { AmenitiesIconNoRepeat } from "../AmenitiesIcon/noRepeat";
import { useTranslation } from "react-i18next";
import { Static, Room } from "../../services/types/getStaticData";

const CardRoom: React.FC<
  Room & {
    onClick: (e: any) => unknown;
    rates?: Rate[];
  }
> = (room) => {
  const { useCurrency } = Hooks;
  const { cachedImage } = Utils;
  const { t } = useTranslation();

  // Memoize reordered images so it recalculates só se room.images mudar
  const sortedImages = useMemo(() => {
    if (!room?.images?.length) return [];
    const heroIndex = room.images.findIndex((img) => img.heroImage === true);
    if (heroIndex === -1) return room.images;
    // cria nova ordem com a heroImage no início
    return [
      room.images[heroIndex],
      ...room.images.slice(0, heroIndex),
      ...room.images.slice(heroIndex + 1),
    ];
  }, [room.images]);

  // Memoize amenities para evitar recomputação
  const combinedAmenities = useMemo(() => {
    const rateAmenities =
      room?.rates?.[0]?.amenities && room.rates[0].amenities.length
        ? room.rates[0].amenities
        : [];
    return [...(room?.amenities || []), ...rateAmenities];
  }, [room.amenities, room.rates]);

  const rate = room.rates?.[0];

  const totalPriceWithTaxes = useMemo(() => {
    if (!rate?.totalPriceInclusive?.value) return null;

    const totalPrice = rate.totalPriceInclusive.value;

    const resortFee = rate.resortFeesRequest?.priceTotal?.value ?? 0;

    const mandatoryFee = rate.mandatoryTaxRequest?.priceTotal?.value ?? 0;

    const grandTotal = totalPrice + resortFee + mandatoryFee;

    return useCurrency(grandTotal);
  }, [rate, useCurrency]);

  const averagePerNight = useMemo(() => {
    return useCurrency(rate?.averagePerNight?.value);
  }, [rate, useCurrency]);

  return (
    <Styles.WrapperCardRoom id={room.id}>
      <Card>
        <Carousel>
          {sortedImages.length
            ? sortedImages.map((image, index) => (
                <img
                  loading="lazy"
                  src={cachedImage(
                    image.links?.["1000px"]?.href ??
                      image.links?.["350px"]?.href
                  )}
                  key={index}
                  alt={image.caption || `Room image ${index + 1}`}
                />
              ))
            : [1, 2].map((item) => <ImageNotFoud key={item} />)}
        </Carousel>

        <div className="WrapperAmenities">
          {combinedAmenities.length ? (
            <AmenitiesIconNoRepeat amenities={combinedAmenities} />
          ) : null}
        </div>

        <div className="WrapperDescription">
          <h2>{room?.name}</h2>
          <div>
            {room?.area?.squareMeters && (
              <p>
                <b>{t("cardroom.size")}: </b>
                {room.area.squareMeters}m²
              </p>
            )}

            {room?.bedGroups?.length && room.bedGroups[0]?.description && (
              <p>
                <b>{t("cardroom.beds")}: </b>
                {room.bedGroups[0].description}
              </p>
            )}
          </div>
        </div>

        <div className="WrapperDescription">
          {rate?.cancelPenalties?.map((penalty, index) => {
            if (
              penalty &&
              rate.refundable &&
              moment(penalty.startDate).isAfter(new Date()) &&
              moment(penalty.endDate).isAfter(new Date())
            ) {
              return (
                <p key={index} className="refundable">
                  {t("selectPrice.freeCancellationUntil")}{" "}
                  <b>
                    {moment(penalty.startDate)
                      .subtract(1, "days")
                      .format("DD/MM/YYYY")}
                  </b>
                </p>
              );
            }
            return (
              <p key={index} className="noRefundable">
                <span>{t("selectPrice.nonRefundable")}</span>
              </p>
            );
          })}
        </div>

        <div className="WrapperFooter">
          {rate ? (
            <div className="WrapperPrice">
              <p>
                {t("cardroom.from")}
                <br />
                <b>{averagePerNight}</b> {t("cardhotel.perNight")}
              </p>
              <p className="totalPrice">
                <b>{totalPriceWithTaxes}</b> {t("cardhotel.total")}
                <br />
                {t("cardhotel.totalIncluded")}
              </p>
            </div>
          ) : (
            <p>
              <b style={{ color: "#a7183c" }}>{t("cardroom.noAvailability")}</b>
            </p>
          )}

          <Button onClick={room.onClick}>{t("cardroom.details")}</Button>
        </div>
      </Card>
    </Styles.WrapperCardRoom>
  );
};

export default CardRoom;
