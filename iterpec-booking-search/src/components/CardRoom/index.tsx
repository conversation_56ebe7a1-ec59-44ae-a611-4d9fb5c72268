import Icon from "@iterpecdev/icons-system";
import * as Styles from "./styles";
import { <PERSON><PERSON>, <PERSON> } from "@iterpecdev/design-sistem";
import React, { Fragment } from "react";
import Carousel from "../Carrousel";
import { Rate, Room } from "../../services/types/getAvailability";
//@ts-ignore
import { Hooks } from "@iterpec/booking-utility";
import ImageNotFoud from "../ImageNotFound";
import AmenitiesIcon from "../AmenitiesIcon";
import { AmenitiesIconNoRepeat } from "../AmenitiesIcon/noRepeat";
import { useTranslation } from "react-i18next";
const CardRoom: React.FC<
  Room & { onClick: (e: any) => unknown; rates?: Rate[] }
> = (room) => {
  const { useCurrency } = Hooks;
  const { t } = useTranslation();
  return (
    <Styles.WrapperCardRoom id={room.roomName}>
      <Card>
        <Carousel>
          {room?.photos?.length
            ? room?.photos?.map((image, index) => (
                <img
                  loading="lazy"
                  src={image.urlSize350px ?? image.urlSize1000px}
                  key={index}
                />
              ))
            : [1].map((item) => <ImageNotFoud />)}
        </Carousel>
        <div className="WrapperAmenities">
          {room?.amenities?.length ? (
            <AmenitiesIconNoRepeat
              amenities={[
                ...room?.amenities,
                ...(room?.rates?.length && room?.rates[0]?.amenities?.length
                  ? room?.rates[0]?.amenities
                  : []),
              ]}
            />
          ) : null}
        </div>
        <div className="WrapperDescription">
          <h2>{room?.roomName}</h2>
          <div>
            <p>
              {/*     {room?.area?.squareMeters ? (
                <>
                  <b>{t("cardroom.size")}: </b>
                  {room?.area?.squareMeters}
                  {"m² "}
                </>
              ) : null} */}
            </p>
            <p>
              {/*    {room?.bedConfiguration[0]?.description ? (
                <>
                  <b>{t("cardroom.beds")}: </b>
                  {room?.bedConfiguration[0]?.description}
                </>
              ) : null} */}
            </p>
            <p>
              {room?.rates[0]?.availbleQuantity ? (
                <>
                  <b>{t("cardroom.availableRooms")}: </b>
                  {room?.rates[0]?.availbleQuantity}
                </>
              ) : null}
            </p>
          </div>
        </div>

        <div className="WrapperFooter">
          {room && room?.rates?.length ? (
            <p>
              {t("cardroom.from")} <br />
              <b>{useCurrency(room.rates[0]?.totalPriceInclusive?.value)}</b>
            </p>
          ) : (
            <p>
              <b style={{ color: "#a7183c" }}>{t("cardroom.noAvailability")}</b>
            </p>
          )}
          <Button onClick={room.onClick}>{t("cardroom.details")}</Button>
        </div>
      </Card>
    </Styles.WrapperCardRoom>
  );
};

export default CardRoom;
