import React from 'react';
import { useTranslation } from 'react-i18next';

const LanguageSwitcher = () => {
  const { i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div>
      <button onClick={() => changeLanguage('en-US')}>English</button>
      <button onClick={() => changeLanguage('es-MX')}>Español</button>
      <button onClick={() => changeLanguage('pt-BR')}>Português</button>
    </div>
  );
};

export default LanguageSwitcher;