import styled from "styled-components";

export const wrapperCard = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  & > hr {
    width: 100%;
    height: 3px;
  }
  & > button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
  }
`;
export const wrapperCardReview = styled.div`
  display: flex;
  gap: 10px;

  & > div.wrapperAvatar {
    display: flex;
    justify-content: start;
    align-items: center;
    flex-direction: column;
    & > div {
      margin: 1rem 0;
    }
    & > span {
      color: #878787;
      font-family: Inter;
      font-size: 14px;
      text-transform: lowercase;
      text-align: center;
      &::first-letter {
        text-transform: capitalize;
      }
    }
  }
  & > div.wrapperReview {
    width: 100%;
    color: #878787;
    font-family: Inter;
    & > span {
      width: 100%;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px; /* 171.429% */
    }
    & > h4 {
      color: #000;
      font-family: Inter;
      font-size: 13px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px; /* 184.615% */
    }
    & > div.wrapperButton {
      display: flex;
      align-items: end;
      justify-content: end;
    }
    & > div.wrapperStars {
      display: flex;
      gap: 10px;
      font-size: 14px;
      & > div > svg {
        width: 20px;
        height: auto;
      }
    }
  }
`;
