import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { IReview } from "../../services/types";
import { useTranslation } from "react-i18next";
import Stars from "../Stars";
import Api from "../../services";
import moment from "moment";
import {
  Hooks,
  getSearch,
  //@ts-ignore
} from "@iterpec/booking-utility";

const CardReview: React.FC<{ idHotel: string; hotelName: string }> = ({
  idHotel,
  hotelName,
}) => {
  const api = new Api();
  const search = getSearch();
  const { t } = useTranslation();
  const isMobile = Hooks.useMobileDetect().isMobile();

  const [allReviews, setAllReviews] = useState<IReview["verified"]["recent"]>(
    []
  );
  const [visibleReviews, setVisibleReviews] = useState<
    IReview["verified"]["recent"]
  >([]);
  const [reviewsPerPage] = useState(5);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);

  const observer = useRef<IntersectionObserver>();
  const toggleReviewsDisplay = useCallback(() => {
    setOpen((prevOpen) => {
      const newOpen = !prevOpen;

      if (newOpen) {
        setVisibleReviews(allReviews); // mostra tudo
      } else {
        setVisibleReviews(allReviews.slice(0, reviewsPerPage)); // volta para o início
        setPage(1);
      }

      return newOpen;
    });
  }, [allReviews, reviewsPerPage]);

  const lastReviewRef = useCallback(
    (node: HTMLDivElement) => {
      if (loading) return;
      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting) {
          loadMoreReviews();
        }
      });

      if (node) observer.current.observe(node);
    },
    [loading, page, allReviews]
  );

  const loadMoreReviews = () => {
    setLoading(true);
    const nextPage = page + 1;
    const nextReviews = allReviews.slice(0, nextPage * reviewsPerPage);

    setVisibleReviews(nextReviews);
    setPage(nextPage);
    setLoading(false);
  };
  const [open, setOpen] = useState(false);

  const getReview = async () => {
    try {
      const { data } = await api.getReviews({
        propertyId: idHotel,
        searchId: search.searchId,
      });
      const recent = (data[0]?.verified?.recent ?? []).sort(
        (a, b) =>
          new Date(b.date_submitted).getTime() -
          new Date(a.date_submitted).getTime()
      );

      setAllReviews(recent);
      setVisibleReviews(recent.slice(0, reviewsPerPage));
      setPage(1);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getReview();
  }, []);

  return (
    <>
      {allReviews.length > 0 && (
        <Styles.wrapperCard>
          {visibleReviews.map((review, index) => {
            const isLast = index === visibleReviews.length - 1;
            return (
              <div key={index} ref={isLast && !isMobile ? lastReviewRef : null}>
                <Styles.wrapperCardReview>
                  <div className="wrapperAvatar">
                    <Avatar
                      label={review.reviewer_name}
                      size="lg"
                      variant="primary"
                    />
                  </div>
                  <div className="wrapperReview">
                    <h4>{review.reviewer_name}</h4>
                    <h4>{review.title ?? hotelName}</h4>{" "}
                    <div className="wrapperStars">
                      <Stars stars={review.rating} />{" "}
                      <span>
                        {moment(review.date_submitted).format("DD/MM/YYYY")}
                        <br />
                      </span>
                    </div>
                    <span>{review.text}</span>
                    <p>
                      <small>{review.verification_source}</small>
                    </p>
                  </div>
                </Styles.wrapperCardReview>
                <Divider />
              </div>
            );
          })}
          {loading && <p>{t("cardReview.loading", "Carregando...")}</p>}
          {!!isMobile && (
            <Button onClick={loadMoreReviews} variant="link">
              {t("cardReview.seeMore")}
            </Button>
          )}
        </Styles.wrapperCard>
      )}
    </>
  );
};

export default CardReview;
