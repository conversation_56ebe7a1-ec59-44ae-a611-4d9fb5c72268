import { Tab, TabItem } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import { useScrollPosition } from "../../hooks";
//@ts-ignore
import { Hooks } from "@iterpec/booking-utility";
import { useTranslation } from "react-i18next";
export const ScrollFixedTab = ({ handleTab }) => {
  const scrollPosition = useScrollPosition();
  const { t } = useTranslation();
  const isMobile = Hooks.useMobileDetect().isMobile();
  const isFixed = isMobile ? scrollPosition >= 430 : scrollPosition >= 630;
  return (
    <>
      {isFixed ? <div style={{ height: "40px" }} /> : null}
      <Styles.WrapperTab isfixed={isFixed}>
        <Tab onTabSelected={handleTab}>
          <TabItem></TabItem>
          <TabItem onClick={() => handleTab(1)}>
            {t("scrollFixedTab.rooms")}
          </TabItem>
          <TabItem onClick={() => handleTab(2)}>
            {t("scrollFixedTab.amenities")}
          </TabItem>
          <TabItem onClick={() => handleTab(3)}>
            {t("scrollFixedTab.reviews")}
          </TabItem>
          <TabItem onClick={() => handleTab(4)}>
            {t("scrollFixedTab.location")}
          </TabItem>
          <TabItem onClick={() => handleTab(5)}>
            {t("scrollFixedTab.policies")}
          </TabItem>
          <TabItem onClick={() => handleTab(6)}>
            {t("scrollFixedTab.checkinCheckout")}
          </TabItem>
          <TabItem onClick={() => handleTab(7)}>
            {t("scrollFixedTab.fees")}
          </TabItem>
        </Tab>
      </Styles.WrapperTab>
    </>
  );
};
