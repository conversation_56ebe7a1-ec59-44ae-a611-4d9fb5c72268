import styled, { css } from "styled-components";

export const WrapperTab = styled.div<{ isfixed: boolean }>`
  background-color: #fff;
  padding-top: 10px;
  z-index: 14;
  width: 100%;
  overflow-x: auto;

  & > div {
    width: fit-content;
    & > div {
      &:first-child {
        width: fit-content;
        display: none;
      }
      width: max-content;
      min-width: 64px;
    }
  }
  ${({ isfixed }) =>
    isfixed
      ? css`
          position: fixed;
          top: 190px;
          left: 0;
          ${(props) =>
            props.theme.breakpoints.desktop(`
              top:70px;
              display: flex;
              justify-content: center;
              align-items: center;
              & > div {
                max-width: 1200px;
                width: 100%;
              }
            `)}
        `
      : null}
`;
