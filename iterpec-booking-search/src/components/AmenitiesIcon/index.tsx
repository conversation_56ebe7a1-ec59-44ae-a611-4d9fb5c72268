import Icon from "@iterpecdev/icons-system"; // Certifique-se de que o componente Icon está sendo importado corretamente
import { iconName } from "../../utils/iconName";

const AmenitiesIcon = (props) => {
  const iconNametext = iconName(props?.name);

  if (!iconNametext) {
    console.warn(`Icon not found for amenity: ${props?.name}`);
    return <></>;
  }

  return <Icon name={iconNametext} aria-label={props?.name} />;
};

export default AmenitiesIcon;
