import React, { Fragment, useMemo } from "react";
import AmenitiesIcon from ".";
import { iconName } from "../../utils/iconName";

export const AmenitiesIconNoRepeat: React.FC<{
  amenities?: { name: string }[];
}> = ({ amenities }) => {
  const uniqueIcons = useMemo(() => {
    if (!amenities?.length) return [];

    const seen = new Set<string>();
    const result: JSX.Element[] = [];

    for (const { name } of amenities) {
      const icon = iconName(name);

      if (typeof icon === "string" && icon.length > 0 && !seen.has(icon)) {
        seen.add(icon);
        result.push(<AmenitiesIcon key={icon} aria-label={name} name={name} />);
      }
    }

    return result;
  }, [amenities]);

  return <>{uniqueIcons}</>;
};
