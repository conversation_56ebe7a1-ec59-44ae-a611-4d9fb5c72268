import {
  Checkbox,
  Divider,
  <PERSON>Button,
  Tooltip,
} from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import React, { useCallback, useMemo } from "react";
//@ts-ignore
import { getRegion, getSearch, Hooks } from "@iterpec/booking-utility";
import moment from "moment";
import { useTranslation } from "react-i18next";
import Icon from "@iterpecdev/icons-system";
import { Rate } from "../../services/types/getAvailability";

export const SelectPrice: React.FC<
  Rate & { checked?: boolean; onChange: (e: boolean) => void }
> = ({ checked, onChange, ...rate }) => {
  const { useCurrency } = Hooks;
  const { language } = getRegion();
  moment.locale(language);
  const { t } = useTranslation();
  const search = getSearch();

  const cancelPenalties = useCallback(
    () =>
      rate?.cancelPenalties?.sort((dataA, dataB) =>
        moment(dataA.startDate).isBefore(dataB.startDate) ? -1 : 1
      ),
    [rate?.cancelPenalties]
  );
  const { grandTotal } = useMemo(() => {
    const nights = moment(search.checkOut).diff(search.checkIn, "days") || 1;

    const totalPrice = rate?.totalPriceInclusive?.value || 0;
    const averagePerNight = rate?.averagePerNight?.value || 0;

    const exclusive = rate?.totalPriceExclusive?.value || 0;
    const taxesAndFees = totalPrice - exclusive;

    const resortFeesRequestPricePerNight =
      rate?.resortFeesRequest?.pricePerNight.value ?? null;

    const mandatoryFee = rate?.mandatoryTaxRequest?.priceTotal?.value ?? null;

    const resortFee = rate?.resortFeesRequest?.priceTotal?.value ?? null;

    const totalExtraFees = [mandatoryFee, resortFee]
      .filter((v): v is number => v !== null)
      .reduce((sum, v) => sum + v, 0);

    const grandTotal = totalPrice + totalExtraFees;

    return {
      nights,
      totalPrice,
      averagePerNight,
      taxesAndFees,
      mandatoryFee,
      resortFee,
      totalExtraFees,
      grandTotal,
      resortFeesRequestPricePerNight,
    };
  }, [rate, search]);
  const firstPenalties: {
    text: string;
    startDate: Date | string;
    endDate: Date | string;
  } = cancelPenalties()[0] ?? null;

  return (
    <>
      <Styles.WrapperSelectPrice>
        <div>
          <p>
            <b>{useCurrency(grandTotal)}</b>
            <br />
            {rate.amenities.length
              ? rate.amenities.map((amenitie) => amenitie.name).join(", ") + "."
              : null}
          </p>
          {rate.refundable &&
          firstPenalties &&
          moment(firstPenalties?.startDate).isAfter(new Date()) &&
          moment(firstPenalties?.endDate).isAfter(new Date()) ? (
            <p className={"refundable"}>
              <span>
                {t("selectPrice.freeCancellationUntil")}{" "}
                <b>
                  {moment(
                    moment(firstPenalties?.startDate).subtract(1, "days")
                  ).format("DD/MM/YYYY")}
                </b>
              </span>
              <Icon name="InformationSlabCircleOutline" />
              <Tooltip arrow="top">
                <p>{firstPenalties.text}</p>
              </Tooltip>
            </p>
          ) : (
            <>
              <p className={"noRefundable"}>
                <span>{t("selectPrice.nonRefundable")}</span>
                <Icon name="InformationSlabCircleOutline" />
                <Tooltip arrow="top">
                  <p>{firstPenalties.text}</p>
                </Tooltip>
              </p>
            </>
          )}
        </div>
        {checked ? (
          <RadioButton onChangeValue={onChange} checked={checked} />
        ) : (
          <RadioButton onChangeValue={onChange} checked={false} />
        )}
      </Styles.WrapperSelectPrice>
      <Divider />
    </>
  );
};
