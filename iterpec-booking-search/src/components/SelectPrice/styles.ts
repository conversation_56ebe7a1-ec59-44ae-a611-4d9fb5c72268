import styled from "styled-components";

export const WrapperSelectPrice = styled.div`
  display: flex;
  justify-content: space-between;
  min-height: 50px;
  & > div {
    font-family: Inter;
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    padding: 20px 0;
    position: relative;
    & > p {
      color: #878787;
      font-size: 14px;
      max-width: fit-content;
      font-weight: 400;
      line-height: 20px;

      & > b {
        color: #000;
        font-size: 16px;
        font-weight: 600;
      }
      & > svg {
        width: 15px;
        height: 15px;
        & > path {
          fill: #878787;
        }
      }
      & > div {
        visibility: hidden;
        opacity: 0;
        width: 0;
        height: 0;
        display: none;
      }
      &:hover {
        max-width: 100%;
        & > div {
          display: block;
          margin-top: 2px;
          height: max-content;
          width: max-content;
          max-width: 80%;
          visibility: visible;
          position: absolute;
          z-index: 20;
          top: 90%;
          left: 0;
          opacity: 1;
          transition: opacity 0.5s ease-in-out;
        }
      }
      &.noRefundable {
        color: rgba(255, 0, 0);
        position: relative;
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 5px;
      }

      &.refundable {
        color: #a4ce4a;
        & > b {
          color: #a4ce4a;
        }
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 5px;
      }
    }
  }
`;
