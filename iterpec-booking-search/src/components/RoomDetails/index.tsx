import Carousel from "../Carrousel";
import * as Styles from "./styles";
import { <PERSON><PERSON>, Divider } from "@iterpecdev/design-sistem";
import { Rate } from "../../services/types/getAvailability";
import React, {
  Fragment,
  useEffect,
  useState,
  useMemo,
  useCallback,
} from "react";
import { SelectPrice } from "../SelectPrice";
import { useNavigate } from "react-router-dom";
import ImageNotFoud from "../ImageNotFound";

import { Room } from "../../services/types/getStaticData";

import {
  getHotel,
  getSearch,
  setHotel,
  Types,
  Hooks,
  Utils,
  //@ts-ignore
} from "@iterpec/booking-utility";
import { useTranslation } from "react-i18next";

const RoomDetails: React.FC<{
  selectedRoom: Room;
  onDismiss: () => void;
}> = ({ selectedRoom, onDismiss }) => {
  const [openAmenities, setOpenAmenities] = useState(false);
  const [expandedDescription, setExpandedDescription] = useState(false);
  const [selectPrice, setselectPrice] = useState<Types["IHotel"]["rate"]>(null);
  const search = useMemo(() => getSearch(), []);
  const hotel = useMemo(() => getHotel(), []);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { cachedImage } = Utils;

  const handleAmenities = useCallback(() => setOpenAmenities((v) => !v), []);
  const handleExpandedDescription = useCallback(
    () => setExpandedDescription((v) => !v),
    []
  );

  console.log(selectedRoom)
  const updateSelectPrice = useCallback(
    (rate: Types["IHotel"]["rate"]) => {
      setselectPrice(rate);
      setHotel({
        ...hotel,
        rate,
      });
    },
    [hotel]
  );

  // Ordena os rates do menor para o maior preço
  const sortedRates = useMemo(() => {
    //@ts-ignore
    if (!selectedRoom?.rates?.length) return [];
    //@ts-ignore
    return [...selectedRoom.rates].sort(
      (a, b) =>
        (a?.totalPriceInclusive?.value ?? Infinity) -
        (b?.totalPriceInclusive?.value ?? Infinity)
    );
    //@ts-ignore
  }, [selectedRoom?.rates]);
  return (
    <Styles.WrapperRoomDetails>
      {!!selectedRoom?.images?.length ? (
        <div style={{ position: "relative" }}>
          <Carousel>
            {selectedRoom?.images.map((image, index) => (
              <img
                loading="lazy"
                src={cachedImage(image.links["1000px"].href)}
                key={index}
                alt={image.caption}
              />
            ))}
          </Carousel>
        </div>
      ) : (
        <ImageNotFoud />
      )}
      <Styles.WrapperRoomDescriptions aria-expanded={expandedDescription}>
        <div className="WrapperDescription">
          <p>
            <b>{t("roomDetails.roomFeatures")}</b>
          </p>
          <p
            dangerouslySetInnerHTML={{
              __html: selectedRoom?.descriptions.overview,
            }}
          />
          <Button variant="link" onClick={handleExpandedDescription}>
            {!expandedDescription
              ? t("roomDetails.fullDescription")
              : t("roomDetails.collapseDescription")}
          </Button>

          <Divider />
        </div>

        <div className="WrapperAmenitiesList">
          <p>
            <b>{t("roomDetails.roomAmenities")}</b>
          </p>

          <ul>
            {(openAmenities
              ? selectedRoom.amenities
              : selectedRoom.amenities?.slice(0, 5)
            )?.map((amenitie) => (
              <li key={amenitie.id}>{amenitie.name}</li>
            ))}
          </ul>
        </div>
        <div className="WrapperButton">
          <Button variant="link" onClick={handleAmenities}>
            {!openAmenities
              ? t("roomDetails.seeMore")
              : t("roomDetails.seeLess")}
          </Button>
          <Divider />
        </div>
        <div className="WrapperPrices">
          {!!sortedRates?.length &&
            sortedRates.map((rate) => (
              <Fragment key={rate.id}>
                <SelectPrice
                  {...rate}
                  onChange={(check) => {
                    if (check) updateSelectPrice(rate);
                  }}
                  checked={rate?.rateId === selectPrice?.rateId}
                />
              </Fragment>
            ))}
        </div>
      </Styles.WrapperRoomDescriptions>
      <div className="WrapperButtonBooking">
        {!!sortedRates.length ? (
          <Button
            variant="primary"
            onClick={() => {
              navigate("/checkout");
            }}
            disabled={!selectPrice}
          >
            {search?.rooms.length > 1
              ? t("roomDetails.bookMultipleRooms", {
                  count: search?.rooms.length,
                })
              : t("roomDetails.bookRoom")}
          </Button>
        ) : (
          <Button variant="primary" onClick={onDismiss}>
            {t("roomDetails.goBack")}
          </Button>
        )}
      </div>
    </Styles.WrapperRoomDetails>
  );
};

export default RoomDetails;
