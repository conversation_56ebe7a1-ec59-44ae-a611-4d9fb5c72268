import styled from "styled-components";

export const WrapperRoomDetails = styled.div`
  ${(props) =>
    props.theme.breakpoints.mobile(`
      max-height: 79vh;
    `)}

  ${(props) =>
    props.theme.breakpoints.desktop(`
      width: 500px;
    `)}
  overflow: auto;
  & > div.WrapperButtonBooking {
    ${(props) =>
      props.theme.breakpoints.desktop(`
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 20px;
        & > button {
 
        }
    `)}
    & > button {
      margin: 20px 0;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
`;
export const WrapperRoomDescriptions = styled.div<{ "aria-expanded": boolean }>`
  display: block;
  ${(props) =>
    props.theme.breakpoints.mobile(`
      height: 58%;
    `)}
  box-sizing: border-box;
  & > div.WrapperButton {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    & > hr {
      width: 100%;
    }
  }
  & > div.WrapperAmenitiesList {
    & > p {
      line-height: 200%;
      margin: 10px 0;
    }
    & > ul {
      margin-top: 1rem;
      list-style-position: inside;
      & > li {
        color: #a5a5a5;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 200%; /* 171.429% */
      }
    }
  }
  & > div.WrapperDescription {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 12px;
    & > button {
      background: #ffffff;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    & > p > p {
      line-height: 150%;
      max-width: 100%;
      &:not(:nth-child(-n + 3)) {
        display: ${(props) => (props["aria-expanded"] ? "block" : "none")};
      }
    }
  }
  & > div.WrapperAmenities {
    margin-top: 13px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: space-around;
    & > div {
      width: fit-content;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      color: #a5a5a5;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px; /* 171.429% */
      & > svg {
        width: 25px;
        fill: #878787;
      }
    }
  }
`;
