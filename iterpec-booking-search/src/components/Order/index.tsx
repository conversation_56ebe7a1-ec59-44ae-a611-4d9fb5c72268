import { <PERSON>Button, But<PERSON> } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { IOrder } from "../Search";

const Order = (props) => {
  const [order, setOrder] = useState<IOrder>({
    field: "SCORE",
    ascending: false,
  });
  const handleOrderChange = (field: string, ascending: boolean) => {
    setOrder({ field, ascending });
  };

  useEffect(() => {
    props?.handleOrder?.(order);
  }, [order]);
  const { t } = useTranslation();
  return (
    <Styles.WrapperOrder>
      <div className="WrapperRow">
        <div>
          <h3>{t("order.recommended")}</h3>
          <RadioButton
            checked={order?.field === "SCORE" && order.ascending}
            onChangeValue={(e) => e && handleOrderChange("SCORE", true)}
          >
            {t("order.ascending")}
          </RadioButton>
          <RadioButton
            checked={order?.field === "SCORE" && !order.ascending}
            onChangeValue={(e) => e && handleOrderChange("SCORE", false)}
          >
            {t("order.descending")}
          </RadioButton>
        </div>

        <div>
          <h3>{t("order.price")}</h3>
          <RadioButton
            checked={order?.field === "PRICE" && order.ascending}
            onChangeValue={(e) => e && handleOrderChange("PRICE", true)}
          >
            {t("order.ascending")}
          </RadioButton>
          <RadioButton
            checked={order?.field === "PRICE" && !order.ascending}
            onChangeValue={(e) => e && handleOrderChange("PRICE", false)}
          >
            {t("order.descending")}
          </RadioButton>
        </div>
        <div>
          <h3>{t("order.popularity")}</h3>
          <RadioButton
            checked={order?.field === "RATING" && order.ascending}
            onChangeValue={(e) => e && handleOrderChange("RATING", true)}
          >
            {t("order.ascending")}
          </RadioButton>
          <RadioButton
            checked={order?.field === "RATING" && !order.ascending}
            onChangeValue={(e) => e && handleOrderChange("RATING", false)}
          >
            {t("order.descending")}
          </RadioButton>
        </div>
        <div>
          <h3>{t("order.stars")}</h3>
          <RadioButton
            checked={order?.field === "STARS" && order.ascending}
            onChangeValue={(e) => e && handleOrderChange("STARS", true)}
          >
            {t("order.ascending")}
          </RadioButton>
          <RadioButton
            checked={order?.field === "STARS" && !order.ascending}
            onChangeValue={(e) => e && handleOrderChange("STARS", false)}
          >
            {t("order.descending")}
          </RadioButton>
        </div>
      </div>
      <Button onClick={() => props?.handleOrder?.(order)}>
        {t("order.orderButton")}
      </Button>
    </Styles.WrapperOrder>
  );
};

export default Order;
