import styled from "styled-components";

export const WrapperOrder = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: space-between;
  height: 95%;
  & > div.WrapperRow {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    gap: 25px;
    & > div {
      display: flex;
      flex-direction: column;
      & > div {
        padding-left: 0;
      }
      & > h3 {
        color: #000;
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 133.333% */
      }
    }
  }
  & > button {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }
`;
