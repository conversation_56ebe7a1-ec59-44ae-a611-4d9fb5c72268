module.exports = {
  apps: [
    /* {
                          name: "booking-balance",
                          cwd: "./iterpec-booking-balance",
                          script: "yarn",
                          args: "start",
                          exec_mode: "cluster",
                          instances: 1,
                          env: {
                              NODE_ENV: "development",
                          },
                          env_staging: {
                              NODE_ENV: "production",
                          },
                          env_production: {
                              NODE_ENV: "production",
                          },
                      }, */
    {
      name: "booking-checkout",
      cwd: "./iterpec-booking-checkout",
      script: "yarn",
      args: "start",
      watch: true,
      exec_mode: "cluster",
      instances: 1,
    },
    {
      name: "booking-dashboard",
      cwd: "./iterpec-booking-dashboard",
      script: "yarn",
      args: "start",
      watch: true,
      exec_mode: "cluster",
      instances: 1,
    },
    {
      name: "booking-search",
      cwd: "./iterpec-booking-search",
      script: "yarn",
      args: "start",
      watch: true,
      exec_mode: "cluster",
      instances: 1,
    },
    {
      name: "booking-nav-bar",
      cwd: "./iterpec-booking-nav-bar",
      script: "yarn",
      watch: true,
      args: "start",
      exec_mode: "cluster",
      instances: 1,
    },
    {
      name: "booking-main",
      cwd: "./iterpec-booking-main",
      watch: true,
      script: "yarn",
      args: "start",
      exec_mode: "cluster",
      instances: 1,
    },
    {
      name: "booking-utility",
      cwd: "./iterpec-booking-utility",
      script: "yarn",
      watch: true,
      args: "start",
      exec_mode: "cluster",
      instances: 1,
    },
  ],
};
