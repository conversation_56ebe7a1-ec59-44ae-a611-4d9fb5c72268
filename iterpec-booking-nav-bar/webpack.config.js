const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react-ts");
const Dotenv = require("dotenv-webpack");
const BundleAnalyzerPlugin =
  require("webpack-bundle-analyzer").BundleAnalyzerPlugin;

module.exports = (webpackConfigEnv, argv) => {
  const defaultConfig = singleSpaDefaults({
    orgName: "iterpec",
    projectName: "booking-nav-bar",
    webpackConfigEnv,
    argv,
  });
  return merge(defaultConfig, {
    plugins: [
      new Dotenv({
        path: webpackConfigEnv.isLocal ? ".env" : "production.env",
      }),
      //new BundleAnalyzerPlugin(),
    ],
    // modify the webpack config however you'd like to by adding to this object

    // TODO ALTERAR O reaac-date-range para ele funcionar com o react externo
    externals: [
      ...defaultConfig.externals,
      "@iterpecdev/design-sistem",
      "@iterpecdev/icons-system",
      "@iterpecdev/theme-system",
      "react",
      "moment",
      "axios",
      "react-dom",
      "yup",
      /^locale($|\/)/,
      /^date-fns($|\/)/, // Usando regex para incluir submódulos
      "styled-components",
      "react-router-dom",
      "axios",
      "react-is",
      "react-date-range",
      "react-draggable",
    ],
  });
};
