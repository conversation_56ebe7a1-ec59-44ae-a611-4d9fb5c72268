import { <PERSON><PERSON>er<PERSON>out<PERSON> } from "react-router-dom";
import { Routes } from "./Routes";
import { Provider } from "@iterpecdev/theme-system";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
//@ts-ignore
import { Hooks } from "@iterpec/booking-utility";
const App: React.FC<any> = (props) => {
  const { i18n, t } = useTranslation();
  const useTheme = Hooks.useTheme();

  useEffect(() => {
    window.addEventListener(
      "languageChange",
      (customEvent: CustomEvent<{ language: string }>) => {
        i18n.changeLanguage(customEvent?.detail?.language);
      }
    );
  }, []);
  return (
    <Provider theme={useTheme}>
      <BrowserRouter>
        <Routes />
      </BrowserRouter>
    </Provider>
  );
};

export default App;
