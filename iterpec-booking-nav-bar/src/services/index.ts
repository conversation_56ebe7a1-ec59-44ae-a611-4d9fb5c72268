import axios, { AxiosRequestHeaders, AxiosInstance, AxiosHeaders } from "axios";

// @ts-ignore
import { getRegion, getSearch, getUser } from "@iterpec/booking-utility";
import { IAutoComplete, IPreSearch } from "./types";

class Api {
  private expediaApi: AxiosInstance;
  private headers: AxiosRequestHeaders;
  private language: string;
  private searchId: string;
  private client: string;
  private token: string;
  private user: string;

  constructor() {
    this.language = getRegion()?.language;
    this.searchId = getSearch()?.searchId;
    this.token = getUser()?.accessToken ?? null;
    this.user = getUser()?.idUser ?? null;
    this.client = getUser()?.clientId ?? null;

    // Criando uma instância de AxiosHeaders
    this.headers = new AxiosHeaders();
    this.headers.set("Access-Control-Allow-Origin", "*");
    this.headers.set("Content-Type", "application/json;charset=utf-8");
    this.headers.set("Cache-Control", "no-cache");
    this.headers.set("language", this.language);
    this.headers.set("X-UserId", this.user);
    this.headers.set("x-clientId", this.client);
    this.headers.set("crossDomain", "true");
    this.headers.set("Authorization", `Bearer ${this.token}`);

    this.expediaApi = axios.create({
      baseURL: process.env.API_EXPEDIA_INTEGRATION,
      timeout: 40000,
      headers: this.headers,
    });
  }

  async autoComplete(word: string) {
    return await this.expediaApi.post<IAutoComplete[]>(
      `/autocomplete/tbi/fullName`,
      {
        mainTerm: word,
        returnLimit: 10,
      },
      {
        headers: {
          language: this.language,
        },
      }
    );
  }
}

export default Api;
