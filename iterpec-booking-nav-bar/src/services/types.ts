export interface IAutoComplete {
  id: string;
  name: string;
  localization: string;
  type: string;
}
/* export interface IAutoComplete {
  id: string;
  name: string;
  type: string;
  city: {
    id: string;
    name: string;
  };
  country: {
    id: string;
    name: string;
  };
  state: {
    id: string;
    name: string;
  };
} */

export interface IPreSearch {
  destiny: {
    id: string;
    type: string;
  };
}

export interface ErrorItem {
  description: string;
  inputName: string;
}

export interface ListErrors extends Array<ErrorItem> {}
