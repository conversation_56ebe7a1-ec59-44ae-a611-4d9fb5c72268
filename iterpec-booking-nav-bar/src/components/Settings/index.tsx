import * as Styles from "./styles";
import { But<PERSON>, Select } from "@iterpecdev/design-sistem";
import { useNavigate } from "react-router-dom";

//@ts-ignore
import { getRegion, setRegion } from "@iterpec/booking-utility";
import { useTranslation } from "react-i18next";

const Settings = ({
  onDemiss,
  setContent,
}: {
  onDemiss?: () => void;
  setContent?: (e: number) => void;
}) => {
  const { i18n, t } = useTranslation();
  const { language, currency } = getRegion();
  const region = getRegion();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);

    setRegion({
      ...region,
      language: lng,
      languageCode: lng.split("-")[0],
      currency,
    });
  };
  const changeCurrency = (currency: string) => {
    setRegion({
      ...region,
      currency,
    });
  };

  const handleDrawer = () => {
    setTimeout(() => {
      onDemiss?.();
      setContent?.(0);
    }, 400);
  };

  return (
    <>
      <Styles.WrapperSettings>
        <div>
          <Select
            label={t("settings.inputLanguage")}
            onChange={(e) => changeLanguage(e.target.value)}
            defaultValue={language}
          >
            <option value={"es-MX"}>Español</option>
            <option value={"pt-BR"}>Português</option>
            <option value={"en-US"}>English</option>
          </Select>
          <Select
            onChange={(e) => changeCurrency(e.target.value)}
            defaultValue={currency}
            label={t("settings.inputCurrency")}
          >
            <option value={"MXN"}>MXN $</option>
            <option value={"BRL"}>BRL $</option>
            <option value={"USD"}>USD $</option>
          </Select>
        </div>
        <div>
          <Button onClick={handleDrawer}>{t("settings.button")}</Button>
        </div>
      </Styles.WrapperSettings>
    </>
  );
};
export default Settings;
