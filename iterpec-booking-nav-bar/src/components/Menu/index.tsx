import { Avatar, Divider } from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next"; // Importar useTranslation
//@ts-ignore
import { getUser, getRegion, setUser } from "@iterpec/booking-utility";

const Menu = ({ onDismiss, handleMenu }) => {
  const navigate = useNavigate();
  const { t } = useTranslation(); // Usar o hook para traduções
  const user = getUser();
  const { currency, language } = getRegion();

  return (
    <Styles.WrapperManu>
      <div className="Wrapperhead">
        <Avatar size="lg" label={`${user?.name} ${user?.surname} `} />
        <h3>{user?.name + " " + user?.surname}</h3>
      </div>
      <div className="WrapperButtons">
        <div>
          <h2
            onClick={() => {
              navigate("/my-bookings");
              onDismiss();
            }}
          >
            {t("menu.myBookings")}
          </h2>
          <Divider />
        </div>
        <div
          onClick={() => {
            onDismiss();
            handleMenu();
          }}
        >
          <h2>{language + " · " + currency}</h2>
          <Divider />
        </div>
        {user?.groups?.includes("admin") ? (
          <div>
            <h2
              onClick={() => {
                navigate("/send-invite");
                onDismiss();
              }}
            >
              {t("menu.sendInvite")}
            </h2>
            <Divider />
          </div>
        ) : null}

        <div>
          <h2>{t("menu.contactUs")}</h2>
          <Divider />
        </div>

        <div>
          <h2
            onClick={() => {
              navigate("/terms");
              onDismiss();
            }}
          >
            {t("menu.privacyPolicy")}
          </h2>
          <Divider />
        </div>
        <div>
          <h2
            onClick={() => {
              navigate("/login");
              onDismiss();
              setUser(null);
            }}
          >
            {t("menu.logout")}
          </h2>
          <Divider />
        </div>
      </div>
    </Styles.WrapperManu>
  );
};

export default Menu;
