import styled from "styled-components";

export const WrapperManu = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 42px;
  & > div.WrapperButtons {
    width: 100%;
    & > div {
      min-height: 45px;
      & > h2 {
        margin-bottom: 12px;
        color: #000;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        & > img {
          margin-left: 0.3rem;
          block-size: 0.875rem;
          inline-size: 1.25rem;
        }
      }
    }
  }
  & > div.Wrapperhead {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 9px;
    & > h3 {
      color: #000;
      font-family: Inter;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
`;
