import styled, { css } from "styled-components";

export const Logo = styled.div`
  display: flex;
  cursor: pointer;
  & > svg {
    ${(props) =>
      props.theme.name === "iterpec"
        ? css`
            path {
              fill: #000000;
            }
          `
        : null}

    width: 120px;
    height: auto;
  }
`;
export const Wrapper = styled.nav`
  z-index: 16;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 77px;
  background: ${(props) => props.theme.palette.neutral.white};
  & > div {
    max-width: 1200px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
`;

export const List = styled.ul`
  display: flex;
  list-style: none; // Remove o caractere de lista
  padding: 0;
  margin: 0;
  gap: 20px; // Espaçamento entre os itens
  li {
    cursor: pointer; // Adiciona um cursor de ponteiro para itens clicáveis
    color: #000;
    text-align: center;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    display: flex;
    justify-content: center;
    align-items: center;
    & > span.flex {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 4px;
      > img {
        width: 30px;
      }
    }
    &:hover {
      color: #007bff; // Adiciona uma cor de destaque ao passar o mouse
    }
  }
`;
