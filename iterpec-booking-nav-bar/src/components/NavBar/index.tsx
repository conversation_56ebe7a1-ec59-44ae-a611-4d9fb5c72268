import * as Styles from "./styles";
import { Logo, Avatar, Modal } from "@iterpecdev/design-sistem";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next"; // Importar useTranslation
import {
  getRegion,
  getUser,
  Hooks,
  //@ts-ignore
} from "@iterpec/booking-utility";
import { useCallback, useState } from "react";
import Settings from "../Settings";
export const NavBar = () => {
  const navigate = useNavigate();
  const { t } = useTranslation(); // Usar o hook para traduções
  const [open, setOpen] = useState(false);
  const user = getUser();
  const { currency, language, countryCode } = getRegion();

  const handleLogoClick = useCallback(() => {
    if (user?.idUser) navigate("/");
  }, [user, navigate]);
  const useTheme = Hooks.useTheme();

  const bandeiraPais = useCallback(
    (codigo: string) => {
      return `https://d2kv8mz6z68z3e.cloudfront.net/dist/icons-system/static/${codigo}.svg`;
    },
    [countryCode, currency, language]
  );
  const handleModal = () => setOpen(!open);

  return (
    <>
      <Styles.Wrapper>
        <div>
          <Styles.Logo onClick={() => handleLogoClick()}>
            <Logo
              //@ts-ignore
              name={useTheme}
            />
          </Styles.Logo>
          <div>
            <Styles.List>
              <li onClick={handleModal}>
                <span className="flex">
                  <img src={bandeiraPais(language?.toLowerCase().split("-")[1])} />
                  {" · " + currency}
                </span>
              </li>
              <li
                onClick={() => {
                  navigate("/terms");
                }}
              >
                <span>Termos</span>
              </li>
              <li>
                <span
                  onClick={() => {
                    navigate("/my-bookings");
                  }}
                >
                  Minhas reservas
                </span>
              </li>
              <li>
                <Avatar size="md" label={`${user?.name} ${user?.surname} `} />
              </li>
            </Styles.List>
          </div>
        </div>
      </Styles.Wrapper>
      {open && (
        <Modal title={t("settings.title")} isOpen={open} onClose={handleModal}>
          <Settings onDemiss={handleModal} />
        </Modal>
      )}
    </>
  );
};
