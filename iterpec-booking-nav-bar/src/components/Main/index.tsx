import React, { useEffect, useState } from "react";
import { Drawer, Provider } from "@iterpecdev/design-sistem";
import {
  Types,
  getHeader,
  Hooks,
  //@ts-ignore
} from "@iterpec/booking-utility";

import Header from "../Header";
import Menu from "../Menu";
import Settings from "../Settings";
import { NavBar } from "../NavBar";
import { useLocation } from "react-router-dom";

function App() {
  const storageHeader = getHeader?.();
  const [header, setHeader] = useState<Types["IHeader"]>(storageHeader);
  const location = useLocation();

  enum CONTENT {
    "Menu",
    "Settings",
  }
  const { useMobileDetect } = Hooks;
  const isMobile = useMobileDetect().isMobile(); // Estado inicial

  const [open, setOpen] = useState(false);
  const [content, setContent] = useState<CONTENT>(CONTENT["Menu"]);

  useEffect(() => {
    window.addEventListener(
      "headerChange",
      (customEvent: CustomEvent<Types["IHeader"]>) => {
        const { detail } = customEvent || {};
        setHeader(detail);
      }
    );
  }, []);

  const resetMenu = () => {
    closeMenu();
    setTimeout(() => {
      setContent(CONTENT["Menu"]);
    }, 500);
  };

  const handleMenu = () => {
    closeMenu();
    setTimeout(() => {
      setContent(CONTENT["Settings"]);
      setOpen(true);
    }, 500);
  };

  function handleDrawer() {
    setOpen((st) => !st);
  }
  function closeMenu() {
    setOpen(false);
  }

  return (
    <>
      {!isMobile && location.pathname !== "/login" ? <NavBar /> : null}
      <Header {...header} onClick={handleDrawer} />
      {content === 0 ? (
        <div>
          <Drawer
            position={content === 0 ? "left" : "bottom"}
            open={open}
            onDismiss={content === 0 ? closeMenu : resetMenu}
          >
            <Menu onDismiss={closeMenu} handleMenu={handleMenu} />
          </Drawer>
        </div>
      ) : (
        <Drawer position={"bottom"} open={open} onDismiss={resetMenu}>
          <Settings onDemiss={closeMenu} setContent={setContent} />
        </Drawer>
      )}
    </>
  );
}

export default App;
