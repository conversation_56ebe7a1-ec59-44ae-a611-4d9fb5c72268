import styled from "styled-components";

export const WrapperDateInput = styled.div<{ expandedDate: boolean }>`
  display: flex;
  flex-wrap: wrap;
  gap: 8px 0px;
  position: relative;

  ${(props) =>
    props.theme.breakpoints.mobile(`
      min-width: 300px;
      & div.rdrCalendarWrapper {
        position: relative;
      }
    `)}
  ${(props) =>
    props.theme.breakpoints.desktop(`
      min-width: 250px;
      & div.rdrCalendarWrapper {
        background-color: rgba(0, 0, 0, 0);
        font-size: 10px;
      }
    `)}
 
  &:focus-within,
  & *:focus:focus-within:active:target:hover:focus-visible,
  & > * > * > * use:hover use:active use:focus {
    div.rdrMonthAndYearWrapper {
      display: flex;
      background-color: ${(props) => props.theme.palette.neutral.white};
      ${(props) =>
        props.theme.breakpoints.mobile(`
      `)}
      background-color:  none;
      position: absolute;
      z-index: 900;
      width: max-content;
      left: 50%;
      transform: translateX(-50%);
      top: 105%;
      ${(props) =>
        props.theme.breakpoints.mobile(`
          width: 100%;
        `)}
    }
    div.rdrMonths {
      background-color: ${(props) => props.theme.palette.neutral.white};
      ${(props) =>
        props.theme.breakpoints.mobile(`
          width: 100%;
        `)}
      ${(props) =>
        props.theme.breakpoints.desktop(`
          width: max-content;
        `)}
      display: flex;
      z-index: 901;
      position: absolute;
      top: calc(115% + 40px);
      left: 50%;
      transform: translateX(-50%);
    }
  }
  div.rdrMonthAndYearWrapper {
    display: ${(props) => (props.expandedDate ? "flex" : "none")};
  }
  div.rdrMonth {
  }
  div.rdrDateDisplay {
    margin: 0;
    gap: 1%;
    background-color: ${(props) => props.theme.palette.neutral.white};
  }
  label {
    ${(props) =>
      props.theme.breakpoints.mobile(`
       
    `)}
  }
  div.rdrMonths {
    display: ${(props) => (props.expandedDate ? "flex" : "none")};
    justify-content: center;
    align-items: center;
  }
  div.rdrDateDisplayWrapper {
    width: 100%;
    ${(props) =>
      props.theme.breakpoints.desktop(`
        background-color: rgba(0, 0, 0, 0);
    `)}
    & > .rdrDateDisplay {
      ${(props) =>
        props.theme.breakpoints.desktop(`
        display: none;
    `)}
      & span {
        margin: 0 -9px;
        box-sizing: content-box;
        border-radius: 4px;
        background-color: transparent;
        box-shadow: none;
        border: none;
        width: 48%;
      }
      & input {
        background-color: ${(props) => props.theme.palette.neutral.white};
        width: 55%;
        box-sizing: content-box;
        border-radius: 4px;
        border: 1px solid rgb(167, 168, 170) !important;
        padding: 12px 41px 12px 16px;
        height: 22px;
        font-family: Inter, sans-serif;
        font-weight: 400;
        font-size: 16px;
        line-height: 150%;
        color: rgb(64, 64, 64) !important;
        transition: color 300ms ease 0s;
      }
    }
  }
  & > div.rdrDateRangeWrapper {
    width: max-content;
  }
  ${(props) =>
    props.theme.breakpoints.mobile(`
        & > div:not(.rdrDateRangeWrapper) {
          display: none;
        }
  `)}
`;
export const WrapperRooms = styled.div`
  ${(props) =>
    props.theme.breakpoints.desktop(`
      position: relative;
      & > hr {
        display: none;
        }
    `)}

  &:focus-within,
  & *:focus:focus-within:active:target:hover:focus-visible,
  & > * > * > * use:hover use:active use:focus {
    & > div {
      display: block;
      ${(props) =>
        props.theme.breakpoints.desktop(`
          display: flex;
          max-height: 46vh;
          overflow-y: auto;
        `)}
    }
  }
`;

export const WrapperRoom = styled.div`
  ${(props) =>
    props.theme.breakpoints.desktop(`
      display: none;
      position: absolute;
      background-color: ${props.theme.palette.neutral.white};
      z-index: 30;
 
      left: 50%;
      transform: translateX(-50%);
      padding: 10px;
      box-shadow: ${props.theme.shadows.full};
      border-radius: ${props.theme.borderRadius.xs};
      flex-direction: column;
      gap: 10px;

    `)}
  & > div.wrapperTitle {
    width: 100%;
    min-height: 24px;
    font-family: Inter;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    ${(props) =>
      props.theme.breakpoints.mobile(`
        margin: 10px 0px;
      `)}
    & > button {
      padding: 0;
      height: fit-content;
    }
    & > div > button {
      width: max-content;
      height: max-content;
      box-shadow: none;
    }
    & > svg {
      width: 24px;
      height: 24px;
      fill: rgb(64, 64, 64);
    }
  }
  & > div.wrapperInputs {
    display: flex;
    gap: 4%;
    flex-wrap: wrap;

    & > div.wrapperAge {
      width: 100%;
      display: flex;
      gap: 4%;
      flex-wrap: wrap;
      & select {
        box-sizing: content-box;
      }
      & > div {
        ${(props) =>
          props.theme.breakpoints.mobile(`
           width: 48%;
        `)}
      }
    }
    & > div.wrapper {
      width: 48%;
      display: flex;
      align-items: center;
      gap: 10px;
      position: relative;
      & > article {
        width: 100%;
        input {
          border: none;
          text-align: center;
          &:focus-visible {
            outline: none;
          }
        }
      }

      & > div {
        z-index: 80;
        &:first-child {
          left: 10px;
        }
        &:last-child {
          right: 10px;
        }
        position: absolute;
        top: 30%;
        & > button {
          margin-top: 1rem;
          width: 20px;
          height: 20px;
        }
      }
    }
  }
  ${(props) =>
    props.theme.breakpoints.desktop(`
    
    `)}
`;
export const WrapperSearch = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  ${(props) =>
    props.theme.breakpoints.desktop(`
   flex-direction: row;

    `)}
  ${(props) =>
    props.theme.breakpoints.mobile(`
      overflow: hidden;
      height: 95%;
  `)}
  font-family: Inter;
  width: 100%;
  div.check {
    margin: 0 !important;
  }
  div.WrapperAddRoom {
    display: flex;
    justify-content: flex-end;

    > button {
      padding: 0;
      height: fit-content;
      padding: 4px 7px;
      width: max-content;
    }
  }
  & > div.wrapperButton {
    width: 100%;
    display: flex;
    gap: 10px;
    justify-content: center;
    align-content: center;
    flex-direction: column;
    ${(props) =>
      props.theme.breakpoints.desktop(`
      display: flex;
      align-items: flex-start;
      width: auto;
      justify-content: flex-start;
      padding-top: 14px;
    `)}
    > button {
      width: 100%;
      display: flex;
      justify-content: center;
      align-content: center;
    }
  }
  & > div.wrapperFlex {
    width: 100%;
    ${(props) =>
      props.theme.breakpoints.mobile(`
      overflow: hidden;
      height: 100%;
      
      `)}

    overflow: auto;
    max-height: 92%;
    display: flex;
    flex-direction: column;
    ${(props) =>
      props.theme.breakpoints.desktop(`
        flex-direction: row;
        overflow: visible;
        width: 100%;
        height: 100%;
        & > hr {
          display: none;
        }
        & > div {
          height: 120%;
        } 
    `)}
    gap: 10px;

    & > div.wrapperDatepiker > div > div {
      * {
        font-family: Inter;
        font-weight: 400;
        font-size: 16px;
      }
      display: flex;
      gap: 10px;
      z-index: 300;
      & > label {
        border: 1px solid rgb(167, 168, 170);
        border-radius: 4px;
      }
      & > div > svg {
        display: none;
      }
    }

    & > div.check {
      min-width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 11px 0;
      ${(props) =>
        props.theme.breakpoints.desktop(`
          display: none;
    `)}
    }

    & > div:not(.check) {
      label {
        font-family: Inter, sans-serif;
        font-weight: 500;
        font-size: 16px;
        line-height: 100%;
        color: rgb(64, 64, 64);
        width: 100%;
        transition: color 300ms;
        ${(props) =>
          props.theme.breakpoints.desktop(`
              color: ${props.theme.palette.neutral.white};
        `)}
      }
    }
    & > div:not(.check):first-child {
      margin-top: 0;
      ${(props) =>
        props.theme.breakpoints.mobile(`
          width: 100%;
        `)}
    }
    & diV.rooms {
      label {
        ${(props) =>
          props.theme.breakpoints.desktop(`
            color: rgb(64, 64, 64);
            text-align: center;
        `)}
      }
    }
  }
`;

export const WrapperItemAutoComplete = styled.div`
  padding: 12px 41px 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  gap: 6px;
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  transition: background-color 200ms ease-in-out;
  & > div > :first-child {
    font-weight: 600;
  }
  & > svg {
    min-width: 26px;
    width: 26px;
    height: 26px;
    fill: #404040;
  }
`;
export const WrapperAutoComplete = styled.div`
  font-family: Inter;
  width: 50%;
  position: relative;

  & > div.autoComplete {
    background-color: #fff;
    width: 100%;
    overflow-x: auto;
    position: absolute;
    left: 0;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0.5ms, opacity 0.5ms;
    z-index: 99;
    height: auto; // Comportamento para desktop

    ${(props) =>
      props.theme.breakpoints.desktop(`
        max-height: 300px; // Limita a altura no desktop
        transition: visibility 0.2s, opacity 0.2s; // Ajuste na transição para suavizar
        overflow-y: auto; // Adiciona rolagem vertical no desktop
    `)}
    ${(props) =>
      props.theme.breakpoints.mobile(`
        height: 98vh; // Comportamento para mobile
    `)}
  }

  & ${WrapperItemAutoComplete}:focus {
    display: block;

    ${(props) =>
      props.theme.breakpoints.desktop(`
        display: flex; // Mantém visível ao focar no desktop
    `)}
  }

  & ${WrapperItemAutoComplete}:hover {
    ${(props) =>
      props.theme.breakpoints.desktop(`
        display: flex; // Mantém visível ao passar o mouse no desktop
    `)}
  }

  &:focus-within,
  & *:focus:focus-within:active:target:hover:focus-visible,
  & > * > * > * use:hover use:active use:focus {
    & > div.autoComplete {
      display: block;
      opacity: 1;
      visibility: visible;

      ${(props) =>
        props.theme.breakpoints.desktop(`
          display: block; // Exibe o autocomplete no desktop
          opacity: 1;
          visibility: visible;
          box-shadow: ${props.theme.shadows.full};
          border-radius: ${props.theme.borderRadius.xs};
        `)}
    }
  }
`;
