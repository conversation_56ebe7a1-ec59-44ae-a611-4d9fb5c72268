import {
  <PERSON><PERSON>,
  <PERSON>,
  Checkbox,
  Divider,
  IconButton,
  Input,
  Select,
} from "@iterpecdev/design-sistem";
import * as Styles from "./styles";
import React, {
  Fragment,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  Types,
  setSearch,
  Hooks,
  getSearch,
  getRegion,
  getUser,
  // @ts-ignore
} from "@iterpec/booking-utility";
import Api from "../../services";
import { IAutoComplete, ListErrors } from "../../services/types";
import Icon, { IconProps } from "@iterpecdev/icons-system";
import { useNavigate } from "react-router-dom";
import "react-date-range/dist/styles.css"; // main css file
import "react-date-range/dist/theme/default.css"; // theme css file
import { DateRange } from "react-date-range";

import * as localeObject from "date-fns/locale";
import * as Yup from "yup";
import moment from "moment";
import { DateRangePicker } from "../DateRangePicker";
import { useTranslation } from "react-i18next";
const TODAY = () => {
  let date = new Date();
  const offset = date.getTimezoneOffset();
  date = new Date(date.getTime() - offset * 60 * 1000);
  return date.toISOString().split("T")[0];
};

const IconAutoComplete = ({ destiny }) => {
  const { type }: { [x: string]: string } = destiny;
  const ICONSAUTOCOMPETE: { [x: string]: IconProps["name"] } = {
    BUS_STATION: "BusStop",
    AIRPORT: "AirplaneLanding",
    METRO_STATION: "SubwayVariant",
    CITY: "MapMarkerOutline",
    PROVINCE_STATE: "MapLegend",
    MULTI_CITY_VICINITY: "MapLegend",
    "MULTI-CITY (VICINITY)": "MapLegend",
    HIGH_LEVEL_REGION: "MapLegend",
    NEIGHBORHOOD: "MapMarkerPath",
    POINT_OF_INTEREST: "MapMarkerPath",
    "POINT OF INTEREST SHADOW": "MapMarkerPath",
    TRAIN_STATION: "SubwayVariant",
    HOTEL: "Bed",
    PROPERTY: "Bed",
  };
  return <Icon name={ICONSAUTOCOMPETE[type.toUpperCase()]} />;
};

const Search: React.FC<{ handleMenu: () => void }> = ({ handleMenu }) => {
  const DEFAULT_DATE = {
    CHECKIN: moment().add("60", "d").toDate(),
    CHECKOUT: moment().add("65", "d").toDate(),
  };
  const isMobile = Hooks.useMobileDetect().isMobile();

  const DEFAULT_ROOM = {
    toWork: false,
    quantityRooms: 1,
    checkIn: DEFAULT_DATE.CHECKIN,
    checkOut: DEFAULT_DATE.CHECKOUT,
    rooms: [
      { id: 1, adults: 1, children: 0, childrenAge: [{ age: 0, id: 1 }] },
    ],
  } as Types["ISearch"];

  const storageSearch: Types["ISearch"] = getSearch?.() ?? DEFAULT_ROOM;

  const [inputSearch, setInputSearch] = useState<Types["ISearch"]>({
    ...storageSearch,
  });
  const api = new Api();
  const languageCode = getRegion()?.languageCode;

  const [output, setOutput] = useState<Types["ISearch"]>(null);
  const [expandedDate, setExpandedDate] = useState<boolean>(false);
  const [errors, setErrors] = useState<ListErrors>([]);
  const [autocomplete, setAutocomplete] = useState<IAutoComplete[]>(null);
  const navigate = useNavigate();
  const { t } = useTranslation(); // Usar o hook para traduções

  const validateRooms = (rooms: Types["ISearch"]["rooms"]) => {
    const roomsValidate = rooms.map((room) => {});
  };
  const sendRequestSearch = () => {
    const customEvent = new CustomEvent("searchEvent");
    window.dispatchEvent(customEvent);
  };

  const validateForm = async () => {
    try {
      const schema = Yup.object().shape({
        destiny: Yup.string().required("Campo obligatorio"),
        destiny_id: Yup.string(),
        hotel_id: Yup.string(),
        checkIn: Yup.date()
          .required("Campo obligatorio")
          .typeError("Expected a value of type ${type} but got: ${value}"),
        checkOut: Yup.date()
          .required("Campo obligatorio")
          .typeError("Expected a value of type ${type} but got: ${value}"),
        quantityRooms: Yup.number().required("Campo obligatorio"),
        toWork: Yup.boolean().required("Campo obligatorio"),
        rooms: Yup.array().of(
          Yup.object().shape({
            id: Yup.number().required("Campo obligatorio"),
            adults: Yup.number().required("Campo obligatorio"),
            children: Yup.number().required("Campo obligatorio"),
            childrenAge: Yup.array().of(
              Yup.object().shape({
                id: Yup.number().required("Campo obligatorio"),
                age: Yup.number().required("Campo obligatorio"),
              })
            ),
          })
        ),
      });
      await schema.validate(inputSearch, { abortEarly: false });
      setErrors([]);
      handleMenu();
      navigate("search");
      setTimeout(() => {
        sendRequestSearch();
      }, 1000);
    } catch (error: any) {
      if (error instanceof Yup.ValidationError) {
        console.log({ ...error });
        let tmp = error.inner.map((erro) => {
          return {
            description: erro.message,
            inputName: erro.path ? erro.path : "",
          };
        });
        setErrors(tmp);
      }
    }
  };

  const setStateInput = (value, name: string) => {
    setInputSearch({ ...inputSearch, [name]: value });
    setSearch({ ...inputSearch, [name]: value });
  };

  const setDestiny = (
    value: string | IAutoComplete,
    name: "destiny" | "hotel"
  ) => {
    if (typeof value === "string") {
      const { destiny_id = null, hotel_id = null, ...rest } = output;
      const destiny = {
        ...rest,
        destiny: value,
      };
      setAutocomplete([]);
      setInputSearch(destiny);
      setSearch(destiny);
      return;
    }
    const key = {
      destiny: {
        name: "destiny_id",
        id: value?.id,
        type: value?.type,
      },
      hotel: {
        name: "hotel_id",
        id: value?.id,
        type: value?.type,
      },
    };
    const { destiny_id, hotel_id, ...rest } = inputSearch;

    const destiny = {
      ...rest,
      destiny: `${value?.name}${
        value?.localization ? ", " + value?.localization : ""
      }`,
      [key[name].name]: key[name].id,
      type: key[name].type,
    };
    setInputSearch(destiny);
    setSearch(destiny);
  };
  const addPerson = (type: "adults" | "children", roomNumber: number) => {
    let rooms = inputSearch.rooms.filter((room) => roomNumber !== room.id);
    let newRoom = inputSearch.rooms.find((room) => roomNumber === room.id);
    newRoom = {
      ...newRoom,
      [type]: newRoom[type] ? newRoom[type] + 1 : 1,
    };

    setStateInput([...rooms, newRoom], "rooms");
  };
  const addAgeChildren = (roomNumber: number, age: number, id: number) => {
    let rooms = inputSearch.rooms.filter((room) => roomNumber !== room.id);
    let newRoom = inputSearch.rooms.find((room) => roomNumber === room.id);
    let oldArrayChildrenAge =
      newRoom?.childrenAge?.filter((children) => children.id !== id) ?? [];
    newRoom = {
      ...newRoom,
      childrenAge: [...oldArrayChildrenAge, { id, age }],
    };
    setStateInput([...rooms, newRoom], "rooms");
  };
  const removePerson = (type: "adults" | "children", roomNumber: number) => {
    let rooms = inputSearch.rooms.filter((room) => roomNumber !== room.id);
    let newRoom = inputSearch.rooms.find((room) => roomNumber === room.id);

    if (type === "adults" && newRoom.adults === 1) {
      return;
    }
    if (type === "children") {
      newRoom = {
        ...newRoom,
        childrenAge:
          newRoom?.childrenAge?.length > 1
            ? newRoom.childrenAge.splice(0, -1)
            : [],
      };
    }

    newRoom = {
      ...newRoom,
      [type]: newRoom[type] ? newRoom[type] - 1 : 0,
    };
    setStateInput([...rooms, newRoom], "rooms");
  };
  const updateRooms = (quantity: number) => {
    let rooms = [...inputSearch.rooms];
    if (quantity < rooms.length) {
      setInputSearch({
        ...inputSearch,
        quantityRooms: Number(quantity),
        rooms: rooms.filter((room) => room.id <= quantity),
      });
      setSearch({
        ...inputSearch,
        quantityRooms: Number(quantity),
        rooms: rooms.filter((room) => room.id <= quantity),
      });
      return;
    }
    for (let i = 1; i <= quantity; i++) {
      if (i > inputSearch.rooms.length) {
        const NEW_ROOM = {
          id: i,
          adults: 1,
          children: 0,
        };
        rooms.push(NEW_ROOM);
      }
    }
    const search = {
      ...inputSearch,
      quantityRooms: Number(quantity),
      rooms,
    };
    setInputSearch(search);
    setSearch(search);
  };

  const setValueDestiny = async (destiny: IAutoComplete) => {
    setDestiny(destiny, "destiny");
  };

  const getAutoComplete = async (value: string) => {
    if (!output?.destiny) {
      return;
    }
    enum PRIORITY {
      "CITY",
      "City",
      "Multi-City (Vicinity)",
      "MULTI_CITY_VICINITY",
      "HOTEL",
      "Hotel",
      "AIRPORT",
      "Airport",
      "POINT_OF_INTEREST",
      "Point of Interest Shadow",
      "Neighborhood",
      "NEIGHBORHOOD",
    }
    try {
      const { data, config } = await api.autoComplete(value);
      // cidades = 3, aredores = 2, hotel = 10, pontos = 3  , aerportos = 3 , bairros =10.........
      const temp = (data.length ? [...data] : []).sort((itemA, itemB) => {
        try {
          if (itemA.type in PRIORITY && itemB.type in PRIORITY)
            return PRIORITY[itemA.type] - PRIORITY[itemB.type];
        } catch (error) {
          console.log(error);
          return 20;
        }

        if (itemA.type in PRIORITY) return -1;
        if (itemB.type in PRIORITY) return 20;

        return 20;
      });
      const term: { mainTerm: string } = JSON.parse(config.data);
      if (term.mainTerm === inputSearch?.destiny) setAutocomplete(temp);
    } catch (error) {
      setAutocomplete([]);
      console.log(error);
    }
  };

  useEffect(() => {
    if (!inputSearch?.destiny?.length) {
      setAutocomplete([]);
    }
  }, [inputSearch]);
  useEffect(() => {
    if (storageSearch.destiny) {
      setInputSearch((old) => ({
        ...old,
        destiny: storageSearch.destiny,
      }));
    }
  }, [storageSearch.destiny]);

  useEffect(() => {
    getAutoComplete(output?.destiny);
  }, [output?.destiny]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setOutput(inputSearch);
    }, 100);
    return () => {
      clearTimeout(timeout);
    };
  }, [inputSearch]);
  const selectionRange = {
    startDate: new Date(inputSearch?.checkIn),
    endDate: new Date(inputSearch?.checkOut),
    key: "selection",
  };
  function handleSelect(ranges) {
    setInputSearch({
      ...inputSearch,
      ...{
        checkOut: new Date(moment(ranges[0]?.endDate).toISOString()),
        checkIn: new Date(moment(ranges[0]?.startDate).toISOString()),
      },
    });
    setSearch({
      ...inputSearch,
      ...{
        checkOut: new Date(moment(ranges[0]?.endDate).toISOString()),
        checkIn: new Date(moment(ranges[0]?.startDate).toISOString()),
      },
    });
  }

  // Função para remover um quarto pelo id
  const removeRoomById = (roomId: number) => {
    // Remove o quarto e renumera os ids sequencialmente
    const filteredRooms = inputSearch.rooms.filter((r) => r.id !== roomId);
    const newRooms = filteredRooms.map((room, idx) => ({
      ...room,
      id: idx + 1,
    }));
    setInputSearch({
      ...inputSearch,
      quantityRooms: newRooms.length,
      rooms: newRooms,
    });
    setSearch({
      ...inputSearch,
      quantityRooms: newRooms.length,
      rooms: newRooms,
    });
  };

  return (
    <Styles.WrapperSearch>
      <div className="wrapperFlex">
        <Styles.WrapperAutoComplete>
          <Input
            placeholder={t("search.destinationPlaceholder")}
            label={t("search.destination")}
            type="search"
            value={inputSearch?.destiny}
            name="destiny"
            autoComplete="off"
            helper={
              errors.filter((error) => error.inputName === "destiny").length
                ? "error"
                : undefined
            }
            helperText={String(
              errors
                .filter((error) => error.inputName === "destiny")
                .map((error) => error.description)
            )}
            submitSearch={() => getAutoComplete(inputSearch?.destiny)}
            onChangeValue={(e: string) => {
              setDestiny(e, "destiny");
            }}
          />
          <div className="autoComplete">
            {!!autocomplete &&
              autocomplete?.map((destiny, index) => (
                <Fragment key={index}>
                  <Styles.WrapperItemAutoComplete
                    key={index}
                    onClick={() => {
                      setValueDestiny(destiny);
                    }}
                  >
                    <IconAutoComplete destiny={destiny} />

                    <div>
                      <p>{destiny.name}</p>
                      <p>
                        {destiny?.localization
                          ? `${destiny?.localization} `
                          : ""}
                      </p>
                    </div>
                  </Styles.WrapperItemAutoComplete>
                  <Divider />
                </Fragment>
              ))}
          </div>
        </Styles.WrapperAutoComplete>
        <Styles.WrapperDateInput
          expandedDate={expandedDate}
          onFocus={() => setExpandedDate(true)}
          onBlur={() => setExpandedDate(false)}
        >
          <DateRangePicker
            ranges={[selectionRange]}
            onChange={handleSelect}
            locale={localeObject[languageCode]}
            minDate={moment(TODAY()).toDate()}
            dateDisplayFormat="dd/MM/yyyy"
            //@ts-ignore
            tabindex="0"
            showMonthAndYearPickers={true}
            editableDateInputs={true}
          />
        </Styles.WrapperDateInput>

        <Styles.WrapperRooms>
          {!isMobile && (
            <Input
              label="Viajantes"
              value={`${inputSearch?.rooms?.reduce(
                (acc, room) => acc + (room.adults || 0) + (room.children || 0),
                0
              )} Viajantes, ${inputSearch?.rooms.length} habitação`}
              readOnly
            />
          )}
          <Divider />
          <Styles.WrapperRoom className="rooms">
            {inputSearch?.rooms
              ?.sort((a, b) => a.id - b.id)
              ?.map((room, index) => (
                <>
                  <div className="wrapperTitle">
                    <span>
                      {t("search.room")} {room.id}
                    </span>{" "}
                    {/* <Icon name="SettingTrashClose" /> */}
                    {inputSearch?.rooms.length > 1 ? (
                      <IconButton
                        icon="TrashCanOutline"
                        variant="secondary"
                        onClick={() => removeRoomById(room.id)}
                      />
                    ) : null}
                  </div>
                  <div className="wrapperInputs">
                    <div className="wrapper">
                      <IconButton
                        icon="Minus"
                        variant="secondary"
                        onClick={() => removePerson("adults", room.id)}
                      />
                      <article>
                        <Input
                          label={t("search.adult")}
                          type="number"
                          key={room.id}
                          value={
                            inputSearch?.rooms?.find((rm) => rm.id === room.id)
                              ?.adults
                          }
                        />
                      </article>
                      <IconButton
                        icon="Plus"
                        variant="secondary"
                        onClick={() => addPerson("adults", room.id)}
                        disabled={room.adults === 14}
                      />
                    </div>
                    <div className="wrapper">
                      <IconButton
                        icon="Minus"
                        variant="secondary"
                        onClick={() => removePerson("children", room.id)}
                      />
                      <article>
                        <Input
                          label={t("search.children")}
                          key={room.id}
                          value={
                            inputSearch?.rooms?.find((rm) => rm.id === room.id)
                              ?.children
                          }
                          type="number"
                        />
                      </article>
                      <IconButton
                        icon="Plus"
                        variant="secondary"
                        onClick={() => addPerson("children", room.id)}
                        disabled={room.children === 6}
                      />
                    </div>
                    <div className="wrapperAge">
                      {Array.from(Array(room.children).keys()).map(
                        (children, index) => (
                          <Select
                            key={children}
                            label={`${t("search.childrenAge")} ${index + 1}`}
                            /* label={`Edad del niño ${children + 1}`} */
                            defaultValue={
                              room?.childrenAge?.length >= children
                                ? room?.childrenAge[children]?.age
                                : 0
                            }
                            onChangeValue={(age) =>
                              addAgeChildren(room.id, age, children + 1)
                            }
                          >
                            <option value={0}>Menos de 1</option>
                            {Array.from(Array(18).keys())
                              .filter((number) => number)
                              .map((option) => (
                                <option key={option} value={option}>
                                  {option} anos
                                </option>
                              ))}
                          </Select>
                        )
                      )}
                    </div>
                  </div>
                  <Divider />
                </>
              ))}
            <div className="WrapperAddRoom wrapperTitle">
              <Button
                onClick={() => {
                  updateRooms(Number(inputSearch?.quantityRooms) + 1);
                }}
                variant="secondary"
              >
                {t("search.addRoom")}
                {/*    Agregar otra habitación */}
              </Button>
            </div>
          </Styles.WrapperRoom>
        </Styles.WrapperRooms>
        <Checkbox
          className="check"
          onChangeValue={(e: boolean) => setStateInput(e, "toWork")}
          checked={inputSearch?.toWork}
        >
          {t("search.toWork")}
        </Checkbox>
      </div>
      <div className="wrapperButton">
        <Button
          onClick={() => {
            validateForm();
          }}
        >
          {t("search.find")}
        </Button>
      </div>
    </Styles.WrapperSearch>
  );
};
export default Search;
