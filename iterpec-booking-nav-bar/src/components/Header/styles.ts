import { is } from "date-fns/locale";
import styled, { css } from "styled-components";

interface headerProps {
  $show: boolean;
  $showFallback?: boolean;
  $pathname?: string;
  $isMobile?: boolean;
  $showSearch?: boolean;
}

// Exemplo de uso de tema para cor do logo
export const Logo = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  & > div > svg,
  & > div > img {
    width: 107px;
    height: auto;
    ${({ theme }) =>
      theme.name === "iterpec"
        ? css`
            & > path,
            path {
              fill: ${theme.palette.neutral.white};
            }
          `
        : null}
  }
`;

export const WrapperSearch = styled.div`
  width: 100%;
  max-width: 1200px;
  ${({ theme }) =>
    theme.breakpoints.desktop(`
      display: flex;
      height: 100%;
      align-items: center;
    `)}
  ${({ theme }) =>
    theme.breakpoints.mobile(`
      padding: 16px;
      background-color: ${theme.palette.neutral.darkest};
    `)}
  & > div {
    gap: 0;
  }
  & > button {
    ${({ theme }) =>
      theme.breakpoints.mobile(`
        display: none;
      `)}
  }
  & > div.WrapperDate {
    background-color: #fff;
    height: 40px;
    ${({ theme }) =>
      theme.breakpoints.desktop(`
        display: none;
        & > input {
          display: none;
        }
      `)}
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 4px;
    gap: 6px;
    & > div {
      display: flex;
      align-items: center;
      gap: 2px;
      > svg {
        width: 20px;
        height: 20px;
        & > path {
          fill: rgba(60, 102, 114, 1);
        }
      }
    }
    & > div.WrapperCalendar {
      ${({ theme }) =>
        theme.breakpoints.desktop(`
          display: none;
        `)}
    }
    & > div.WrapperGuests {
      & > svg {
        width: 27px;
        height: auto;
        & > path {
          fill: rgba(60, 102, 114, 1);
        }
      }
    }
    & span {
      color: #3c6672;
      font-family: Inter;
      font-size: 75%;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }
`;

export const WrapperMenu = styled.div<headerProps>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  ${({ theme, $pathname }) =>
    $pathname !== "/login" ? theme.breakpoints.desktop(``) : null}
  & > svg {
    width: 26px;
    ${({ theme, $show, $showFallback }) => css`
      height: ${$show && !$showFallback ? "0" : "auto"};
    `}
    & > path, > g {
      ${({ theme }) =>
        theme.name === "iterpec"
          ? css`
              fill: ${theme.palette.neutral.white};
            `
          : null}
    }
  }
  & > svg.button {
    height: auto;
  }
  ${(props) =>
    props.theme.breakpoints.mobile(`
     justify-content: center;
     align-items: center; 
     padding: 16px;         
    `)}
`;

export const Space = styled.div<headerProps>`
  ${({ theme, $show: showSearch }) => css`
    height: ${showSearch ? "194px" : "100px"};
    ${theme.breakpoints.desktop(`
      height: ${showSearch ? "275px" : "77px"};
    `)}
  `};
`;

export const Container = styled.header<headerProps>`
  ${({ theme, $show, $pathname }) =>
    $show && $pathname !== "/login"
      ? theme.breakpoints.desktop(`
          top: 77px;
        `)
      : null}
  ${({ theme, $show }) =>
    !$show
      ? theme.breakpoints.desktop(`
          display: none;
        `)
      : null}

  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 15;
  // Estilos globais do header
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }
  ${({ theme, $show, $pathname, $showSearch, $isMobile }) => {
    return css`
      height: ${$pathname === "/login"
        ? "100px"
        : $showSearch && $show
        ? "194px"
        : "100px"};
      /*  padding: ${$show ? theme.spacings.inset.md : "22px 16px"}; */
      background: ${theme.name === "iterpec" ||
      (!$isMobile && $pathname !== "/login")
        ? theme.palette.neutral.darkest
        : theme.palette.neutral.white};
      /* gap: ${$show ? "16px" : "0"}; */
      box-shadow: ${theme.shadows.lower};
      display: flex;
      flex-direction: column;
      justify-content: ${$show ? "center" : "end"};
      align-items: center;
      ${!$show &&
      css`
        & > div.drawer {
          display: none;
        }
      `}
    `;
  }}
  ${({ theme, $show }) =>
    theme.breakpoints.mobile(
      `justify-content: ${$show ? "center" : "flex-end"} !important;`
    )}
`;

export const Link = styled.div`
  ${({ theme }) => css`
    font-family: ${theme.fonts.base};
    color: ${theme.palette.primary.main};
    font-size: ${theme.fontSizes.xs};
    font-weight: ${theme.fontWeights.medium};
    line-height: ${theme.lineHeights.md};
    text-decoration-line: ${theme.underline};
    display: flex;
    align-items: center;
    flex: none;
    order: 1;
    flex-grow: 0;
  `}
`;

export const Title = styled.h3`
  ${({ theme }) => css`
    font-family: ${theme.fonts.base};
    color: ${theme.palette.neutral.darkest};
    font-size: ${theme.fontSizes.sm};
    font-weight: ${theme.fontWeights.bold};
    line-height: ${theme.lineHeights.default};
    margin: 0;
    width: 100%;
  `}
`;

export const Icon = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  & > svg {
    width: 24px;
    height: auto;
    & path {
      fill: ${({ theme }) => theme.palette.neutral.darkest};
    }
  }
`;
