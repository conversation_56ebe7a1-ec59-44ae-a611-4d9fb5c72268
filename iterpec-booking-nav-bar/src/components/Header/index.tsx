import React, { useEffect, useState, useMemo, useCallback } from "react";
import * as Styles from "./styles";
import { Drawer, Input, Logo, Button } from "@iterpecdev/design-sistem";
import Search from "../Search";
import {
  getSearch,
  Types as ITypes,
  getUser,
  Hooks,
  //@ts-ignore
} from "@iterpec/booking-utility";
import moment from "moment";
import { useLocation, useNavigate } from "react-router-dom";
import Icon from "@iterpecdev/icons-system";
import { useTranslation } from "react-i18next"; // Importar useTranslation

export const Header: React.FC<ITypes["IHeader"]> = React.memo(
  ({ onClick, ...header }) => {
    const { t } = useTranslation(); // Usar o hook para traduções
    const [open, setOpen] = useState(false);
    const inMemorySearch = getSearch();
    const location = useLocation();
    const userData = getUser();
    const navigate = useNavigate();
    const { useMobileDetect } = Hooks;
    const isMobile = useMobileDetect().isMobile();
    const path = useMemo(() => location.pathname, [location.pathname]);

    const handleMenu = useCallback(() => setOpen((prev) => !prev), []);
    const closeMenu = useCallback(() => setOpen(false), []);
    const handleLogoClick = useCallback(() => {
      if (userData?.idUser) navigate("/");
    }, [userData, navigate]);

    const somarItens = useCallback((array: number[]) => {
      return array?.reduce((soma, item) => soma + item, 0);
    }, []);
    const useTheme = Hooks.useTheme();

    return (
      <>
        <Styles.Container
          {...header}
          $pathname={path}
          $show={header.$show}
          $showSearch={header.$showSearch}
          $isMobile={isMobile}
        >
          <Styles.WrapperMenu
            $show={header.$showSearch}
            $showFallback={header.$showFallback}
            $pathname={path}
          >
            {header.$showFallback && isMobile ? (
              <Icon name="ArrowLeft" onClick={() => header?.$fallback?.()} />
            ) : (
              <svg height={2} />
            )}
            <Styles.Logo>
              {isMobile || path === "/login" ? (
                <div onClick={handleLogoClick}>
                  <Logo
                    //@ts-ignore
                    name={useTheme}
                  />
                </div>
              ) : null}
            </Styles.Logo>
            {header.$showMenu && isMobile ? (
              <Icon className="button" name="Menu" onClick={onClick} />
            ) : (
              <svg height={2} />
            )}
          </Styles.WrapperMenu>

          {header.$showSearch ? (
            <Styles.WrapperSearch>
              {isMobile ? (
                <>
                  <Input
                    placeholder={t("header.destinationPlaceholder")}
                    type="inMemorySearch"
                    onClick={handleMenu}
                    value={inMemorySearch?.destiny}
                    readOnly
                  />
                  <div className="WrapperDate" onClick={handleMenu}>
                    <div className="WrapperCalendar">
                      <Icon name="Calendar" />
                      <span>
                        {inMemorySearch?.checkIn
                          ? moment(inMemorySearch?.checkIn).format("DD/MM/YY")
                          : null}{" "}
                        -{" "}
                        {inMemorySearch?.checkOut
                          ? moment(inMemorySearch?.checkOut).format("DD/MM/YY")
                          : null}
                      </span>
                    </div>
                    <div className="WrapperGuests">
                      {!!inMemorySearch?.rooms?.length ? (
                        <span>
                          {somarItens(
                            inMemorySearch?.rooms?.map(
                              (room) => room?.adults + room?.children
                            )
                          ) > 1 ? (
                            <>
                              {somarItens(
                                inMemorySearch?.rooms?.map(
                                  (room) => room?.adults + room?.children
                                )
                              )}{" "}
                              {t("header.people")}
                            </>
                          ) : (
                            <>{t("header.singlePerson")}</>
                          )}
                          ,
                        </span>
                      ) : null}

                      <span>
                        {inMemorySearch?.rooms?.length ?? 1}{" "}
                        {inMemorySearch?.rooms?.length > 1
                          ? t("header.rooms")
                          : t("header.room")}
                      </span>
                    </div>
                  </div>
                </>
              ) : (
                <Search handleMenu={closeMenu} />
              )}
            </Styles.WrapperSearch>
          ) : null}
          {isMobile && (
            <div className="drawer">
              <Drawer position="left" open={open} onDismiss={handleMenu}>
                <Search handleMenu={handleMenu} />
              </Drawer>
            </div>
          )}
        </Styles.Container>
        <Styles.Space
          {...header}
          $show={header.$showSearch}
          $isMobile={isMobile}
        />
      </>
    );
  }
);

export default Header;
