import { DateRange } from "react-date-range";
import { useTranslation } from "react-i18next";
import { useRef, useState, useEffect } from "react";
import { Input } from "@iterpecdev/design-sistem";
import moment from "moment";
import {
  Hooks,
  // @ts-ignore
} from "@iterpec/booking-utility";
export const DateRangePicker = ({ ...props }) => {
  const { t } = useTranslation();
  const dateRangeRef = useRef(null); // Referência para o componente DateRange
  const [state, setState] = useState(props.ranges);
  const isMobile = Hooks.useMobileDetect().isMobile();

  useEffect(() => {
    props?.onChange({
      ...state,
    });
  }, [state]);
  return (
    <>
      {isMobile ? (
        <label>{t("dateRangePicker.label")}</label>
      ) : (
        <Input
          label={t("search.dates")}
          value={`${moment(state[0].startDate).format("DD/MM/YYYY")} - ${moment(
            state[0].endDate
          ).format("DD/MM/YYYY")}`}
          name="checkIn"
          readOnly
        />
      )}

      <DateRange
        editableDateInputs={true}
        onChange={(item) =>
          setState([
            {
              startDate: item.selection?.startDate,
              endDate: item.selection?.endDate,
              key: "selection",
            },
          ])
        }
        locale={props?.locale}
        minDate={props?.minDate}
        //@ts-ignore
        tabindex="0"
        dateDisplayFormat="dd/MM/yyyy"
        moveRangeOnFirstSelection={false}
        ranges={state}
      />
    </>
  );
};
