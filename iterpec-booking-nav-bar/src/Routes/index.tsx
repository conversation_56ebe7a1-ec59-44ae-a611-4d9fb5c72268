import React, { useEffect } from "react";
import { Routes as Switch, Route, useNavigate } from "react-router-dom";
import Search from "../components/Search";
import Main from "../components/Main";
//@ts-ignore
import { Hooks } from "@iterpec/booking-utility";

export const Routes = () => {
  const { useAuth } = Hooks;
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isAuthenticated && window.location.pathname !== "/login") {
      navigate("/login");
    }
  }, [isAuthenticated]);
  return (
    <Switch>
      <Route path="/*" element={<Main />} />
    </Switch>
  );
};
