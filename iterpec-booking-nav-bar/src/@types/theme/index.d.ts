import { Types } from "@iterpecdev/theme-system";
import { DefaultTheme as DefaultThemeStyledComponents } from "styled-components";

declare module "styled-components" {
  export interface DefaultTheme extends DefaultThemeStyledComponents {
    name: string;
    underline: string;
    palette: Types.Palette;
    breakpoints: Types.Breakpoint;
    fontSizes: Types.FontSize;
    fonts: Types.Font;
    fontWeights: Types.FontWeight;
    lineHeights: Types.LineHeight;
    letterSpacings: Types.LetterSpacing;
    borderWidths: Types.BorderWidth;
    borderRadius: Types.BorderRadius;
    radii: Types.Radii;
    shadows: Types.Shadow;
    zIndices: Types.ZIndice;
    opacities: Types.Opacity;
    transitions: Types.Transition;
    spacings: Types.Spacings;
  }
}
